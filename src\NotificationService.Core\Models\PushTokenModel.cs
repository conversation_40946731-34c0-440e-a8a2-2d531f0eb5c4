﻿using NotificationService.Core.Constants;
using System.ComponentModel.DataAnnotations;

namespace NotificationService.Core.Models
{
    public class PushTokenModel
    {
        [Required]
        public Application Application { get; set; }

        [Required]
        public string Token { get; set; } = default!;

        [Required]
        public string userId { get; set; } = default!;

        public string Subdomain { get; set; } = "api";

        public Platform Platform { get; set; } = Platform.Andriod;

        public IOSPushType? IOSPushType { get; set; }
    }
}
