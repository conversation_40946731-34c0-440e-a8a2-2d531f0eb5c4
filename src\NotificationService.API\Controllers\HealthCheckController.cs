﻿using Microsoft.AspNetCore.Mvc;
using NotificationService.Core.Models;

namespace NotificationService.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class HealthCheckController : ControllerBase
    {
        public HealthCheckController()
        {
            
        }

        #region Get all values of environment variables
        /// <summary>
        /// This gets all set environment vaiables
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("get-all-env-variables")]
        public IActionResult GetEnvironmentVariables(ViewEnvVariablesDto request)
        {
            var envKey = Environment.GetEnvironmentVariable("JOBPRO_ENVIRONMENT_VARIABLE_KEY");
            var username = Environment.GetEnvironmentVariable("JOBPRO_ENVIRONMENT_VARIABLE_USERNAME");
            var password = Environment.GetEnvironmentVariable("JOBPRO_ENVIRONMENT_VARIABLE_PASSWORD");

            //if (request.Key != envKey)
            //{
            //    return BadRequest(new ApiResponse<string>
            //    {
            //        Status = 400,
            //        Message = "Invalid Key",
            //        Data = null
            //    });
            //}

            //// Check that the username and password match
            //if (username != request.Username || password != request.Password)
            //{
            //    return Unauthorized(new ApiResponse<string>
            //    {
            //        Status = 401,
            //        Message = "Wrong username or password",
            //        Data = null
            //    });
            //}

            var databaseSource = Environment.GetEnvironmentVariable("JOBPRO_DATA_SOURCE");
            var database = Environment.GetEnvironmentVariable("JOBPRO_DATABASE");
            var databaseUsername = Environment.GetEnvironmentVariable("JOBPRO_DATABASE_USERNAME");
            var databasePassword = Environment.GetEnvironmentVariable("JOBPRO_DATABASE_PASSWORD");
            var twilioAccountSID = Environment.GetEnvironmentVariable("JOBPRO_TWILIO_ACCOUNT_SID");
            var twilioAuthToken = Environment.GetEnvironmentVariable("JOBPRO_TWILIO_AUTH_TOKEN");
            var elasticCloudId = Environment.GetEnvironmentVariable("JOBPRO_ELASTIC_CLOUD_ID");
            var elasticUsername = Environment.GetEnvironmentVariable("JOBPRO_ELASTIC_USERNAME");
            var elasticPassword = Environment.GetEnvironmentVariable("JOBPRO_ELASTIC_PASSWORD");
            var elasticUri = Environment.GetEnvironmentVariable("JOBPRO_ELASTIC_URI");
            var hangfireUsername = Environment.GetEnvironmentVariable("JOBPRO_HANGFIRE_USERNAME");
            var hangfirePassword = Environment.GetEnvironmentVariable("JOBPRO_HANGFIRE_PASSWORD");
            var rabbitMQUrl = Environment.GetEnvironmentVariable("JOBPRO_RABBITMQ_BROKER_URL");
            var rabbitMQPort = Environment.GetEnvironmentVariable("JOBPRO_RABBITMQ_BROKER_PORT");
            var rabbitMQUsername = Environment.GetEnvironmentVariable("JOBPRO_RABBITMQ_BROKER_USERNAME");
            var rabbitMQPassword = Environment.GetEnvironmentVariable("JOBPRO_RABBITMQ_BROKER_PASSWORD");
            var mollieTestApiKey = Environment.GetEnvironmentVariable("JOBPRO_MOLLIE_API_KEY_TEST");
            var mollieLiveApiKey = Environment.GetEnvironmentVariable("JOBPRO_MOLLIE_API_KEY_LIVE");
            var stripeTestPubKey = Environment.GetEnvironmentVariable("JOBPRO_STRIPE_PUB_KEY_TEST");
            var stripeTestSecretKey = Environment.GetEnvironmentVariable("JOBPRO_STRIPE_SECRET_KEY_TEST");
            var stripeTestWebHookSecretKey = Environment.GetEnvironmentVariable("JOBPRO_STRIPE_WEBHOOK_SECRET_TEST");
            var stripeLivePubKey = Environment.GetEnvironmentVariable("JOBPRO_STRIPE_PUB_KEY_LIVE");
            var stripeLiveSecretKey = Environment.GetEnvironmentVariable("JOBPRO_STRIPE_SECRET_KEY_LIVE");
            var stripeLiveWebHookSecretKey = Environment.GetEnvironmentVariable("JOBPRO_STRIPE_WEBHOOK_SECRET_LIVE");
            var watchDogUsername = Environment.GetEnvironmentVariable("JOBPRO_WATCHDOG_USERNAME");
            var watcgDogPassword = Environment.GetEnvironmentVariable("JOBPRO_WATCHDOG_PASSWORD");
            var redisHost = Environment.GetEnvironmentVariable("JOBPRO_REDIS_LOCATION");
            var mailgunApiKey = Environment.GetEnvironmentVariable("JOBPRO_MAILGUN_APIKEY");
            var mailgunDomain = Environment.GetEnvironmentVariable("JOBPRO_MAILGUN_DOMAIN");
            var jwtKey = Environment.GetEnvironmentVariable("JOBPRO_JWT_SHARED_SECRET");
            var connectionString = Environment.GetEnvironmentVariable("JOBPRO_CONNECTIONSTRING");
            var environment = Environment.GetEnvironmentVariable("JOBPRO_ENVIRONMENT");

            return Ok(new
            {
                databaseSource,
                database,
                databaseUsername,
                databasePassword,
                twilioAccountSID,
                twilioAuthToken,
                elasticCloudId,
                elasticUsername,
                elasticPassword,
                elasticUri,
                hangfireUsername,
                hangfirePassword,
                rabbitMQUrl,
                rabbitMQPort,
                rabbitMQUsername,
                rabbitMQPassword,
                mollieTestApiKey,
                mollieLiveApiKey,
                stripeTestPubKey,
                stripeTestSecretKey,
                stripeTestWebHookSecretKey,
                stripeLivePubKey,
                stripeLiveSecretKey,
                stripeLiveWebHookSecretKey,
                watchDogUsername,
                watcgDogPassword,
                redisHost,
                mailgunApiKey,
                mailgunDomain,
                jwtKey,
                connectionString,
                username,
                password,
                envKey,
                environment
            });
        }
        #endregion
    }
}
