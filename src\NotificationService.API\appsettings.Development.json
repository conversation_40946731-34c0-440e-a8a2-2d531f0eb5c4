{
  "ConnectionStrings": {
    //"NotificationConnection": "Data Source=DESKTOP-EONO2PO\\SQLEXPRESS;Initial Catalog=Notification_Service;Integrated Security=True;TrustServerCertificate=True",
    "Connection": "Data Source=*************;Database=Notification_service_qa;User Id=test;Password=***********************;MultipleActiveResultSets=false;Integrated Security=false;TrustServerCertificate=true",
    "NotificationConnection": "Host=localhost;Port=5432;Database=notification;Username=postgres;Password=*****;Include Error Detail=true"
  },

  "CorsOrigins": [ "http://localhost:4200" ],
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning"
    }
  },

  "JwtSettings": {
    "validAudience": "http://localhost",
    "securityKey": "staging!###!!!!=--=-90-&%^][ghg0gvtftf1!!@@%556872%$#$#$%@$}^^^^secretkey!98765",
    "PublicKey": "19df230f6259c9d4cd0b0a24aeb3d40b624f9e3ddf7b70384471c774916a42b9",
    "APIKey": "f268c65e63630f75783482346cb60048e940a0161e33376070d5f641a29b7e8b",
    "SecretKey": "staging!###!!!!=--=-90-&%^][ghg0gvtftf1!!@@%556872%$#$#$%@$}^^^^secretkey!98765",
    "expiryInMinutes": 600,
    "RefreshTokenExpiryInDays": 7,
    "validIssuer": "EfacRuhuna"
  },

  "WatchDogCredentials": {
    "Username": "*****",
    "Password": "*****"
  },

  "RabbitMQConfiguration": {
    "Host": "amqps://rmq.pactocoin.com",
    "Port": "5672",
    "Username": "user",
    "Password": "N91zFLPqoYSASqd9"
  },

  "KafkaConfiguration": {
    "BootstrapServers": "localhost:9092",
    "GroupId": "JOBPRO",
    "SaslUsername": "dozie",
    "SaslPassword": "password",
    "SslCaLocation": "C:\\Users\\<USER>\\Downloads\\kafka.truststore.jks"
  },

  "Topics": {
    "Notification": "notification-message-queue"
  },

  "ServiceSettings": {
    "Name": "Notification",
    "Version": "1.0.0",
    "Description": "JobPro Notification Service",
    "Id": "NOTIFICATION_SERVICE"
  },

  "AllowedHosts": "*",
  "AMQP": {
    "HostName": "amqps://xruwodqv:<EMAIL>/xruwodqv"
  },

  "LogPath": "./logs/apilogs-.log",
  "Serilog": {
    "Using": [ "Serilog.Sinks.File" ],
    "MinimunLevel": {
      "Default": "Information"
    },
    "WriteTo": [
      {
        "Name": "File",
        "Args": {
          "path": "./logs/api-logs-.log",
          "rollingInterval": "Day"
        }
      }
    ]
  },

  "AIT": {
    "ApiKey": "975e2ee80236fee5b6382972cdfa26343df7bc206e729024cd5e888f90796e3a",
    "Username": "zarttech__jobpro"
  },

  "GCSConfigOptions": {
    "AuthFile": "C:\\applications\\zarttech\\secrets\\google-credentials.json",
    "BucketName": "jobpro-assets"
  },

  "FCM": {
    "ProjectId": "jobpro-prod",
    "SendUrl": "https://fcm.googleapis.com/v1/projects/",
    "ServerApiKey": "***********",
    "SenderId": "jobpro",
    "VapidKey": "BCCqDNrGR1F65lk18aqUETSByrES_0jjOg5nd7lxzRGwqokOy3ibm5qAYyXNjzJ42Wnm4gA06uEhQy81vcFZEJI",
    "AppLogo": "https://avatars.githubusercontent.com/u/********?s=280&v=4",
    "ApnsBundleId": "com.jobpro.joble",
    "ApnsVoipTopic": "come.jobpro.joble.voip"
  },

  "APNs": {
    "KeyId": "43UA2HCTC8",
    "TeamId": "UDVM22XGUG",
    "IsProduction": false
  },

  "Twilio": {
    "AccountSID": "**********************************",
    "AuthToken": "134e2aa7e61fdde8d4644a2b17a17213"
  },

  "MAILGUN_API_KEY": "**************************************************",
  "MAILGUN_DOMAIN": "jobpro.app",

  "MailgunApiKey": "",
  "MailgunDomain": "jobpro.app",
  "EmailOptions": {
    "Url": "https://api.mailgun.net/v3/",
    "ApiKey": "",
    "FromName": "JobPro",
    "FromEmail": "<EMAIL>",
    "Domain": "jobpro.app"
  },


  "Messaging": {
    "UseAWS": "false" // or "false" for RabbitMQ
  },

  "Endpoints": {
    "NotificationService": {
      "BaseUrl": "https://api.pactocoin.com/notify",
      "SendNotification": "api/Notification/send"
    }
  }
}
