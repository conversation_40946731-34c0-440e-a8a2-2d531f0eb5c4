﻿using Amazon.SimpleNotificationService;
using Amazon.SimpleNotificationService.Model;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using NotificationService.Core.Services;
using RabbitMQ.Client;
using RabbitMQ.Client.Exceptions;
using System.Net;
using System.Text;


namespace NotificationService.Infrastructure.Services
{
    public class BaseProducer : IAMQProducer
    {
        private readonly string _hostName;
        private readonly ILogger<NotificationMessageConsumer> _logger;
        private readonly IAmazonSimpleNotificationService _sns;
        private readonly bool _useAWS;

        public BaseProducer(
            IConfiguration configuration,
            ILogger<NotificationMessageConsumer> logger
            //IAmazonSimpleNotificationService sns
            )
        {
            _hostName = Environment.GetEnvironmentVariable("JOBPRO_RABBITMQ_BROKER_URL")
                ?? configuration["AMQP:HostName"]
                ?? throw new ArgumentNullException("RabbitMQ host configuration is missing");
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            //_sns = sns ?? throw new ArgumentNullException(nameof(sns));
            _useAWS = bool.Parse(configuration["Messaging:UseAWS"] ?? "false");
        }

        public async Task<bool> PublishAsync<TMessage>(TMessage message, string destination)
            where TMessage : class
        {
            return _useAWS
                ? await PublishAWSAsync(destination, message)
                : await PublishRabbitMQAsync(message, destination);
        }

        private async Task<bool> PublishRabbitMQAsync<TMessage>(TMessage message, string queue)
            where TMessage : class
        {
            try
            {
                var exchange = "notification-message-event";
                var exchangeType = ExchangeType.Direct;
                var factory = new ConnectionFactory
                {
                    Uri = new Uri(_hostName),
                    DispatchConsumersAsync = true
                };

                using var connection = factory.CreateConnection();
                using var channel = connection.CreateModel();

                var args = new Dictionary<string, object>
                {
                    { "x-message-ttl", ********* } // 100 hours
                };

                await Task.Run(() =>
                {
                    channel.QueueDeclare(
                        queue: queue,
                        durable: true,
                        exclusive: false,
                        autoDelete: false,
                        arguments: args);

                    channel.ExchangeDeclare(exchange, exchangeType);
                    channel.QueueBind(queue, exchange, routingKey: "");

                    var json = JsonConvert.SerializeObject(message);
                    var body = Encoding.UTF8.GetBytes(json);

                    channel.BasicPublish(
                        exchange: exchange,
                        routingKey: "",
                        basicProperties: null,
                        body: body);
                });

                _logger.LogInformation("Successfully published message to RabbitMQ queue: {queue}", queue);
                return true;
            }
            catch (OperationInterruptedException ex)
            {
                _logger.LogError(ex, "RabbitMQ configuration error for queue: {queue}. Contact backend team for assistance", queue);
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to publish message to RabbitMQ queue: {queue}", queue);
                return false;
            }
        }

        private async Task<bool> PublishAWSAsync<TMessage>(string topic, TMessage message)
            where TMessage : class
        {
            try
            {
                // Create topic if it doesn't exist
                var createTopicResponse = await _sns.CreateTopicAsync(topic);
                if (createTopicResponse.HttpStatusCode != HttpStatusCode.OK)
                {
                    _logger.LogError("Failed to create/verify SNS topic: {topic}", topic);
                    return false;
                }

                var topicArn = createTopicResponse.TopicArn;
                var request = new PublishRequest
                {
                    TopicArn = topicArn,
                    Message = JsonConvert.SerializeObject(message),
                    MessageAttributes = new Dictionary<string, MessageAttributeValue>
                    {
                        {
                            "MessageType",
                            new MessageAttributeValue
                            {
                                DataType = "String",
                                StringValue = typeof(TMessage).Name
                            }
                        }
                    }
                };

                var response = await _sns.PublishAsync(request);
                if (response.HttpStatusCode != HttpStatusCode.OK)
                {
                    _logger.LogError("Failed to publish message to SNS topic: {topic}", topic);
                    return false;
                }

                _logger.LogInformation("Successfully published message to SNS topic: {topic}, MessageId: {messageId}",
                    topic, response.MessageId);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to publish message to SNS topic: {topic}", topic);
                return false;
            }
        }
    }
}