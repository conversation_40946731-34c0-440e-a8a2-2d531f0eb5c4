﻿using Amazon.SQS.Model;
using AutoMapper;
using Common.Services.Dtos;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Newtonsoft.Json;
using NotificationService.Core.Models;
using NotificationService.Core.Services;
using NotificationService.Infrastructure.Services;
using NotificationService.Infrastructure.Services.AWS;
using Serilog;
using WatchDog;
using ILogger = Serilog.ILogger;

namespace Jobpro.Subscription.Service.EventProviders.AWS.Consumers
{
    public class NotificationEventConsumer : BackgroundService
    {
        #region Constructor and Properties
        private readonly IServiceScopeFactory _serviceScopeFactory;
        private readonly IMapper _mapper;
        private readonly IEventProcessingSetUp _eventProSetup;
        private readonly ILogger _logger = Log.ForContext<NotificationEventConsumer>();
        private readonly Topics _topics;

        public NotificationEventConsumer(IServiceScopeFactory serviceScopeFactory,
            IConfiguration configuration,
            IMapper mapper)
        {
            _serviceScopeFactory = serviceScopeFactory;
            _mapper = mapper;

            // Get the generic consumer from the service scope factory
            using var scope = serviceScopeFactory.CreateScope();
            _eventProSetup = scope.ServiceProvider.GetRequiredService<IEventProcessingSetUp>();

            _topics = new Topics();
            configuration.GetSection("Topics").Bind(_topics);
        }
        #endregion

        #region Start Consumer
        protected async virtual Task StartConsumer(CancellationToken cancellationToken)
        {
            while (!cancellationToken.IsCancellationRequested)
            {
                (ReceiveMessageResponse? messageResponse, GetQueueUrlResponse? queue) =
                    await _eventProSetup.CreateQueueAndNotificationAsync(_topics.Notification, cancellationToken);
                await ProcessMessage(queue, messageResponse, cancellationToken);
            }
        }
        #endregion

        #region Process the message
        /// <summary>
        /// Process the message
        /// </summary>
        /// <param name="queue"></param>
        /// <param name="messageResponse"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        private async Task ProcessMessage(GetQueueUrlResponse queue, ReceiveMessageResponse messageResponse, CancellationToken cancellationToken)
        {
            if (messageResponse == null)
                return;

            foreach (var message in messageResponse.Messages)
            {
                var snsMessage = System.Text.Json.JsonSerializer.Deserialize<SnsMessage>(message.Body)!;
                var response = await ProcessNotificationMessage(snsMessage.Message);

                if (queue != null && snsMessage is not null)
                {
                    if (response)
                        await _eventProSetup.DeleteMessage(queue, message, cancellationToken);
                }
            }
        }
        #endregion

        #region Process Notification Message
        private async Task<bool> ProcessNotificationMessage(string message)
        {
            try
            {
                _logger.Information("Notification Message {0}", message);
                WatchLogger.Log("Notification Message", message);

                using (var scope = _serviceScopeFactory.CreateScope())
                {
                    var notificationService = scope.ServiceProvider.GetRequiredService<INotificationService>();
                    var payload = JsonConvert.DeserializeObject<NotificationMessageModel>(message);
                    var response = await notificationService.ProcessMessage(payload);

                    if (!response)
                    {
                        _logger.Error("Error processing notification message");
                        WatchLogger.Log("Error processing notification message", message);
                        return false;
                    }

                    return true;
                }
            }
            catch (Exception ex)
            {
                _logger.Error($"Error processing notification message {ex.Message}", ex);
                WatchLogger.Log($"Error processing notification message - {ex.Message}",
                    ex.ToString(),
                    nameof(NotificationMessageConsumer));

                return false;
            }
        }
        #endregion

        #region Execute and Stop Methods
        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            await Task.Run(() => StartConsumer(stoppingToken));
        }

        public override Task StopAsync(CancellationToken cancellationToken)
        {
            return base.StopAsync(cancellationToken);
        }
        #endregion
    }
}