﻿using AutoMapper;
using Common.Services.Events.Notification;
using MassTransit;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using NotificationService.Core.Models;
using NotificationService.Core.Services;
using NotificationService.Infrastructure.Services;
using WatchDog;

namespace NotificationService.Infrastructure.MassTransit
{
    public class NotificationConsumer : IConsumer<NotificationEvent>
    {
        private readonly string? _topic;
        private readonly ILogger<NotificationMessageConsumer> _logger;
        private readonly INotificationService _notificationService;
        private readonly IMapper _mapper;

        public NotificationConsumer(
            IConfiguration configuration, 
            ILogger<NotificationMessageConsumer> logger, 
            INotificationService notificationService,
            IMapper mapper)
        {
            _topic = configuration.GetValue<string>("KafkaTopic:NotificationEvent");
            _logger = logger;
            _notificationService = notificationService;
            _mapper = mapper;
        }

        public Task Consume(ConsumeContext<NotificationEvent> context)
        {
            ProcessMessage(context.Message);
            return Task.CompletedTask;
        }

        public void ProcessMessage(NotificationEvent message)
        {
            try
            {
                _logger.LogInformation("Notification Message {0}", message);
                WatchLogger.Log("Notification Message", JsonConvert.SerializeObject(message));

                var payload = _mapper.Map<NotificationMessageModel>(message);
                _notificationService.ProcessMessage(payload);
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error processing notification message {ex.Message}", ex);
                WatchLogger.Log($"Error processing notification message - {ex.Message}", ex.ToString(), nameof(NotificationMessageConsumer));
            }
        }
    }
}
