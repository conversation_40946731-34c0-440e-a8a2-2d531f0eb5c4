
12:43:19 [Error] :: An error occurred using the connection to database '"Notification"' on server '"tcp://localhost:5432"'.

12:43:19 [Error] :: An error occurred using the connection to database '"Notification"' on server '"tcp://localhost:5432"'.

12:55:56 [Error] :: An error occurred using the connection to database '"Notification"' on server '"tcp://localhost:5432"'.

12:55:57 [Information] :: User profile is available. Using '"C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys"' as key repository and Windows DPAPI to encrypt keys at rest.

12:55:57 [Information] :: Now listening on: "https://localhost:7044"

12:55:57 [Information] :: Now listening on: "http://localhost:5179"

12:55:57 [Information] :: Application started. Press Ctrl+C to shut down.

12:55:57 [Information] :: Hosting environment: "Development"

12:55:57 [Information] :: Content root path: "C:\Users\<USER>\source\repos\Notification-Service\src\NotificationService.API"

12:55:59 [Information] :: Request starting "HTTP/2" "GET" "https"://"localhost:7044""""/swagger""" - null null

12:55:59 [Information] :: Request finished "HTTP/2" "GET" "https"://"localhost:7044""""/swagger""" - 301 0 null 302.4556ms

12:55:59 [Information] :: Request starting "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/index.html""" - null null

12:55:59 [Information] :: Request finished "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/index.html""" - 200 null "text/html;charset=utf-8" 216.4701ms

12:55:59 [Information] :: Request starting "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/swagger-ui.css""" - null null

12:55:59 [Information] :: Request starting "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/swagger-ui-bundle.js""" - null null

12:55:59 [Information] :: Request starting "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/swagger-ui-standalone-preset.js""" - null null

12:55:59 [Information] :: Sending file. Request path: '"/swagger-ui.css"'. Physical path: '"N/A"'

12:55:59 [Information] :: Request finished "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/swagger-ui.css""" - 200 143943 "text/css" 86.9345ms

12:55:59 [Information] :: Sending file. Request path: '"/swagger-ui-standalone-preset.js"'. Physical path: '"N/A"'

12:56:00 [Information] :: Request starting "HTTP/2" "GET" "https"://"localhost:7044""""/_vs/browserLink""" - null null

12:56:00 [Information] :: Request finished "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/swagger-ui-standalone-preset.js""" - 200 339486 "text/javascript" 119.4737ms

12:56:00 [Information] :: Request starting "HTTP/2" "GET" "https"://"localhost:7044""""/_framework/aspnetcore-browser-refresh.js""" - null null

12:56:00 [Information] :: Sending file. Request path: '"/swagger-ui-bundle.js"'. Physical path: '"N/A"'

12:56:00 [Information] :: Request finished "HTTP/2" "GET" "https"://"localhost:7044""""/_framework/aspnetcore-browser-refresh.js""" - 200 13782 "application/javascript; charset=utf-8" 79.9501ms

12:56:00 [Information] :: Request finished "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/swagger-ui-bundle.js""" - 200 1096145 "text/javascript" 190.4835ms

12:56:00 [Information] :: Request finished "HTTP/2" "GET" "https"://"localhost:7044""""/_vs/browserLink""" - 200 null "text/javascript; charset=UTF-8" 715.0117ms

12:56:01 [Information] :: Request starting "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/v1/swagger.json""" - null null

12:56:01 [Information] :: Request starting "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/favicon-32x32.png""" - null null

12:56:01 [Information] :: Sending file. Request path: '"/favicon-32x32.png"'. Physical path: '"N/A"'

12:56:01 [Information] :: Request finished "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/favicon-32x32.png""" - 200 628 "image/png" 98.9515ms

12:56:01 [Information] :: Request finished "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/v1/swagger.json""" - 200 null "application/json;charset=utf-8" 341.3738ms

13:01:35 [Error] :: Failed executing DbCommand ("33"ms) [Parameters=[""], CommandType='Text', CommandTimeout='30']"
""SELECT \"MigrationId\", \"ProductVersion\"
FROM \"__EFMigrationsHistory\"
ORDER BY \"MigrationId\";"

13:01:37 [Error] :: Failed executing DbCommand ("10"ms) [Parameters=[""], CommandType='Text', CommandTimeout='30']"
""SELECT \"MigrationId\", \"ProductVersion\"
FROM \"__EFMigrationsHistory\"
ORDER BY \"MigrationId\";"

13:01:37 [Information] :: Applying migration '"20250302120031_init"'.

13:01:43 [Information] :: User profile is available. Using '"C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys"' as key repository and Windows DPAPI to encrypt keys at rest.

13:01:43 [Information] :: Now listening on: "https://localhost:7044"

13:01:43 [Information] :: Now listening on: "http://localhost:5179"

13:01:43 [Information] :: Application started. Press Ctrl+C to shut down.

13:01:43 [Information] :: Hosting environment: "Development"

13:01:43 [Information] :: Content root path: "C:\Users\<USER>\source\repos\Notification-Service\src\NotificationService.API"

13:01:44 [Information] :: Request starting "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/index.html""" - null null

13:01:45 [Information] :: Request finished "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/index.html""" - 200 null "text/html;charset=utf-8" 338.1131ms

13:01:45 [Information] :: Request starting "HTTP/2" "GET" "https"://"localhost:7044""""/_framework/aspnetcore-browser-refresh.js""" - null null

13:01:45 [Information] :: Request finished "HTTP/2" "GET" "https"://"localhost:7044""""/_framework/aspnetcore-browser-refresh.js""" - 200 13782 "application/javascript; charset=utf-8" 34.682ms

13:01:45 [Information] :: Request starting "HTTP/2" "GET" "https"://"localhost:7044""""/_vs/browserLink""" - null null

13:01:45 [Information] :: Request finished "HTTP/2" "GET" "https"://"localhost:7044""""/_vs/browserLink""" - 200 null "text/javascript; charset=UTF-8" 63.813ms

13:01:46 [Information] :: Request starting "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/v1/swagger.json""" - null null

13:01:46 [Information] :: Request finished "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/v1/swagger.json""" - 200 null "application/json;charset=utf-8" 314.1462ms
