﻿using Common.Services.Events.Auth;
using MassTransit;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using NotificationService.Core.Models;
using NotificationService.Core.Services;

// For more information on enabling Web API for empty projects, visit https://go.microsoft.com/fwlink/?LinkID=397860

namespace NotificationService.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class NotificationController : ControllerBase
    {
        private readonly IAMQProducer _aMQProducer;
        private readonly INotificationService _notificationService;
        private readonly Topics _topics;

        public NotificationController(IAMQProducer aMQProducer, INotificationService notificationService, IConfiguration configuration)
        {
            _aMQProducer = aMQProducer;
            _notificationService = notificationService;

            _topics = new Topics();
            configuration.GetSection("Topics").Bind(_topics);
        }

        // POST api/<NotificationController>
        [HttpPost("send")]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<string>))]
        public async Task<IActionResult> Send([FromBody] NotificationMessageModel message)
        {
            var result = await _notificationService.ProcessMessage(message);
            if(result) return Ok("Notification sent successfully");

            return BadRequest("Failed to send Notification");

        }

        [HttpPost("queue")]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<string>))]
        public async Task<IActionResult> Queue([FromBody] NotificationMessageModel message)
        {
            await _aMQProducer.PublishAsync(message, _topics.Notification);
            return Ok("Message queued successfully");
        }
    }
}
