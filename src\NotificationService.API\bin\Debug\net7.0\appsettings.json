{
  "ConnectionStrings": {
    "NotificationConnection": "Server=localhost,14333;Initial Catalog=notification_service;Persist Security Info=False;User ID=sa;Password=***********;MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=True;Connection Timeout=30;"
  },
  "CorsOrigins": [ "http://localhost:4200" ],
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning"
    }
  },
  "JwtSettings": {
    "validAudience": "http://localhost",
    "securityKey": "CodeMazeSecretKey",
    "PublicKey": "19df230f6259c9d4cd0b0a24aeb3d40b624f9e3ddf7b70384471c774916a42b9",
    "APIKey": "f268c65e63630f75783482346cb60048e940a0161e33376070d5f641a29b7e8b",
    "SecretKey": "570e6f300a19dd0e854547671cbd7fa8e98b5564229e7d1ba1ee9234a2ce3074",
    "expiryInMinutes": 600,
    "RefreshTokenExpiryInDays": 7,
    "validIssuer": "EfacRuhuna"
  },
  "AllowedHosts": "*",
  "AMQP": {
    "HostName": "amqps://pmbludyh:<EMAIL>/pmbludyh"
  },
  "LogPath": "./logs/apilogs-.log",
  "Serilog": {
    "Using": [ "Serilog.Sinks.File" ],
    "MinimunLevel": {
      "Default": "Information"
    },
    "WriteTo": [
      {
        "Name": "File",
        "Args": {
          "path": "./logs/api-logs-.log",
          "rollingInterval": "Day"
        }
      }
    ]
  },
  "GCP": {
    "AuthFile": "C:\\applications\\zarttech\\secrets\\",
    "BucketName": "jobpro-notification-templates"
  },
  "FCM": {
    "SendUrl": "https://fcm.googleapis.com/v1/projects/",
    "ServerApiKey": "12038384033",
    "SenderId": "jobpro"
  },
  "MailgunApiKey": "",
  "MailgunDomain": "jobpro.app",
  "EmailOptions": {
    "Url": "https://api.mailgun.net/v3/",
    "ApiKey": "",
    "FromName": "JobPro",
    "FromEmail": "<EMAIL>",
    "Domain": "jobpro.app"
  },
  //"Kestrel": {
  //  "Endpoints": {
  //    "Http": {
  //      "Url": "http://localhost:5000"
  //    },
  //    "Https": {
  //      "Url": "https://localhost:5001"
  //    }
  //  }
  //}
}
