﻿
using Common.Services.Events.Notification;
using Confluent.Kafka;
using MassTransit;
using NotificationService.API.ConfigureModels;
using NotificationService.Core.Models;
using NotificationService.Infrastructure.MassTransit;
using NotificationService.Infrastructure.Repositories;

namespace NotificationService.API.Middlewares
{
    public static class MassTransitServiceExtension
    {
        public static void AddMassTransitConfigForRMQ(this IServiceCollection services, IConfiguration configuration)
        {
            // Get RabbitMQ connection and bind it to RabbitMQConfiguration
            var rabbitMQConfiguration = new RabbitMQConfiguration();
            configuration.GetSection("RabbitMQConfiguration").Bind(rabbitMQConfiguration);

            if (rabbitMQConfiguration == null)
            {
                throw new ArgumentNullException("RabbitMQConfiguration is missing in the configuration.");
            }

            services.AddMassTransit(busConfigurator =>
            {
                busConfigurator.SetKebabCaseEndpointNameFormatter();

                busConfigurator.AddConsumers(typeof(Program).Assembly);

                busConfigurator.UsingRabbitMq((context, cfg) =>
                {
                    cfg.Host(rabbitMQConfiguration.Host, h =>
                    {
                        h.Username(rabbitMQConfiguration.Username);
                        h.Password(rabbitMQConfiguration.Password);
                    });

                    cfg.ConfigureEndpoints(context, new KebabCaseEndpointNameFormatter("Subscription", false));
                });
            });
        }

        public static void AddMassTransitConfigForKafkaAsync(this IServiceCollection services, IConfiguration configuration)
        {
            var connectionString = configuration.GetConnectionString("ConnectionString");
            // Get Kafka connection and bind it to KafkaConfiguration
            var kafkaConfiguration = new KafkaConfiguration();
            configuration.GetSection("KafkaConfiguration").Bind(kafkaConfiguration);

            var topics = new Topics();
            configuration.GetSection("KafkaTopics").Bind(topics);

            var serviceSettings = new ServiceSettings();
            configuration.GetSection("ServiceSettings").Bind(serviceSettings);

            if (kafkaConfiguration == null || topics == null || serviceSettings == null)
            {
                throw new ArgumentNullException("KafkaConfiguration or KafkaTopics or service settings is missing in the configuration.");
            }

            services.AddMassTransit(busConfigurator =>
            {
                busConfigurator.UsingInMemory((context, cfg) => cfg.ConfigureEndpoints(context));
                busConfigurator.SetKebabCaseEndpointNameFormatter();

                busConfigurator.AddRider((rider) =>
                {
                    rider.AddConsumer<NotificationConsumer>();

                    rider.UsingKafka((context, cfg) =>
                    {
                        cfg.Host(kafkaConfiguration.BootstrapServers);

                        cfg.TopicEndpoint<string, NotificationEvent>(topics.Notification, $"{kafkaConfiguration.GroupId}-{serviceSettings.Id}", e =>
                        {
                            e.AutoOffsetReset = AutoOffsetReset.Earliest;
                            e.ConfigureConsumer<NotificationConsumer>(context);
                            e.CreateIfMissing(t =>
                            {
                                t.NumPartitions = 1;
                                t.ReplicationFactor = 1;
                            });

                        });
                    });
                });
            });
        }
    }
}
