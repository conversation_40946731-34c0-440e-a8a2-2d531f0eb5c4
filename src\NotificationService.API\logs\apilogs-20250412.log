
16:38:44 [Information] :: Executed DbCommand ("32"ms) [Parameters=[""], CommandType='Text', CommandTimeout='30']"
""SELECT \"MigrationId\", \"ProductVersion\"
FROM \"__EFMigrationsHistory\"
ORDER BY \"MigrationId\";"

16:38:45 [Information] :: User profile is available. Using '"C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys"' as key repository and Windows DPAPI to encrypt keys at rest.

16:38:45 [Information] :: Now listening on: "https://localhost:7044"

16:38:45 [Information] :: Now listening on: "http://localhost:5179"

16:38:45 [Information] :: Application started. Press Ctrl+C to shut down.

16:38:45 [Information] :: Hosting environment: "Development"

16:38:45 [Information] :: Content root path: "C:\Users\<USER>\source\repos\Notification-Service\src\NotificationService.API"

16:38:46 [Information] :: Request starting "HTTP/2" "GET" "https"://"localhost:7044""""/swagger""" - null null

16:38:47 [Information] :: Request finished "HTTP/2" "GET" "https"://"localhost:7044""""/swagger""" - 301 0 null 277.7285ms

16:38:47 [Information] :: Request starting "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/index.html""" - null null

16:38:47 [Information] :: Request finished "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/index.html""" - 200 null "text/html;charset=utf-8" 212.1767ms

16:38:47 [Information] :: Request starting "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/swagger-ui-bundle.js""" - null null

16:38:47 [Information] :: Request starting "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/swagger-ui.css""" - null null

16:38:47 [Information] :: Request starting "HTTP/2" "GET" "https"://"localhost:7044""""/_framework/aspnetcore-browser-refresh.js""" - null null

16:38:47 [Information] :: Request starting "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/swagger-ui-standalone-preset.js""" - null null

16:38:47 [Information] :: Request finished "HTTP/2" "GET" "https"://"localhost:7044""""/_framework/aspnetcore-browser-refresh.js""" - 200 13782 "application/javascript; charset=utf-8" 63.5994ms

16:38:47 [Information] :: Sending file. Request path: '"/swagger-ui.css"'. Physical path: '"N/A"'

16:38:47 [Information] :: Sending file. Request path: '"/swagger-ui-standalone-preset.js"'. Physical path: '"N/A"'

16:38:47 [Information] :: Request finished "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/swagger-ui.css""" - 200 143943 "text/css" 88.9457ms

16:38:47 [Information] :: Request finished "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/swagger-ui-standalone-preset.js""" - 200 339486 "text/javascript" 97.8015ms

16:38:47 [Information] :: Sending file. Request path: '"/swagger-ui-bundle.js"'. Physical path: '"N/A"'

16:38:47 [Information] :: Request finished "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/swagger-ui-bundle.js""" - 200 1096145 "text/javascript" 125.3451ms

16:38:49 [Information] :: Request starting "HTTP/2" "GET" "https"://"localhost:7044""""/_vs/browserLink""" - null null

16:38:49 [Information] :: Request finished "HTTP/2" "GET" "https"://"localhost:7044""""/_vs/browserLink""" - 200 null "text/javascript; charset=UTF-8" 92.4769ms

16:38:49 [Information] :: Request starting "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/favicon-32x32.png""" - null null

16:38:49 [Information] :: Sending file. Request path: '"/favicon-32x32.png"'. Physical path: '"N/A"'

16:38:49 [Information] :: Request finished "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/favicon-32x32.png""" - 200 628 "image/png" 14.7868ms

16:38:49 [Information] :: Request starting "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/v1/swagger.json""" - null null

16:38:49 [Information] :: Request finished "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/v1/swagger.json""" - 200 null "application/json;charset=utf-8" 172.4864ms
