using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.IdentityModel.Tokens;
using NotificationService.API.Extensions;
using NotificationService.API.Middlewares;
using NotificationService.Core.Services;
using NotificationService.Infrastructure;
using NotificationService.Infrastructure.Repositories;
using NotificationService.Infrastructure.Services;
using Serilog;
using System.Text;
using WatchDog;

var builder = WebApplication.CreateBuilder(args);
string ApiCorsPolicy = "ApiCorsPolicy";
ConfigurationManager configuration = builder.Configuration;
var connectionString = configuration.GetConnectionString("NotificationConnection");

// Learn more about configuring Swagger/OpenAPI at https://aka.ms/aspnetcore/swashbuckle
builder.Services.AddDbContext<NotificationDbContext>(options =>
    options.UseNpgsql(connectionString, o =>
    {
        o.EnableRetryOnFailure();
    }));

// - DO NOT DELETE
builder.Services.AddHostedService(provider =>
{
    using (var scope = provider.CreateScope())
    {
        var service = scope.ServiceProvider.GetService<RabbitMQBrokerService>();
        return service;
    }
});

// Add auto mapper
builder.Services.AddAutoMapper(typeof(NotificationService.Core.Helper.AutoMapper));
builder.Services.AddControllers().AddJsonOptions(options => options.JsonSerializerOptions.PropertyNamingPolicy = null);

// Add kafka mass transit - DO NOT DELETE
//builder.Services.AddMassTransitConfigForKafkaAsync(configuration);

// cors policy
var cors = builder.Configuration.GetSection("CorsOrigins").Get<string[]>();
builder.Services.AddCors(options =>
{
    options.AddPolicy("ApiCorsPolicy", config =>
    {
        config
            .AllowAnyOrigin()
            .AllowAnyMethod()
            .AllowAnyHeader();
    });
});

builder.Services.Configure<ApiBehaviorOptions>(options =>
{
    options.SuppressModelStateInvalidFilter = true;
});

builder.Services.AddHttpClient();
builder.Services.AddCoreServices(configuration);
builder.Services.AddScoped<IApnsService, ApnsService>();
builder.Services.AddAuthentication(options =>
{
    options.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
    options.DefaultScheme = JwtBearerDefaults.AuthenticationScheme;
    options.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
})
   .AddJwtBearer(options =>
    {
        options.SaveToken = true;
        options.RequireHttpsMetadata = false;
        options.TokenValidationParameters = new TokenValidationParameters()
        {
            ValidateIssuer = true,
            ValidateAudience = true,
            ValidAudience = Environment.GetEnvironmentVariable("JOBPRO_JWT_VALID_AUDIENCE") ?? configuration["JwtSettings:validAudience"],
            ValidIssuer = Environment.GetEnvironmentVariable("JOBPRO_JWT_VALID_ISSUER") ?? configuration["JwtSettings:validIssuer"],
            IssuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(Environment.GetEnvironmentVariable("JOBPRO_JWT_SHARED_SECRET") ?? configuration["JwtSettings:securityKey"]))
        };
    });

builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen();

// Add watchdog service
builder.Services.AddWatchDogService();

Serilog.Debugging.SelfLog.Enable(ex => Log.Warning(ex));
builder.Host.UseSerilog((hostingContext, loggerConfiguration) =>
{
    var cfg = loggerConfiguration
        .Enrich.FromLogContext()
        .MinimumLevel.Information()
        .WriteTo.Console(
            outputTemplate:
            "{NewLine}[{Timestamp:HH:mm:ss} {Level:u3}] :: {Message:lj} {Properties:j}{NewLine}{Exception}");
    Boolean.TryParse(configuration["DOTNET_RUNNING_IN_CONTAINER"], out bool inContainer);
    if (!inContainer)
    {
        string? logPath = configuration["LogPath"];
        cfg.WriteTo.File(
            logPath,
            outputTemplate:
            "{NewLine}{Timestamp:HH:mm:ss} [{Level}] :: {Message}{NewLine}{Exception}",
            retainedFileCountLimit: 100,
            fileSizeLimitBytes: 104857600,
            rollOnFileSizeLimit: true,
            rollingInterval: RollingInterval.Day

        );
    }
});


var app = builder.Build();

using (var scope = app.Services.CreateScope())
{
    /// apply pending migration if any
    var context = scope.ServiceProvider.GetRequiredService<NotificationDbContext>();
    if (context.Database.GetPendingMigrations().Any())
    {
        await context.Database.MigrateAsync();
    }
}


app.UseCors(ApiCorsPolicy);

// Configure the HTTP request pipeline.

app.UseSwagger();
app.UseSwaggerUI();

var lf = app.Services.GetRequiredService<ILoggerFactory>();
app.ConfigureExceptionHandler(lf);

//app.UseHttpsRedirection();
app.UseAuthentication();
app.UseAuthorization();
app.UseMiddleware<UserContextMiddleware>();

app.MapControllers();

// Add watchdog service
app.UseWatchDogExceptionLogger();
app.UseWatchDog(opt =>
{
    opt.WatchPageUsername = Environment.GetEnvironmentVariable("JOBPRO_WATCHDOG_USERNAME") ?? configuration["WatchdogCredentials:Username"];
    opt.WatchPagePassword = Environment.GetEnvironmentVariable("JOBPRO_WATCHDOG_PASSWORD") ?? configuration["WatchdogCredentials:Password"];
});

app.Run();
