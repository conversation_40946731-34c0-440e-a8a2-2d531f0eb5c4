﻿using Microsoft.Extensions.Configuration;
using NotificationService.Core.Models;
using NotificationService.Core.Services;
using RestSharp;
using System.Net.Http.Headers;
using System.Text;
using RazorLight;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using WatchDog;
using NotificationService.Core.Exceptions;
using System.Net;
using NotificationService.Core.Constants;

namespace NotificationService.Infrastructure.Services
{
    public class EmailMessageService : IEmailMessageService
    {
        private readonly IMessageTemplateService _templateService;
        private readonly EmailOptions? _emailOptions;
        private readonly AppEmailDomains? _appEmailDomains;
        private readonly IConfiguration _configuration;
        ILogger<EmailMessageService> _logger;
        public EmailMessageService(IConfiguration configuration, ILogger<EmailMessageService> logger, IMessageTemplateService templateService)
        {
            _emailOptions = configuration.GetSection("EmailOptions").Get<EmailOptions>();
            _appEmailDomains = configuration.GetSection("AppEmailDomains").Get<AppEmailDomains>();
            _templateService = templateService;
            _logger = logger;
            _configuration = configuration;
        }

        #region Send message
        public async Task<MessageResponseModel?> SendMessage(NotificationMessageModel model)
        {
            MessageResponseModel? result = null;

            if (model.TemplateWithPlaceHolders != null && string.IsNullOrEmpty(model.TemplateWithParams))
            {
                model.TemplateWithParams = await CompileTemplate(model.TemplateWithPlaceHolders.Template, model.TemplateWithPlaceHolders.Props, GenerateRandomKey());
            }

            if (model.Attachments.Any())
            {
                result = await SendUsingMailGunWithAttachment(model);
                return result;
            }

            result = await SendUsingMailGun(model);
            return result;
        }
        #endregion

        #region Private Methods
        private async Task<string> CompileTemplate(string templateString, Dictionary<string, string> props, string key)
        {
            RazorLightEngine engine = new RazorLightEngineBuilder()
                .UseFileSystemProject(Path.GetTempPath())
                .Build();

            string htmlContent = await engine.CompileRenderStringAsync(key, templateString, props);
            return htmlContent;
        }

        // Generate random 15 digit alpha numeric key
        private string GenerateRandomKey()
        {
            Random random = new Random();
            const string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
            return new string(Enumerable.Repeat(chars, 15)
                             .Select(s => s[random.Next(s.Length)]).ToArray());
        }
        #endregion

        #region Send Email using MailGun
        private async Task<MessageResponseModel?> SendUsingMailGun(NotificationMessageModel model)
        {
            MessageResponseModel? result = null;
            using (var client = new HttpClient
            {
                BaseAddress = new Uri(_emailOptions.Url)
            })
            {
                var apiKey = Environment.GetEnvironmentVariable("JOBPRO_MAILGUN_APIKEY") ?? _configuration["MAILGUN_API_KEY"];
                var domain = Environment.GetEnvironmentVariable("JOBPRO_MAILGUN_DOMAIN") ?? _configuration["MAILGUN_DOMAIN"];

                client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Basic", Convert.ToBase64String(Encoding.ASCII.GetBytes($"api:{apiKey}")));

                var from = $"{model.Application} <{GetApplicationEmailDomain(model.Application)}>";

                var content = new FormUrlEncodedContent(new[]
                {
                    new KeyValuePair<string, string>("from", from),
                    new KeyValuePair<string, string>("to", string.Join(',', model.RecipientEmails)),
                    new KeyValuePair<string, string>("cc", model.CC != null ? string.Join(',', model.CC) : "" ),
                    new KeyValuePair<string, string>("subject", model.Subject),
                    new KeyValuePair<string, string>("html", model.TemplateWithParams),
                    new KeyValuePair<string, string>("text", model.Body)
                });

                var response = await client.PostAsync($"{_emailOptions.Domain}/messages", content).ConfigureAwait(false);

                string responseString = await response.Content.ReadAsStringAsync();

                if (response.StatusCode != System.Net.HttpStatusCode.OK)
                {
                    throw new ApiException((int)HttpStatusCode.BadRequest, responseString);
                }

                var ret = new Dictionary<string, string>();
                // Check if responseString is a javascript object
                if (responseString.Contains("{") && responseString.Contains("}"))
                {
                    ret = JsonConvert.DeserializeObject<Dictionary<string, string>>(responseString);
                }

                if (ret.Count > 0)
                {
                    result = new MessageResponseModel() { ResponseId = ret["id"], StatusCode = "", StatusMessage = ret["message"] };
                }

                if (responseString == "Forbidden")
                    result = new MessageResponseModel() { ResponseId = "", StatusCode = "403", StatusMessage = responseString };

                _logger.LogInformation("MailGun response " + responseString);
            }

            return result;
        }
        #endregion

        #region Send Email using MailGun with attachment
        private async Task<MessageResponseModel?> SendUsingMailGunWithAttachment(NotificationMessageModel model)
        {
            MessageResponseModel? result = null;
            using (var client = new HttpClient
            {
                BaseAddress = new Uri(_emailOptions.Url)
            })
            {
                var apiKey = Environment.GetEnvironmentVariable("JOBPRO_MAILGUN_APIKEY") ?? _configuration["MAILGUN_API_KEY"];
                var domain = Environment.GetEnvironmentVariable("JOBPRO_MAILGUN_DOMAIN") ?? _configuration["MAILGUN_DOMAIN"];

                client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Basic", Convert.ToBase64String(Encoding.ASCII.GetBytes($"api:{apiKey}")));

                //    var from = $"{model.Application} <{_emailOptions.FromEmail}>";

                var from = $"{model.Application} <{GetApplicationEmailDomain(model.Application)}>";

                var content = new MultipartFormDataContent
                {
                    { new StringContent(from), "from" },
                    { new StringContent(string.Join(',', model.RecipientEmails)), "to" },
                    { new StringContent(model.CC != null ? string.Join(',', model.CC) : ""), "cc" },
                    { new StringContent(model.Subject), "subject" },
                    { new StringContent(model.TemplateWithParams), "html" },
                    { new StringContent(model.Body), "text" }
                };

                // Add attachments from base64 strings
                if (model.Attachments.Any())
                {
                    foreach (var attachment in model.Attachments)
                    {
                        if (!string.IsNullOrEmpty(attachment.Base64Content))
                        {
                            var bytes = Convert.FromBase64String(attachment.Base64Content);
                            var stream = new MemoryStream(bytes);
                            content.Add(new StreamContent(stream), "attachment", attachment.FileName);
                        }
                    }
                }

                var response = await client.PostAsync($"{_emailOptions.Domain}/messages", content).ConfigureAwait(false);
                string responseString = await response.Content.ReadAsStringAsync();

                var ret = new Dictionary<string, string>();
                // Check if responseString is a javascript object
                if (responseString.Contains("{") && responseString.Contains("}"))
                {
                    ret = JsonConvert.DeserializeObject<Dictionary<string, string>>(responseString);
                }

                if (ret.Count > 0)
                {
                    result = new MessageResponseModel() { ResponseId = ret["id"], StatusCode = "", StatusMessage = ret["message"] };
                }

                if (responseString == "Forbidden")
                    result = new MessageResponseModel() { ResponseId = "", StatusCode = "403", StatusMessage = responseString };

                _logger.LogInformation("MailGun response " + responseString);
                WatchLogger.Log("MailGun response", responseString);
            }

            return result;
        }

        private string GetApplicationEmailDomain(Application application)
        {
            return application switch
            {
                Application.Joble => _appEmailDomains.JobleEmail,
                Application.JobPays => _appEmailDomains.JobPaysEmail,
                Application.JobID => _appEmailDomains.JobIdEmail,
                Application.CaringBoss => _appEmailDomains.CaringBoss,
                Application.Jobfy => _appEmailDomains.Jobfy,
                _ => _appEmailDomains.JobleEmail // Default fallback for Echo and JobEvent
            };
        }

        #endregion

        //public async Task<HttpResponseMessage> CheckEmailStatus(string email)
        //{
        //    using (var client = new HttpClient
        //    {
        //        BaseAddress = new Uri(baseUrl + $"{domain}")
        //    })
        //    {
        //        client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Basic", Convert.ToBase64String(Encoding.ASCII.GetBytes($"api:{apiKey}")));
        //        var response = await client.GetAsync($"{domain}/events?ascending=yes&limit=25&pretty=yes&recipient={email}");
        //        return response;
        //    }
        //}
    }
}
