﻿using Microsoft.EntityFrameworkCore;
using NotificationService.Core.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection.Emit;
using System.Text;
using System.Threading.Tasks;

namespace NotificationService.Infrastructure.Repositories
{
    public class NotificationDbContext : DbContext
    {
        public NotificationDbContext(DbContextOptions<NotificationDbContext> options) : base(options)
        {
        }


        public DbSet<User> Users { get; set; }
        public DbSet<Notification> Notifications { get; set; }
        public DbSet<PushToken> PushTokens { get; set; }
       

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {

            base.OnModelCreating(modelBuilder);

        }

        public override int SaveChanges()
        {
            TriggerBeforeChangeEntityEvents();
            var change = base.SaveChanges();
            TriggerAfterChangeEntityEvents();
            return change;
        }

        public override Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
        {
            TriggerBeforeChangeEntityEvents();
            var change = base.SaveChangesAsync(cancellationToken);
            TriggerAfterChangeEntityEvents();
            return change;
        }

        public void TriggerAfterChangeEntityEvents()
        {

        }

        public void TriggerBeforeChangeEntityEvents()
        {
            var changedEntities = ChangeTracker.Entries();
            foreach (var changedEntity in changedEntities)
            {
                if (changedEntity.Entity is EntityBase)
                {
                    var entity = (EntityBase)changedEntity.Entity;

                    switch (changedEntity.State)
                    {
                        case EntityState.Added:
                            entity.OnBeforeInsert();
                            break;

                        case EntityState.Modified:
                            entity.OnBeforeUpdate();
                            break;
                    }
                }
            }
        }

    }
}

