﻿using WatchDog;

namespace NotificationService.API.Extensions
{
    public static class WatchDogServiceExtensiom
    {
        /// <summary>
        /// WatchDog Service Extension
        /// </summary>
        /// <param name="services"></param>
        public static void AddWatchDogService(this IServiceCollection services)
        {
            services.AddWatchDogServices(options =>
            {
                options.IsAutoClear = true;
                options.ClearTimeSchedule = WatchDog.src.Enums.WatchDogAutoClearScheduleEnum.Weekly;
            });
        }
    }
}
