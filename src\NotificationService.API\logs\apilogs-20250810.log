
14:53:24 [Information] :: Executed DbCommand ("28"ms) [Parameters=[""], CommandType='Text', CommandTimeout='30']"
""SELECT \"MigrationId\", \"ProductVersion\"
FROM \"__EFMigrationsHistory\"
ORDER BY \"MigrationId\";"

14:53:24 [Information] :: Executed DbCommand ("6"ms) [Parameters=[""], CommandType='Text', CommandTimeout='30']"
""SELECT \"MigrationId\", \"ProductVersion\"
FROM \"__EFMigrationsHistory\"
ORDER BY \"MigrationId\";"

14:53:24 [Information] :: Executed DbCommand ("12"ms) [Parameters=[""], CommandType='Text', CommandTimeout='30']"
""CREATE TABLE IF NOT EXISTS \"__EFMigrationsHistory\" (
    \"MigrationId\" character varying(150) NOT NULL,
    \"ProductVersion\" character varying(32) NOT NULL,
    CONSTRAINT \"PK___EFMigrationsHistory\" PRIMARY KEY (\"MigrationId\")
);"

14:53:24 [Information] :: Executed DbCommand ("3"ms) [Parameters=[""], CommandType='Text', CommandTimeout='30']"
""LOCK TABLE \"__EFMigrationsHistory\" IN ACCESS EXCLUSIVE MODE"

14:53:24 [Information] :: Executed DbCommand ("4"ms) [Parameters=[""], CommandType='Text', CommandTimeout='30']"
""SELECT \"MigrationId\", \"ProductVersion\"
FROM \"__EFMigrationsHistory\"
ORDER BY \"MigrationId\";"

14:53:24 [Information] :: Applying migration '"20250810135127_updated_pushtoken_tbl"'.

14:53:24 [Information] :: Executed DbCommand ("25"ms) [Parameters=[""], CommandType='Text', CommandTimeout='30']"
""DROP INDEX \"IX_PushTokens_UserId\";"

14:53:24 [Information] :: Executed DbCommand ("17"ms) [Parameters=[""], CommandType='Text', CommandTimeout='30']"
""CREATE INDEX \"IX_PushTokens_UserId\" ON \"PushTokens\" (\"UserId\");"

14:53:24 [Information] :: Executed DbCommand ("2"ms) [Parameters=[""], CommandType='Text', CommandTimeout='30']"
""INSERT INTO \"__EFMigrationsHistory\" (\"MigrationId\", \"ProductVersion\")
VALUES ('20250810135127_updated_pushtoken_tbl', '9.0.2');"

14:53:26 [Information] :: Firebase Admin SDK initialized successfully

14:53:27 [Information] :: RabbitMQ Broker Url: amqps://xruwodqv:<EMAIL>/xruwodqv

14:53:27 [Information] :: RabbitMQ Broker Url: amqps://xruwodqv:<EMAIL>/xruwodqv

14:53:27 [Information] :: User profile is available. Using '"C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys"' as key repository and Windows DPAPI to encrypt keys at rest.

14:53:27 [Information] :: Creating new RabbitMQ connection for queue notification-message-queue

14:53:32 [Information] :: RabbitMQ consumer started successfully for queue notification-message-queue

14:53:32 [Information] :: Started consumer: "NotificationMessageConsumer"

14:53:32 [Information] :: Creating new RabbitMQ connection for queue user-created-queue

14:53:38 [Information] :: RabbitMQ consumer started successfully for queue user-created-queue

14:53:38 [Information] :: Started consumer: "UserCreatedMessageConsumer"

14:53:38 [Information] :: Now listening on: "https://localhost:7044"

14:53:38 [Information] :: Now listening on: "http://localhost:5179"

14:53:38 [Information] :: Application started. Press Ctrl+C to shut down.

14:53:38 [Information] :: Hosting environment: "Development"

14:53:38 [Information] :: Content root path: "C:\Users\<USER>\source\repos\Notification-Service\src\NotificationService.API"

14:53:40 [Information] :: Request starting "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/index.html""" - null null

14:53:40 [Information] :: Request finished "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/index.html""" - 200 null "text/html;charset=utf-8" 490.5644ms

14:53:41 [Information] :: Request starting "HTTP/2" "GET" "https"://"localhost:7044""""/_framework/aspnetcore-browser-refresh.js""" - null null

14:53:41 [Information] :: Request starting "HTTP/2" "GET" "https"://"localhost:7044""""/_vs/browserLink""" - null null

14:53:41 [Information] :: Request finished "HTTP/2" "GET" "https"://"localhost:7044""""/_framework/aspnetcore-browser-refresh.js""" - 200 16531 "application/javascript; charset=utf-8" 27.569ms

14:53:41 [Information] :: Request finished "HTTP/2" "GET" "https"://"localhost:7044""""/_vs/browserLink""" - 200 null "text/javascript; charset=UTF-8" 129.6453ms

14:53:42 [Information] :: Request starting "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/v1/swagger.json""" - null null

14:53:42 [Information] :: Request finished "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/v1/swagger.json""" - 200 null "application/json;charset=utf-8" 41.7484ms

14:55:11 [Information] :: Request starting "HTTP/1.1" "POST" "https"://"localhost:7044""""/api/User/create-push-token""" - "application/json" 288

14:55:11 [Information] :: Executing endpoint '"NotificationService.API.Controllers.UserController.PushToken (NotificationService.API)"'

14:55:11 [Information] :: Route matched with "{action = \"PushToken\", controller = \"User\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] PushToken(NotificationService.Core.Models.PushTokenModel)" on controller "NotificationService.API.Controllers.UserController" ("NotificationService.API").

14:55:12 [Information] :: Executed DbCommand ("17"ms) [Parameters=["@__model_userId_0='?'"], CommandType='Text', CommandTimeout='30']"
""SELECT u.\"Id\", u.\"CompanyEmail\", u.\"Country\", u.\"CreatedBy\", u.\"CreatedOn\", u.\"Email\", u.\"FirstName\", u.\"LastName\", u.\"PhoneNumber\", u.\"TimeZone\", u.\"UpdatedBy\", u.\"UpdatedOn\", u.\"UserId\", u.\"Username\", u.\"ZipCode\"
FROM \"Users\" AS u
WHERE u.\"UserId\" = @__model_userId_0
LIMIT 1"

14:55:14 [Information] :: Executed DbCommand ("12"ms) [Parameters=["@p0='?', @p1='?', @p2='?', @p3='?' (DbType = DateTime), @p4='?', @p5='?', @p6='?', @p7='?', @p8='?', @p9='?', @p10='?' (DbType = DateTime), @p11='?', @p12='?', @p13='?'"], CommandType='Text', CommandTimeout='30']"
""INSERT INTO \"Users\" (\"CompanyEmail\", \"Country\", \"CreatedBy\", \"CreatedOn\", \"Email\", \"FirstName\", \"LastName\", \"PhoneNumber\", \"TimeZone\", \"UpdatedBy\", \"UpdatedOn\", \"UserId\", \"Username\", \"ZipCode\")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13)
RETURNING \"Id\";"

14:55:14 [Information] :: Executed DbCommand ("4"ms) [Parameters=["@__model_userId_0='?', @__ToString_1='?', @__model_Platform_2='?' (DbType = Int32)"], CommandType='Text', CommandTimeout='30']"
""SELECT p.\"Id\", p.\"AppName\", p.\"CreatedBy\", p.\"CreatedOn\", p.\"Platform\", p.\"Token\", p.\"UpdatedBy\", p.\"UpdatedOn\", p.\"UserId\"
FROM \"PushTokens\" AS p
WHERE p.\"UserId\" = @__model_userId_0 AND p.\"AppName\" = @__ToString_1 AND p.\"Platform\" = @__model_Platform_2
LIMIT 1"

14:55:14 [Information] :: Executed DbCommand ("4"ms) [Parameters=["@p0='?', @p1='?', @p2='?' (DbType = DateTime), @p3='?' (DbType = Int32), @p4='?', @p5='?', @p6='?' (DbType = DateTime), @p7='?'"], CommandType='Text', CommandTimeout='30']"
""INSERT INTO \"PushTokens\" (\"AppName\", \"CreatedBy\", \"CreatedOn\", \"Platform\", \"Token\", \"UpdatedBy\", \"UpdatedOn\", \"UserId\")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7)
RETURNING \"Id\";"

14:55:14 [Information] :: Executing "OkObjectResult", writing value of type '"<>f__AnonymousType1`3[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[NotificationService.Core.Models.PushTokenModel, NotificationService.Core, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]"'.

14:55:14 [Information] :: Executed action "NotificationService.API.Controllers.UserController.PushToken (NotificationService.API)" in 2453.8488ms

14:55:14 [Information] :: Executed endpoint '"NotificationService.API.Controllers.UserController.PushToken (NotificationService.API)"'

14:55:14 [Information] :: Request finished "HTTP/1.1" "POST" "https"://"localhost:7044""""/api/User/create-push-token""" - 200 null "application/json; charset=utf-8" 3299.076ms

14:55:32 [Information] :: Request starting "HTTP/1.1" "POST" "https"://"localhost:7044""""/api/User/create-push-token""" - "application/json" 288

14:55:32 [Information] :: Executing endpoint '"NotificationService.API.Controllers.UserController.PushToken (NotificationService.API)"'

14:55:32 [Information] :: Route matched with "{action = \"PushToken\", controller = \"User\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] PushToken(NotificationService.Core.Models.PushTokenModel)" on controller "NotificationService.API.Controllers.UserController" ("NotificationService.API").

14:55:32 [Information] :: Executed DbCommand ("1"ms) [Parameters=["@__model_userId_0='?'"], CommandType='Text', CommandTimeout='30']"
""SELECT u.\"Id\", u.\"CompanyEmail\", u.\"Country\", u.\"CreatedBy\", u.\"CreatedOn\", u.\"Email\", u.\"FirstName\", u.\"LastName\", u.\"PhoneNumber\", u.\"TimeZone\", u.\"UpdatedBy\", u.\"UpdatedOn\", u.\"UserId\", u.\"Username\", u.\"ZipCode\"
FROM \"Users\" AS u
WHERE u.\"UserId\" = @__model_userId_0
LIMIT 1"

14:55:32 [Information] :: Executed DbCommand ("1"ms) [Parameters=["@__model_userId_0='?', @__ToString_1='?', @__model_Platform_2='?' (DbType = Int32)"], CommandType='Text', CommandTimeout='30']"
""SELECT p.\"Id\", p.\"AppName\", p.\"CreatedBy\", p.\"CreatedOn\", p.\"Platform\", p.\"Token\", p.\"UpdatedBy\", p.\"UpdatedOn\", p.\"UserId\"
FROM \"PushTokens\" AS p
WHERE p.\"UserId\" = @__model_userId_0 AND p.\"AppName\" = @__ToString_1 AND p.\"Platform\" = @__model_Platform_2
LIMIT 1"

14:55:32 [Information] :: Executed DbCommand ("1"ms) [Parameters=["@p0='?', @p1='?', @p2='?' (DbType = DateTime), @p3='?' (DbType = Int32), @p4='?', @p5='?', @p6='?' (DbType = DateTime), @p7='?'"], CommandType='Text', CommandTimeout='30']"
""INSERT INTO \"PushTokens\" (\"AppName\", \"CreatedBy\", \"CreatedOn\", \"Platform\", \"Token\", \"UpdatedBy\", \"UpdatedOn\", \"UserId\")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7)
RETURNING \"Id\";"

14:55:32 [Information] :: Executing "OkObjectResult", writing value of type '"<>f__AnonymousType1`3[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[NotificationService.Core.Models.PushTokenModel, NotificationService.Core, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]"'.

14:55:32 [Information] :: Executed action "NotificationService.API.Controllers.UserController.PushToken (NotificationService.API)" in 195.5818ms

14:55:32 [Information] :: Executed endpoint '"NotificationService.API.Controllers.UserController.PushToken (NotificationService.API)"'

14:55:32 [Information] :: Request finished "HTTP/1.1" "POST" "https"://"localhost:7044""""/api/User/create-push-token""" - 200 null "application/json; charset=utf-8" 244.3799ms

14:55:43 [Information] :: Request starting "HTTP/1.1" "POST" "https"://"localhost:7044""""/api/User/create-push-token""" - "application/json" 288

14:55:43 [Information] :: Executing endpoint '"NotificationService.API.Controllers.UserController.PushToken (NotificationService.API)"'

14:55:43 [Information] :: Route matched with "{action = \"PushToken\", controller = \"User\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] PushToken(NotificationService.Core.Models.PushTokenModel)" on controller "NotificationService.API.Controllers.UserController" ("NotificationService.API").

14:55:43 [Information] :: Executed DbCommand ("2"ms) [Parameters=["@__model_userId_0='?'"], CommandType='Text', CommandTimeout='30']"
""SELECT u.\"Id\", u.\"CompanyEmail\", u.\"Country\", u.\"CreatedBy\", u.\"CreatedOn\", u.\"Email\", u.\"FirstName\", u.\"LastName\", u.\"PhoneNumber\", u.\"TimeZone\", u.\"UpdatedBy\", u.\"UpdatedOn\", u.\"UserId\", u.\"Username\", u.\"ZipCode\"
FROM \"Users\" AS u
WHERE u.\"UserId\" = @__model_userId_0
LIMIT 1"

14:55:43 [Information] :: Executed DbCommand ("2"ms) [Parameters=["@__model_userId_0='?', @__ToString_1='?', @__model_Platform_2='?' (DbType = Int32)"], CommandType='Text', CommandTimeout='30']"
""SELECT p.\"Id\", p.\"AppName\", p.\"CreatedBy\", p.\"CreatedOn\", p.\"Platform\", p.\"Token\", p.\"UpdatedBy\", p.\"UpdatedOn\", p.\"UserId\"
FROM \"PushTokens\" AS p
WHERE p.\"UserId\" = @__model_userId_0 AND p.\"AppName\" = @__ToString_1 AND p.\"Platform\" = @__model_Platform_2
LIMIT 1"

14:55:43 [Information] :: Executed DbCommand ("7"ms) [Parameters=["@p8='?' (DbType = Int64), @p0='?', @p1='?', @p2='?' (DbType = DateTime), @p3='?' (DbType = Int32), @p4='?', @p5='?', @p6='?' (DbType = DateTime), @p7='?'"], CommandType='Text', CommandTimeout='30']"
""UPDATE \"PushTokens\" SET \"AppName\" = @p0, \"CreatedBy\" = @p1, \"CreatedOn\" = @p2, \"Platform\" = @p3, \"Token\" = @p4, \"UpdatedBy\" = @p5, \"UpdatedOn\" = @p6, \"UserId\" = @p7
WHERE \"Id\" = @p8;"

14:55:43 [Information] :: Executing "OkObjectResult", writing value of type '"<>f__AnonymousType1`3[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[NotificationService.Core.Models.PushTokenModel, NotificationService.Core, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]"'.

14:55:43 [Information] :: Executed action "NotificationService.API.Controllers.UserController.PushToken (NotificationService.API)" in 79.9002ms

14:55:43 [Information] :: Executed endpoint '"NotificationService.API.Controllers.UserController.PushToken (NotificationService.API)"'

14:55:43 [Information] :: Request finished "HTTP/1.1" "POST" "https"://"localhost:7044""""/api/User/create-push-token""" - 200 null "application/json; charset=utf-8" 199.1356ms

14:55:53 [Information] :: Request starting "HTTP/1.1" "POST" "https"://"localhost:7044""""/api/User/create-push-token""" - "application/json" 288

14:55:53 [Information] :: Executing endpoint '"NotificationService.API.Controllers.UserController.PushToken (NotificationService.API)"'

14:55:53 [Information] :: Route matched with "{action = \"PushToken\", controller = \"User\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] PushToken(NotificationService.Core.Models.PushTokenModel)" on controller "NotificationService.API.Controllers.UserController" ("NotificationService.API").

14:55:53 [Information] :: Executed DbCommand ("1"ms) [Parameters=["@__model_userId_0='?'"], CommandType='Text', CommandTimeout='30']"
""SELECT u.\"Id\", u.\"CompanyEmail\", u.\"Country\", u.\"CreatedBy\", u.\"CreatedOn\", u.\"Email\", u.\"FirstName\", u.\"LastName\", u.\"PhoneNumber\", u.\"TimeZone\", u.\"UpdatedBy\", u.\"UpdatedOn\", u.\"UserId\", u.\"Username\", u.\"ZipCode\"
FROM \"Users\" AS u
WHERE u.\"UserId\" = @__model_userId_0
LIMIT 1"

14:55:53 [Information] :: Executed DbCommand ("1"ms) [Parameters=["@__model_userId_0='?', @__ToString_1='?', @__model_Platform_2='?' (DbType = Int32)"], CommandType='Text', CommandTimeout='30']"
""SELECT p.\"Id\", p.\"AppName\", p.\"CreatedBy\", p.\"CreatedOn\", p.\"Platform\", p.\"Token\", p.\"UpdatedBy\", p.\"UpdatedOn\", p.\"UserId\"
FROM \"PushTokens\" AS p
WHERE p.\"UserId\" = @__model_userId_0 AND p.\"AppName\" = @__ToString_1 AND p.\"Platform\" = @__model_Platform_2
LIMIT 1"

14:55:53 [Information] :: Executed DbCommand ("2"ms) [Parameters=["@p0='?', @p1='?', @p2='?' (DbType = DateTime), @p3='?' (DbType = Int32), @p4='?', @p5='?', @p6='?' (DbType = DateTime), @p7='?'"], CommandType='Text', CommandTimeout='30']"
""INSERT INTO \"PushTokens\" (\"AppName\", \"CreatedBy\", \"CreatedOn\", \"Platform\", \"Token\", \"UpdatedBy\", \"UpdatedOn\", \"UserId\")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7)
RETURNING \"Id\";"

14:55:53 [Information] :: Executing "OkObjectResult", writing value of type '"<>f__AnonymousType1`3[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[NotificationService.Core.Models.PushTokenModel, NotificationService.Core, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]"'.

14:55:53 [Information] :: Executed action "NotificationService.API.Controllers.UserController.PushToken (NotificationService.API)" in 42.8094ms

14:55:53 [Information] :: Executed endpoint '"NotificationService.API.Controllers.UserController.PushToken (NotificationService.API)"'

14:55:53 [Information] :: Request finished "HTTP/1.1" "POST" "https"://"localhost:7044""""/api/User/create-push-token""" - 200 null "application/json; charset=utf-8" 75.5147ms

20:19:23 [Information] :: Executed DbCommand ("23"ms) [Parameters=[""], CommandType='Text', CommandTimeout='30']"
""SELECT \"MigrationId\", \"ProductVersion\"
FROM \"__EFMigrationsHistory\"
ORDER BY \"MigrationId\";"

20:19:23 [Information] :: Executed DbCommand ("17"ms) [Parameters=[""], CommandType='Text', CommandTimeout='30']"
""SELECT \"MigrationId\", \"ProductVersion\"
FROM \"__EFMigrationsHistory\"
ORDER BY \"MigrationId\";"

20:19:23 [Information] :: Executed DbCommand ("21"ms) [Parameters=[""], CommandType='Text', CommandTimeout='30']"
""CREATE TABLE IF NOT EXISTS \"__EFMigrationsHistory\" (
    \"MigrationId\" character varying(150) NOT NULL,
    \"ProductVersion\" character varying(32) NOT NULL,
    CONSTRAINT \"PK___EFMigrationsHistory\" PRIMARY KEY (\"MigrationId\")
);"

20:19:23 [Information] :: Executed DbCommand ("6"ms) [Parameters=[""], CommandType='Text', CommandTimeout='30']"
""LOCK TABLE \"__EFMigrationsHistory\" IN ACCESS EXCLUSIVE MODE"

20:19:23 [Information] :: Executed DbCommand ("1"ms) [Parameters=[""], CommandType='Text', CommandTimeout='30']"
""SELECT \"MigrationId\", \"ProductVersion\"
FROM \"__EFMigrationsHistory\"
ORDER BY \"MigrationId\";"

20:19:23 [Information] :: Applying migration '"20250810191840_updated_pushtoken_tbl2"'.

20:19:23 [Information] :: Executed DbCommand ("16"ms) [Parameters=[""], CommandType='Text', CommandTimeout='30']"
""ALTER TABLE \"PushTokens\" ADD \"IOSPushType\" integer;"

20:19:23 [Information] :: Executed DbCommand ("2"ms) [Parameters=[""], CommandType='Text', CommandTimeout='30']"
""INSERT INTO \"__EFMigrationsHistory\" (\"MigrationId\", \"ProductVersion\")
VALUES ('20250810191840_updated_pushtoken_tbl2', '9.0.2');"

20:19:24 [Information] :: Firebase Admin SDK initialized successfully

20:19:24 [Information] :: RabbitMQ Broker Url: amqps://xruwodqv:<EMAIL>/xruwodqv

20:19:24 [Information] :: RabbitMQ Broker Url: amqps://xruwodqv:<EMAIL>/xruwodqv

20:19:24 [Information] :: User profile is available. Using '"C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys"' as key repository and Windows DPAPI to encrypt keys at rest.

20:19:24 [Information] :: Creating new RabbitMQ connection for queue notification-message-queue

20:19:34 [Information] :: RabbitMQ consumer started successfully for queue notification-message-queue

20:19:34 [Information] :: Started consumer: "NotificationMessageConsumer"

20:19:34 [Information] :: Creating new RabbitMQ connection for queue user-created-queue

20:19:39 [Information] :: RabbitMQ consumer started successfully for queue user-created-queue

20:19:39 [Information] :: Started consumer: "UserCreatedMessageConsumer"

20:19:40 [Information] :: Now listening on: "https://localhost:7044"

20:19:40 [Information] :: Now listening on: "http://localhost:5179"

20:19:40 [Information] :: Application started. Press Ctrl+C to shut down.

20:19:40 [Information] :: Hosting environment: "Development"

20:19:40 [Information] :: Content root path: "C:\Users\<USER>\source\repos\Notification-Service\src\NotificationService.API"

20:19:42 [Information] :: Request starting "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/index.html""" - null null

20:19:42 [Information] :: Request finished "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/index.html""" - 200 null "text/html;charset=utf-8" 221.6907ms

20:19:42 [Information] :: Request starting "HTTP/2" "GET" "https"://"localhost:7044""""/_framework/aspnetcore-browser-refresh.js""" - null null

20:19:42 [Information] :: Request finished "HTTP/2" "GET" "https"://"localhost:7044""""/_framework/aspnetcore-browser-refresh.js""" - 200 16531 "application/javascript; charset=utf-8" 16.3113ms

20:19:42 [Information] :: Request starting "HTTP/2" "GET" "https"://"localhost:7044""""/_vs/browserLink""" - null null

20:19:42 [Information] :: Request finished "HTTP/2" "GET" "https"://"localhost:7044""""/_vs/browserLink""" - 200 null "text/javascript; charset=UTF-8" 57.4103ms

20:19:43 [Information] :: Request starting "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/v1/swagger.json""" - null null

20:19:43 [Information] :: Request finished "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/v1/swagger.json""" - 200 null "application/json;charset=utf-8" 59.2406ms

20:20:15 [Information] :: Request starting "HTTP/1.1" "POST" "https"://"localhost:7044""""/api/User/create-push-token""" - "application/json" 312

20:20:15 [Information] :: Executing endpoint '"NotificationService.API.Controllers.UserController.PushToken (NotificationService.API)"'

20:20:15 [Information] :: Route matched with "{action = \"PushToken\", controller = \"User\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] PushToken(NotificationService.Core.Models.PushTokenModel)" on controller "NotificationService.API.Controllers.UserController" ("NotificationService.API").

20:20:16 [Information] :: Executed DbCommand ("41"ms) [Parameters=["@__model_userId_0='?'"], CommandType='Text', CommandTimeout='30']"
""SELECT u.\"Id\", u.\"CompanyEmail\", u.\"Country\", u.\"CreatedBy\", u.\"CreatedOn\", u.\"Email\", u.\"FirstName\", u.\"LastName\", u.\"PhoneNumber\", u.\"TimeZone\", u.\"UpdatedBy\", u.\"UpdatedOn\", u.\"UserId\", u.\"Username\", u.\"ZipCode\"
FROM \"Users\" AS u
WHERE u.\"UserId\" = @__model_userId_0
LIMIT 1"

20:20:16 [Information] :: Executed DbCommand ("9"ms) [Parameters=["@__model_userId_0='?', @__ToString_1='?', @__model_Platform_2='?' (DbType = Int32)"], CommandType='Text', CommandTimeout='30']"
""SELECT p.\"Id\", p.\"AppName\", p.\"CreatedBy\", p.\"CreatedOn\", p.\"IOSPushType\", p.\"Platform\", p.\"Token\", p.\"UpdatedBy\", p.\"UpdatedOn\", p.\"UserId\"
FROM \"PushTokens\" AS p
WHERE p.\"UserId\" = @__model_userId_0 AND p.\"AppName\" = @__ToString_1 AND p.\"Platform\" = @__model_Platform_2 AND p.\"IOSPushType\" IS NULL
LIMIT 1"

20:20:17 [Information] :: Executed DbCommand ("11"ms) [Parameters=["@p9='?' (DbType = Int64), @p0='?', @p1='?', @p2='?' (DbType = DateTime), @p3='?' (DbType = Int32), @p4='?' (DbType = Int32), @p5='?', @p6='?', @p7='?' (DbType = DateTime), @p8='?'"], CommandType='Text', CommandTimeout='30']"
""UPDATE \"PushTokens\" SET \"AppName\" = @p0, \"CreatedBy\" = @p1, \"CreatedOn\" = @p2, \"IOSPushType\" = @p3, \"Platform\" = @p4, \"Token\" = @p5, \"UpdatedBy\" = @p6, \"UpdatedOn\" = @p7, \"UserId\" = @p8
WHERE \"Id\" = @p9;"

20:20:17 [Information] :: Executing "OkObjectResult", writing value of type '"<>f__AnonymousType1`3[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[NotificationService.Core.Models.PushTokenModel, NotificationService.Core, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]"'.

20:20:17 [Information] :: Executed action "NotificationService.API.Controllers.UserController.PushToken (NotificationService.API)" in 1600.4294ms

20:20:17 [Information] :: Executed endpoint '"NotificationService.API.Controllers.UserController.PushToken (NotificationService.API)"'

20:20:17 [Information] :: Request finished "HTTP/1.1" "POST" "https"://"localhost:7044""""/api/User/create-push-token""" - 200 null "application/json; charset=utf-8" 2394.8723ms

20:20:44 [Information] :: Request starting "HTTP/1.1" "POST" "https"://"localhost:7044""""/api/User/create-push-token""" - "application/json" 309

20:20:44 [Information] :: Executing endpoint '"NotificationService.API.Controllers.UserController.PushToken (NotificationService.API)"'

20:20:44 [Information] :: Route matched with "{action = \"PushToken\", controller = \"User\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] PushToken(NotificationService.Core.Models.PushTokenModel)" on controller "NotificationService.API.Controllers.UserController" ("NotificationService.API").

20:20:45 [Information] :: Executed DbCommand ("4"ms) [Parameters=["@__model_userId_0='?'"], CommandType='Text', CommandTimeout='30']"
""SELECT u.\"Id\", u.\"CompanyEmail\", u.\"Country\", u.\"CreatedBy\", u.\"CreatedOn\", u.\"Email\", u.\"FirstName\", u.\"LastName\", u.\"PhoneNumber\", u.\"TimeZone\", u.\"UpdatedBy\", u.\"UpdatedOn\", u.\"UserId\", u.\"Username\", u.\"ZipCode\"
FROM \"Users\" AS u
WHERE u.\"UserId\" = @__model_userId_0
LIMIT 1"

20:20:45 [Information] :: Executed DbCommand ("1"ms) [Parameters=["@__model_userId_0='?', @__ToString_1='?', @__model_Platform_2='?' (DbType = Int32), @__model_IOSPushType_3='?' (DbType = Int32)"], CommandType='Text', CommandTimeout='30']"
""SELECT p.\"Id\", p.\"AppName\", p.\"CreatedBy\", p.\"CreatedOn\", p.\"IOSPushType\", p.\"Platform\", p.\"Token\", p.\"UpdatedBy\", p.\"UpdatedOn\", p.\"UserId\"
FROM \"PushTokens\" AS p
WHERE p.\"UserId\" = @__model_userId_0 AND p.\"AppName\" = @__ToString_1 AND p.\"Platform\" = @__model_Platform_2 AND p.\"IOSPushType\" = @__model_IOSPushType_3
LIMIT 1"

20:20:45 [Information] :: Executed DbCommand ("9"ms) [Parameters=["@p0='?', @p1='?', @p2='?' (DbType = DateTime), @p3='?' (DbType = Int32), @p4='?' (DbType = Int32), @p5='?', @p6='?', @p7='?' (DbType = DateTime), @p8='?'"], CommandType='Text', CommandTimeout='30']"
""INSERT INTO \"PushTokens\" (\"AppName\", \"CreatedBy\", \"CreatedOn\", \"IOSPushType\", \"Platform\", \"Token\", \"UpdatedBy\", \"UpdatedOn\", \"UserId\")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8)
RETURNING \"Id\";"

20:20:45 [Information] :: Executing "OkObjectResult", writing value of type '"<>f__AnonymousType1`3[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[NotificationService.Core.Models.PushTokenModel, NotificationService.Core, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]"'.

20:20:45 [Information] :: Executed action "NotificationService.API.Controllers.UserController.PushToken (NotificationService.API)" in 305.1153ms

20:20:45 [Information] :: Executed endpoint '"NotificationService.API.Controllers.UserController.PushToken (NotificationService.API)"'

20:20:45 [Information] :: Request finished "HTTP/1.1" "POST" "https"://"localhost:7044""""/api/User/create-push-token""" - 200 null "application/json; charset=utf-8" 370.2566ms

20:21:03 [Information] :: Request starting "HTTP/1.1" "POST" "https"://"localhost:7044""""/api/User/create-push-token""" - "application/json" 309

20:21:03 [Information] :: Executing endpoint '"NotificationService.API.Controllers.UserController.PushToken (NotificationService.API)"'

20:21:03 [Information] :: Route matched with "{action = \"PushToken\", controller = \"User\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] PushToken(NotificationService.Core.Models.PushTokenModel)" on controller "NotificationService.API.Controllers.UserController" ("NotificationService.API").

20:21:03 [Information] :: Executed DbCommand ("1"ms) [Parameters=["@__model_userId_0='?'"], CommandType='Text', CommandTimeout='30']"
""SELECT u.\"Id\", u.\"CompanyEmail\", u.\"Country\", u.\"CreatedBy\", u.\"CreatedOn\", u.\"Email\", u.\"FirstName\", u.\"LastName\", u.\"PhoneNumber\", u.\"TimeZone\", u.\"UpdatedBy\", u.\"UpdatedOn\", u.\"UserId\", u.\"Username\", u.\"ZipCode\"
FROM \"Users\" AS u
WHERE u.\"UserId\" = @__model_userId_0
LIMIT 1"

20:21:03 [Information] :: Executed DbCommand ("2"ms) [Parameters=["@__model_userId_0='?', @__ToString_1='?', @__model_Platform_2='?' (DbType = Int32), @__model_IOSPushType_3='?' (DbType = Int32)"], CommandType='Text', CommandTimeout='30']"
""SELECT p.\"Id\", p.\"AppName\", p.\"CreatedBy\", p.\"CreatedOn\", p.\"IOSPushType\", p.\"Platform\", p.\"Token\", p.\"UpdatedBy\", p.\"UpdatedOn\", p.\"UserId\"
FROM \"PushTokens\" AS p
WHERE p.\"UserId\" = @__model_userId_0 AND p.\"AppName\" = @__ToString_1 AND p.\"Platform\" = @__model_Platform_2 AND p.\"IOSPushType\" = @__model_IOSPushType_3
LIMIT 1"

20:21:03 [Information] :: Executed DbCommand ("1"ms) [Parameters=["@p9='?' (DbType = Int64), @p0='?', @p1='?', @p2='?' (DbType = DateTime), @p3='?' (DbType = Int32), @p4='?' (DbType = Int32), @p5='?', @p6='?', @p7='?' (DbType = DateTime), @p8='?'"], CommandType='Text', CommandTimeout='30']"
""UPDATE \"PushTokens\" SET \"AppName\" = @p0, \"CreatedBy\" = @p1, \"CreatedOn\" = @p2, \"IOSPushType\" = @p3, \"Platform\" = @p4, \"Token\" = @p5, \"UpdatedBy\" = @p6, \"UpdatedOn\" = @p7, \"UserId\" = @p8
WHERE \"Id\" = @p9;"

20:21:03 [Information] :: Executing "OkObjectResult", writing value of type '"<>f__AnonymousType1`3[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[NotificationService.Core.Models.PushTokenModel, NotificationService.Core, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]"'.

20:21:03 [Information] :: Executed action "NotificationService.API.Controllers.UserController.PushToken (NotificationService.API)" in 58.0338ms

20:21:03 [Information] :: Executed endpoint '"NotificationService.API.Controllers.UserController.PushToken (NotificationService.API)"'

20:21:03 [Information] :: Request finished "HTTP/1.1" "POST" "https"://"localhost:7044""""/api/User/create-push-token""" - 200 null "application/json; charset=utf-8" 115.4013ms

20:21:09 [Information] :: Request starting "HTTP/1.1" "POST" "https"://"localhost:7044""""/api/User/create-push-token""" - "application/json" 312

20:21:09 [Information] :: Executing endpoint '"NotificationService.API.Controllers.UserController.PushToken (NotificationService.API)"'

20:21:09 [Information] :: Route matched with "{action = \"PushToken\", controller = \"User\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] PushToken(NotificationService.Core.Models.PushTokenModel)" on controller "NotificationService.API.Controllers.UserController" ("NotificationService.API").

20:21:09 [Information] :: Executed DbCommand ("2"ms) [Parameters=["@__model_userId_0='?'"], CommandType='Text', CommandTimeout='30']"
""SELECT u.\"Id\", u.\"CompanyEmail\", u.\"Country\", u.\"CreatedBy\", u.\"CreatedOn\", u.\"Email\", u.\"FirstName\", u.\"LastName\", u.\"PhoneNumber\", u.\"TimeZone\", u.\"UpdatedBy\", u.\"UpdatedOn\", u.\"UserId\", u.\"Username\", u.\"ZipCode\"
FROM \"Users\" AS u
WHERE u.\"UserId\" = @__model_userId_0
LIMIT 1"

20:21:09 [Information] :: Executed DbCommand ("1"ms) [Parameters=["@__model_userId_0='?', @__ToString_1='?', @__model_Platform_2='?' (DbType = Int32)"], CommandType='Text', CommandTimeout='30']"
""SELECT p.\"Id\", p.\"AppName\", p.\"CreatedBy\", p.\"CreatedOn\", p.\"IOSPushType\", p.\"Platform\", p.\"Token\", p.\"UpdatedBy\", p.\"UpdatedOn\", p.\"UserId\"
FROM \"PushTokens\" AS p
WHERE p.\"UserId\" = @__model_userId_0 AND p.\"AppName\" = @__ToString_1 AND p.\"Platform\" = @__model_Platform_2 AND p.\"IOSPushType\" IS NULL
LIMIT 1"

20:21:09 [Information] :: Executed DbCommand ("1"ms) [Parameters=["@p9='?' (DbType = Int64), @p0='?', @p1='?', @p2='?' (DbType = DateTime), @p3='?' (DbType = Int32), @p4='?' (DbType = Int32), @p5='?', @p6='?', @p7='?' (DbType = DateTime), @p8='?'"], CommandType='Text', CommandTimeout='30']"
""UPDATE \"PushTokens\" SET \"AppName\" = @p0, \"CreatedBy\" = @p1, \"CreatedOn\" = @p2, \"IOSPushType\" = @p3, \"Platform\" = @p4, \"Token\" = @p5, \"UpdatedBy\" = @p6, \"UpdatedOn\" = @p7, \"UserId\" = @p8
WHERE \"Id\" = @p9;"

20:21:09 [Information] :: Executing "OkObjectResult", writing value of type '"<>f__AnonymousType1`3[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[NotificationService.Core.Models.PushTokenModel, NotificationService.Core, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]"'.

20:21:09 [Information] :: Executed action "NotificationService.API.Controllers.UserController.PushToken (NotificationService.API)" in 56.6167ms

20:21:09 [Information] :: Executed endpoint '"NotificationService.API.Controllers.UserController.PushToken (NotificationService.API)"'

20:21:09 [Information] :: Request finished "HTTP/1.1" "POST" "https"://"localhost:7044""""/api/User/create-push-token""" - 200 null "application/json; charset=utf-8" 123.3638ms

20:21:17 [Information] :: Request starting "HTTP/1.1" "POST" "https"://"localhost:7044""""/api/User/create-push-token""" - "application/json" 309

20:21:17 [Information] :: Executing endpoint '"NotificationService.API.Controllers.UserController.PushToken (NotificationService.API)"'

20:21:17 [Information] :: Route matched with "{action = \"PushToken\", controller = \"User\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] PushToken(NotificationService.Core.Models.PushTokenModel)" on controller "NotificationService.API.Controllers.UserController" ("NotificationService.API").

20:21:17 [Information] :: Executed DbCommand ("1"ms) [Parameters=["@__model_userId_0='?'"], CommandType='Text', CommandTimeout='30']"
""SELECT u.\"Id\", u.\"CompanyEmail\", u.\"Country\", u.\"CreatedBy\", u.\"CreatedOn\", u.\"Email\", u.\"FirstName\", u.\"LastName\", u.\"PhoneNumber\", u.\"TimeZone\", u.\"UpdatedBy\", u.\"UpdatedOn\", u.\"UserId\", u.\"Username\", u.\"ZipCode\"
FROM \"Users\" AS u
WHERE u.\"UserId\" = @__model_userId_0
LIMIT 1"

20:21:17 [Information] :: Executed DbCommand ("1"ms) [Parameters=["@__model_userId_0='?', @__ToString_1='?', @__model_Platform_2='?' (DbType = Int32), @__model_IOSPushType_3='?' (DbType = Int32)"], CommandType='Text', CommandTimeout='30']"
""SELECT p.\"Id\", p.\"AppName\", p.\"CreatedBy\", p.\"CreatedOn\", p.\"IOSPushType\", p.\"Platform\", p.\"Token\", p.\"UpdatedBy\", p.\"UpdatedOn\", p.\"UserId\"
FROM \"PushTokens\" AS p
WHERE p.\"UserId\" = @__model_userId_0 AND p.\"AppName\" = @__ToString_1 AND p.\"Platform\" = @__model_Platform_2 AND p.\"IOSPushType\" = @__model_IOSPushType_3
LIMIT 1"

20:21:17 [Information] :: Executed DbCommand ("1"ms) [Parameters=["@p0='?', @p1='?', @p2='?' (DbType = DateTime), @p3='?' (DbType = Int32), @p4='?' (DbType = Int32), @p5='?', @p6='?', @p7='?' (DbType = DateTime), @p8='?'"], CommandType='Text', CommandTimeout='30']"
""INSERT INTO \"PushTokens\" (\"AppName\", \"CreatedBy\", \"CreatedOn\", \"IOSPushType\", \"Platform\", \"Token\", \"UpdatedBy\", \"UpdatedOn\", \"UserId\")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8)
RETURNING \"Id\";"

20:21:17 [Information] :: Executing "OkObjectResult", writing value of type '"<>f__AnonymousType1`3[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[NotificationService.Core.Models.PushTokenModel, NotificationService.Core, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]"'.

20:21:17 [Information] :: Executed action "NotificationService.API.Controllers.UserController.PushToken (NotificationService.API)" in 47.7333ms

20:21:17 [Information] :: Executed endpoint '"NotificationService.API.Controllers.UserController.PushToken (NotificationService.API)"'

20:21:17 [Information] :: Request finished "HTTP/1.1" "POST" "https"://"localhost:7044""""/api/User/create-push-token""" - 200 null "application/json; charset=utf-8" 93.4594ms

20:45:26 [Information] :: Executed DbCommand ("40"ms) [Parameters=[""], CommandType='Text', CommandTimeout='30']"
""SELECT \"MigrationId\", \"ProductVersion\"
FROM \"__EFMigrationsHistory\"
ORDER BY \"MigrationId\";"

20:45:26 [Information] :: Firebase Admin SDK initialized successfully

20:45:26 [Information] :: RabbitMQ Broker Url: amqps://xruwodqv:<EMAIL>/xruwodqv

20:45:27 [Information] :: RabbitMQ Broker Url: amqps://xruwodqv:<EMAIL>/xruwodqv

20:45:27 [Information] :: User profile is available. Using '"C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys"' as key repository and Windows DPAPI to encrypt keys at rest.

20:45:27 [Information] :: Creating new RabbitMQ connection for queue notification-message-queue

20:45:32 [Information] :: RabbitMQ consumer started successfully for queue notification-message-queue

20:45:32 [Information] :: Started consumer: "NotificationMessageConsumer"

20:45:32 [Information] :: Creating new RabbitMQ connection for queue user-created-queue

20:45:38 [Information] :: RabbitMQ consumer started successfully for queue user-created-queue

20:45:38 [Information] :: Started consumer: "UserCreatedMessageConsumer"

20:45:38 [Information] :: Now listening on: "https://localhost:7044"

20:45:38 [Information] :: Now listening on: "http://localhost:5179"

20:45:38 [Information] :: Application started. Press Ctrl+C to shut down.

20:45:38 [Information] :: Hosting environment: "Development"

20:45:38 [Information] :: Content root path: "C:\Users\<USER>\source\repos\Notification-Service\src\NotificationService.API"

20:45:38 [Information] :: Request starting "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/index.html""" - null null

20:45:39 [Information] :: Request finished "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/index.html""" - 200 null "text/html;charset=utf-8" 257.112ms

20:45:39 [Information] :: Request starting "HTTP/2" "GET" "https"://"localhost:7044""""/_framework/aspnetcore-browser-refresh.js""" - null null

20:45:39 [Information] :: Request finished "HTTP/2" "GET" "https"://"localhost:7044""""/_framework/aspnetcore-browser-refresh.js""" - 200 16531 "application/javascript; charset=utf-8" 50.3271ms

20:45:39 [Information] :: Request starting "HTTP/2" "GET" "https"://"localhost:7044""""/_vs/browserLink""" - null null

20:45:39 [Information] :: Request finished "HTTP/2" "GET" "https"://"localhost:7044""""/_vs/browserLink""" - 200 null "text/javascript; charset=UTF-8" 124.1844ms

20:45:41 [Information] :: Request starting "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/v1/swagger.json""" - null null

20:45:41 [Information] :: Request finished "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/v1/swagger.json""" - 200 null "application/json;charset=utf-8" 41.4789ms

20:45:56 [Information] :: Notification Message "{\"pattern\":\"notification-message-queue\",\"data\":\"{\\"cc\\":[],\\"attachments\\":[],\\"notificationTypes\\":[3],\\"messageId\\":\\"9ec72ff2-4836-453e-927b-e90054bc0f34\\",\\"priority\\":0,\\"isWhatsappNo\\":false,\\"application\\":1,\\"body\\":\\"c964a01d-d0f3-4553-b53d-07e773d66525 is calling in General\\",\\"subject\\":\\"Incoming audio call\\",\\"userId\\":\\"c964a01d-d0f3-4553-b53d-07e773d66525\\",\\"notificationKey\\":2,\\"templateWithPlaceHolders\\":{\\"template\\":\\"incoming_call\\",\\"props\\":{\\"callId\\":\\"02jjdajqslds\\",\\"callerId\\":\\"6894d2ed1bc552f27cad33b9\\",\\"callerName\\":\\"Chidozie\\",\\"chatId\\":\\"6894d2aa1bc552f27cad331d\\",\\"callType\\":\\"audio\\",\\"chatType\\":\\"CIRCLE\\",\\"key\\":\\"incoming_call\\",\\"initiateCallPayload\\":\\"{\\\\"event\\\\":\\\\"INITIATE_CALL\\\\",\\\\"data\\\\":{\\\\"callerSocket\\\\":\\\\"yHkUsVFqkRP4yp2bAAAc\\\\",\\\\"callerId\\\\":\\\\"6894d2ed1bc552f27cad33b9\\\\",\\\\"callId\\\\":\\\\"02jjdajqslds\\\\",\\\\"callerJobProUserId\\\\":\\\\"c964a01d-d0f3-4553-b53d-07e773d66525\\\\",\\\\"callerName\\\\":\\\\"Osigwe Chidozie\\\\",\\\\"format\\\\":\\\\"audio\\\\",\\\\"type\\\\":\\\\"dm\\\\",\\\\"meetingId\\\\":\\\\"6894d2aa1bc552f27cad331d\\\\",\\\\"title\\\\":\\\\"Osigwe Chidozie\\\\",\\\\"name\\\\":\\\\"Osigwe Chidozie\\\\",\\\\"profilePictureUrl\\\\":\\\\"https://jobpro-public-dev-eu-central-1-010526262538.s3.eu-central-1.amazonaws.com/BB1CDBE9077B5043C70A98A05EA4DEE162F1-oua7kXE5bDF2Y3x9mh4gEi2dSo.png?X-Amz-Expires=604800&X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=AKIAQE43KAUFBUX3QAH7%2F20250805%2Feu-central-1%2Fs3%2Faws4_request&X-Amz-Date=20250805T102022Z&X-Amz-SignedHeaders=host&X-Amz-Signature=6964630ce1cc19b586eea5cc6d68a53da562bbb3f52aab0893339c05ad779bb2\\\\",\\\\"status\\\\":\\\\"INITIATE_CALL\\\\",\\\\"participants\\\\":[\\\\"de56874f-9080-4a54-88c4-265231fa7629\\\\"]}}\\"}}}\"}"

20:45:57 [Information] :: Executed DbCommand ("24"ms) [Parameters=["@__model_UserId_0='?', @__ToString_1='?'"], CommandType='Text', CommandTimeout='30']"
""SELECT p.\"Id\", p.\"AppName\", p.\"CreatedBy\", p.\"CreatedOn\", p.\"IOSPushType\", p.\"Platform\", p.\"Token\", p.\"UpdatedBy\", p.\"UpdatedOn\", p.\"UserId\"
FROM \"PushTokens\" AS p
WHERE p.\"UserId\" = @__model_UserId_0 AND p.\"AppName\" = @__ToString_1"

20:45:57 [Information] :: Executed DbCommand ("24"ms) [Parameters=["@__ToString_0='?'"], CommandType='Text', CommandTimeout='30']"
""SELECT u.\"Id\", u.\"CompanyEmail\", u.\"Country\", u.\"CreatedBy\", u.\"CreatedOn\", u.\"Email\", u.\"FirstName\", u.\"LastName\", u.\"PhoneNumber\", u.\"TimeZone\", u.\"UpdatedBy\", u.\"UpdatedOn\", u.\"UserId\", u.\"Username\", u.\"ZipCode\"
FROM \"Users\" AS u
WHERE u.\"UserId\" = @__ToString_0
LIMIT 1"

20:46:11 [Information] :: Push token for user c964a01d-d0f3-4553-b53d-07e773d66525 and app Joble: c3AMk9G3pRZcKri84Dj_FC:APA91bGVvdz2xOYvcb3VgqshMyAZOjiugdoB0LStTGLbWTYMdihbfcNt1sTc8uUTyet4z3XLOVNJi9wo2VZ5GHYenFw-ahIemLh9G0NR12Og8OaWNdMIJ4g

20:57:23 [Information] :: Executed DbCommand ("22"ms) [Parameters=[""], CommandType='Text', CommandTimeout='30']"
""SELECT \"MigrationId\", \"ProductVersion\"
FROM \"__EFMigrationsHistory\"
ORDER BY \"MigrationId\";"

20:57:24 [Information] :: Firebase Admin SDK initialized successfully

20:57:24 [Information] :: RabbitMQ Broker Url: amqps://xruwodqv:<EMAIL>/xruwodqv

20:57:24 [Information] :: RabbitMQ Broker Url: amqps://xruwodqv:<EMAIL>/xruwodqv

20:57:24 [Information] :: User profile is available. Using '"C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys"' as key repository and Windows DPAPI to encrypt keys at rest.

20:57:24 [Information] :: Creating new RabbitMQ connection for queue notification-message-queue

20:57:30 [Information] :: RabbitMQ consumer started successfully for queue notification-message-queue

20:57:30 [Information] :: Started consumer: "NotificationMessageConsumer"

20:57:30 [Information] :: Creating new RabbitMQ connection for queue user-created-queue

20:57:36 [Information] :: RabbitMQ consumer started successfully for queue user-created-queue

20:57:36 [Information] :: Started consumer: "UserCreatedMessageConsumer"

20:57:37 [Information] :: Now listening on: "https://localhost:7044"

20:57:37 [Information] :: Now listening on: "http://localhost:5179"

20:57:37 [Information] :: Application started. Press Ctrl+C to shut down.

20:57:37 [Information] :: Hosting environment: "Development"

20:57:37 [Information] :: Content root path: "C:\Users\<USER>\source\repos\Notification-Service\src\NotificationService.API"

20:57:37 [Information] :: Request starting "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/index.html""" - null null

20:57:37 [Information] :: Request finished "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/index.html""" - 200 null "text/html;charset=utf-8" 235.8832ms

20:57:38 [Information] :: Request starting "HTTP/2" "GET" "https"://"localhost:7044""""/_framework/aspnetcore-browser-refresh.js""" - null null

20:57:38 [Information] :: Request starting "HTTP/2" "GET" "https"://"localhost:7044""""/_vs/browserLink""" - null null

20:57:38 [Information] :: Request finished "HTTP/2" "GET" "https"://"localhost:7044""""/_framework/aspnetcore-browser-refresh.js""" - 200 16531 "application/javascript; charset=utf-8" 25.4524ms

20:57:38 [Information] :: Request finished "HTTP/2" "GET" "https"://"localhost:7044""""/_vs/browserLink""" - 200 null "text/javascript; charset=UTF-8" 86.471ms

20:57:38 [Information] :: Request starting "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/v1/swagger.json""" - null null

20:57:38 [Information] :: Request finished "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/v1/swagger.json""" - 200 null "application/json;charset=utf-8" 52.4572ms

20:58:06 [Information] :: Notification Message "{\"pattern\":\"notification-message-queue\",\"data\":\"{\\"cc\\":[],\\"attachments\\":[],\\"notificationTypes\\":[3],\\"messageId\\":\\"b7008e7e-d984-4c02-b46c-b8fa5fcf5f59\\",\\"priority\\":0,\\"isWhatsappNo\\":false,\\"application\\":1,\\"body\\":\\"c964a01d-d0f3-4553-b53d-07e773d66525 is calling in General\\",\\"subject\\":\\"Incoming audio call\\",\\"userId\\":\\"c964a01d-d0f3-4553-b53d-07e773d66525\\",\\"notificationKey\\":2,\\"templateWithPlaceHolders\\":{\\"template\\":\\"incoming_call\\",\\"props\\":{\\"callId\\":\\"02jjdajqslds\\",\\"callerId\\":\\"6894d2ed1bc552f27cad33b9\\",\\"callerName\\":\\"Chidozie\\",\\"chatId\\":\\"6894d2aa1bc552f27cad331d\\",\\"callType\\":\\"audio\\",\\"chatType\\":\\"CIRCLE\\",\\"key\\":\\"incoming_call\\",\\"initiateCallPayload\\":\\"{\\\\"event\\\\":\\\\"INITIATE_CALL\\\\",\\\\"data\\\\":{\\\\"callerSocket\\\\":\\\\"yHkUsVFqkRP4yp2bAAAc\\\\",\\\\"callerId\\\\":\\\\"6894d2ed1bc552f27cad33b9\\\\",\\\\"callId\\\\":\\\\"02jjdajqslds\\\\",\\\\"callerJobProUserId\\\\":\\\\"c964a01d-d0f3-4553-b53d-07e773d66525\\\\",\\\\"callerName\\\\":\\\\"Osigwe Chidozie\\\\",\\\\"format\\\\":\\\\"audio\\\\",\\\\"type\\\\":\\\\"dm\\\\",\\\\"meetingId\\\\":\\\\"6894d2aa1bc552f27cad331d\\\\",\\\\"title\\\\":\\\\"Osigwe Chidozie\\\\",\\\\"name\\\\":\\\\"Osigwe Chidozie\\\\",\\\\"profilePictureUrl\\\\":\\\\"https://jobpro-public-dev-eu-central-1-010526262538.s3.eu-central-1.amazonaws.com/BB1CDBE9077B5043C70A98A05EA4DEE162F1-oua7kXE5bDF2Y3x9mh4gEi2dSo.png?X-Amz-Expires=604800&X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=AKIAQE43KAUFBUX3QAH7%2F20250805%2Feu-central-1%2Fs3%2Faws4_request&X-Amz-Date=20250805T102022Z&X-Amz-SignedHeaders=host&X-Amz-Signature=6964630ce1cc19b586eea5cc6d68a53da562bbb3f52aab0893339c05ad779bb2\\\\",\\\\"status\\\\":\\\\"INITIATE_CALL\\\\",\\\\"participants\\\\":[\\\\"de56874f-9080-4a54-88c4-265231fa7629\\\\"]}}\\"}}}\"}"

20:58:07 [Information] :: Executed DbCommand ("19"ms) [Parameters=["@__model_UserId_0='?', @__ToString_1='?'"], CommandType='Text', CommandTimeout='30']"
""SELECT p.\"Id\", p.\"AppName\", p.\"CreatedBy\", p.\"CreatedOn\", p.\"IOSPushType\", p.\"Platform\", p.\"Token\", p.\"UpdatedBy\", p.\"UpdatedOn\", p.\"UserId\"
FROM \"PushTokens\" AS p
WHERE p.\"UserId\" = @__model_UserId_0 AND p.\"AppName\" = @__ToString_1"

20:58:07 [Information] :: Executed DbCommand ("17"ms) [Parameters=["@__ToString_0='?'"], CommandType='Text', CommandTimeout='30']"
""SELECT u.\"Id\", u.\"CompanyEmail\", u.\"Country\", u.\"CreatedBy\", u.\"CreatedOn\", u.\"Email\", u.\"FirstName\", u.\"LastName\", u.\"PhoneNumber\", u.\"TimeZone\", u.\"UpdatedBy\", u.\"UpdatedOn\", u.\"UserId\", u.\"Username\", u.\"ZipCode\"
FROM \"Users\" AS u
WHERE u.\"UserId\" = @__ToString_0
LIMIT 1"

20:58:07 [Information] :: Push token for user c964a01d-d0f3-4553-b53d-07e773d66525 and app Joble: c3AMk9G3pRZcKri84Dj_FC:APA91bGVvdz2xOYvcb3VgqshMyAZOjiugdoB0LStTGLbWTYMdihbfcNt1sTc8uUTyet4z3XLOVNJi9wo2VZ5GHYenFw-ahIemLh9G0NR12Og8OaWNdMIJ4g

21:23:24 [Information] :: Executed DbCommand ("38"ms) [Parameters=[""], CommandType='Text', CommandTimeout='30']"
""SELECT \"MigrationId\", \"ProductVersion\"
FROM \"__EFMigrationsHistory\"
ORDER BY \"MigrationId\";"

21:23:25 [Information] :: Initializing Firebase Admin SDK with ProjectId: "jobpro-prod"

21:23:25 [Information] :: Service account file path: "C:\Users\<USER>\source\repos\Notification-Service\src\NotificationService.API\Secrets\jobpro-private-key.json"

21:23:25 [Information] :: Service account file found, reading credentials...

21:23:25 [Information] :: Creating Firebase app with ProjectId: "jobpro-prod"

21:23:25 [Information] :: Firebase Admin SDK initialized successfully for project: "jobpro-prod"

21:23:25 [Information] :: RabbitMQ Broker Url: amqps://xruwodqv:<EMAIL>/xruwodqv

21:23:25 [Information] :: RabbitMQ Broker Url: amqps://xruwodqv:<EMAIL>/xruwodqv

21:23:25 [Information] :: User profile is available. Using '"C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys"' as key repository and Windows DPAPI to encrypt keys at rest.

21:23:25 [Information] :: Creating new RabbitMQ connection for queue notification-message-queue

21:23:32 [Information] :: RabbitMQ consumer started successfully for queue notification-message-queue

21:23:32 [Information] :: Started consumer: "NotificationMessageConsumer"

21:23:32 [Information] :: Creating new RabbitMQ connection for queue user-created-queue

21:23:37 [Information] :: RabbitMQ consumer started successfully for queue user-created-queue

21:23:37 [Information] :: Started consumer: "UserCreatedMessageConsumer"

21:23:38 [Information] :: Now listening on: "https://localhost:7044"

21:23:38 [Information] :: Now listening on: "http://localhost:5179"

21:23:38 [Information] :: Application started. Press Ctrl+C to shut down.

21:23:38 [Information] :: Hosting environment: "Development"

21:23:38 [Information] :: Content root path: "C:\Users\<USER>\source\repos\Notification-Service\src\NotificationService.API"

21:23:39 [Information] :: Request starting "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/index.html""" - null null

21:23:39 [Information] :: Request finished "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/index.html""" - 200 null "text/html;charset=utf-8" 270.7315ms

21:23:39 [Information] :: Request starting "HTTP/2" "GET" "https"://"localhost:7044""""/_vs/browserLink""" - null null

21:23:39 [Information] :: Request starting "HTTP/2" "GET" "https"://"localhost:7044""""/_framework/aspnetcore-browser-refresh.js""" - null null

21:23:39 [Information] :: Request finished "HTTP/2" "GET" "https"://"localhost:7044""""/_framework/aspnetcore-browser-refresh.js""" - 200 16531 "application/javascript; charset=utf-8" 33.2493ms

21:23:39 [Information] :: Request finished "HTTP/2" "GET" "https"://"localhost:7044""""/_vs/browserLink""" - 200 null "text/javascript; charset=UTF-8" 72.5125ms

21:23:40 [Information] :: Request starting "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/v1/swagger.json""" - null null

21:23:40 [Information] :: Request finished "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/v1/swagger.json""" - 200 null "application/json;charset=utf-8" 35.3842ms

21:24:18 [Information] :: Notification Message "{\"pattern\":\"notification-message-queue\",\"data\":\"{\\"cc\\":[],\\"attachments\\":[],\\"notificationTypes\\":[3],\\"messageId\\":\\"5782c1e4-daa6-454b-b8a6-545d686a684a\\",\\"priority\\":0,\\"isWhatsappNo\\":false,\\"application\\":1,\\"body\\":\\"c964a01d-d0f3-4553-b53d-07e773d66525 is calling in General\\",\\"subject\\":\\"Incoming audio call\\",\\"userId\\":\\"c964a01d-d0f3-4553-b53d-07e773d66525\\",\\"notificationKey\\":2,\\"templateWithPlaceHolders\\":{\\"template\\":\\"incoming_call\\",\\"props\\":{\\"callId\\":\\"02jjdajqslds\\",\\"callerId\\":\\"6894d2ed1bc552f27cad33b9\\",\\"callerName\\":\\"Chidozie\\",\\"chatId\\":\\"6894d2aa1bc552f27cad331d\\",\\"callType\\":\\"audio\\",\\"chatType\\":\\"CIRCLE\\",\\"key\\":\\"incoming_call\\",\\"initiateCallPayload\\":\\"{\\\\"event\\\\":\\\\"INITIATE_CALL\\\\",\\\\"data\\\\":{\\\\"callerSocket\\\\":\\\\"yHkUsVFqkRP4yp2bAAAc\\\\",\\\\"callerId\\\\":\\\\"6894d2ed1bc552f27cad33b9\\\\",\\\\"callId\\\\":\\\\"02jjdajqslds\\\\",\\\\"callerJobProUserId\\\\":\\\\"c964a01d-d0f3-4553-b53d-07e773d66525\\\\",\\\\"callerName\\\\":\\\\"Osigwe Chidozie\\\\",\\\\"format\\\\":\\\\"audio\\\\",\\\\"type\\\\":\\\\"dm\\\\",\\\\"meetingId\\\\":\\\\"6894d2aa1bc552f27cad331d\\\\",\\\\"title\\\\":\\\\"Osigwe Chidozie\\\\",\\\\"name\\\\":\\\\"Osigwe Chidozie\\\\",\\\\"profilePictureUrl\\\\":\\\\"https://jobpro-public-dev-eu-central-1-010526262538.s3.eu-central-1.amazonaws.com/BB1CDBE9077B5043C70A98A05EA4DEE162F1-oua7kXE5bDF2Y3x9mh4gEi2dSo.png?X-Amz-Expires=604800&X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=AKIAQE43KAUFBUX3QAH7%2F20250805%2Feu-central-1%2Fs3%2Faws4_request&X-Amz-Date=20250805T102022Z&X-Amz-SignedHeaders=host&X-Amz-Signature=6964630ce1cc19b586eea5cc6d68a53da562bbb3f52aab0893339c05ad779bb2\\\\",\\\\"status\\\\":\\\\"INITIATE_CALL\\\\",\\\\"participants\\\\":[\\\\"de56874f-9080-4a54-88c4-265231fa7629\\\\"]}}\\"}}}\"}"

21:24:19 [Information] :: Executed DbCommand ("19"ms) [Parameters=["@__model_UserId_0='?', @__ToString_1='?'"], CommandType='Text', CommandTimeout='30']"
""SELECT p.\"Id\", p.\"AppName\", p.\"CreatedBy\", p.\"CreatedOn\", p.\"IOSPushType\", p.\"Platform\", p.\"Token\", p.\"UpdatedBy\", p.\"UpdatedOn\", p.\"UserId\"
FROM \"PushTokens\" AS p
WHERE p.\"UserId\" = @__model_UserId_0 AND p.\"AppName\" = @__ToString_1"

21:24:19 [Information] :: Executed DbCommand ("14"ms) [Parameters=["@__ToString_0='?'"], CommandType='Text', CommandTimeout='30']"
""SELECT u.\"Id\", u.\"CompanyEmail\", u.\"Country\", u.\"CreatedBy\", u.\"CreatedOn\", u.\"Email\", u.\"FirstName\", u.\"LastName\", u.\"PhoneNumber\", u.\"TimeZone\", u.\"UpdatedBy\", u.\"UpdatedOn\", u.\"UserId\", u.\"Username\", u.\"ZipCode\"
FROM \"Users\" AS u
WHERE u.\"UserId\" = @__ToString_0
LIMIT 1"

21:24:19 [Information] :: Push token for user c964a01d-d0f3-4553-b53d-07e773d66525 and app Joble: c3AMk9G3pRZcKri84Dj_FC:APA91bGVvdz2xOYvcb3VgqshMyAZOjiugdoB0LStTGLbWTYMdihbfcNt1sTc8uUTyet4z3XLOVNJi9wo2VZ5GHYenFw-ahIemLh9G0NR12Og8OaWNdMIJ4g

21:27:22 [Error] :: Firebase messaging error: PermissionDenied - "SenderId mismatch"
FirebaseAdmin.Messaging.FirebaseMessagingException: SenderId mismatch
   at FirebaseAdmin.Util.ErrorHandlingHttpClient`1.SendAndReadAsync(HttpRequestMessage request, CancellationToken cancellationToken)
   at FirebaseAdmin.Util.ErrorHandlingHttpClient`1.SendAndDeserializeAsync[TResult](HttpRequestMessage request, CancellationToken cancellationToken)
   at FirebaseAdmin.Messaging.FirebaseMessagingClient.SendAsync(Message message, Boolean dryRun, CancellationToken cancellationToken)
   at FirebaseAdmin.Messaging.FirebaseMessaging.SendAsync(Message message, Boolean dryRun, CancellationToken cancellationToken)
   at FirebaseAdmin.Messaging.FirebaseMessaging.SendAsync(Message message, Boolean dryRun)
   at FirebaseAdmin.Messaging.FirebaseMessaging.SendAsync(Message message)
   at NotificationService.Infrastructure.Services.FirebasePushNotificationService.SendMessage(NotificationMessageModel model)

21:27:22 [Error] :: SenderId mismatch detected. Please verify:

21:27:22 [Error] :: 1. ProjectId in configuration matches the Firebase project: "jobpro-prod"

21:27:22 [Error] :: 2. Service account JSON file is from the correct Firebase project

21:27:22 [Error] :: 3. Service account file path: "C:\Users\<USER>\source\repos\Notification-Service\src\NotificationService.API\Secrets\jobpro-private-key.json"

21:27:23 [Information] :: Executed DbCommand ("24"ms) [Parameters=["@p0='?', @p1='?', @p2='?' (DbType = DateTime), @p3='?', @p4='?', @p5='?', @p6='?', @p7='?', @p8='?', @p9='?', @p10='?', @p11='?', @p12='?', @p13='?', @p14='?', @p15='?', @p16='?', @p17='?' (DbType = DateTime), @p18='?'"], CommandType='Text', CommandTimeout='30']"
""INSERT INTO \"Notifications\" (\"Body\", \"CreatedBy\", \"CreatedOn\", \"Email\", \"MessageId\", \"MessageSid\", \"NotificationType\", \"PhoneNumber\", \"Priority\", \"PushToken\", \"ResponseId\", \"Status\", \"StatusCode\", \"StatusMessage\", \"Subject\", \"Template\", \"UpdatedBy\", \"UpdatedOn\", \"UserId\")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16, @p17, @p18)
RETURNING \"Id\";"

21:27:23 [Information] :: notification-message-queue message received and processed: {"pattern":"notification-message-queue","data":"{\"cc\":[],\"attachments\":[],\"notificationTypes\":[3],\"messageId\":\"5782c1e4-daa6-454b-b8a6-545d686a684a\",\"priority\":0,\"isWhatsappNo\":false,\"application\":1,\"body\":\"c964a01d-d0f3-4553-b53d-07e773d66525 is calling in General\",\"subject\":\"Incoming audio call\",\"userId\":\"c964a01d-d0f3-4553-b53d-07e773d66525\",\"notificationKey\":2,\"templateWithPlaceHolders\":{\"template\":\"incoming_call\",\"props\":{\"callId\":\"02jjdajqslds\",\"callerId\":\"6894d2ed1bc552f27cad33b9\",\"callerName\":\"Chidozie\",\"chatId\":\"6894d2aa1bc552f27cad331d\",\"callType\":\"audio\",\"chatType\":\"CIRCLE\",\"key\":\"incoming_call\",\"initiateCallPayload\":\"{\\\"event\\\":\\\"INITIATE_CALL\\\",\\\"data\\\":{\\\"callerSocket\\\":\\\"yHkUsVFqkRP4yp2bAAAc\\\",\\\"callerId\\\":\\\"6894d2ed1bc552f27cad33b9\\\",\\\"callId\\\":\\\"02jjdajqslds\\\",\\\"callerJobProUserId\\\":\\\"c964a01d-d0f3-4553-b53d-07e773d66525\\\",\\\"callerName\\\":\\\"Osigwe Chidozie\\\",\\\"format\\\":\\\"audio\\\",\\\"type\\\":\\\"dm\\\",\\\"meetingId\\\":\\\"6894d2aa1bc552f27cad331d\\\",\\\"title\\\":\\\"Osigwe Chidozie\\\",\\\"name\\\":\\\"Osigwe Chidozie\\\",\\\"profilePictureUrl\\\":\\\"https://jobpro-public-dev-eu-central-1-010526262538.s3.eu-central-1.amazonaws.com/BB1CDBE9077B5043C70A98A05EA4DEE162F1-oua7kXE5bDF2Y3x9mh4gEi2dSo.png?X-Amz-Expires=604800&X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=AKIAQE43KAUFBUX3QAH7%2F20250805%2Feu-central-1%2Fs3%2Faws4_request&X-Amz-Date=20250805T102022Z&X-Amz-SignedHeaders=host&X-Amz-Signature=6964630ce1cc19b586eea5cc6d68a53da562bbb3f52aab0893339c05ad779bb2\\\",\\\"status\\\":\\\"INITIATE_CALL\\\",\\\"participants\\\":[\\\"de56874f-9080-4a54-88c4-265231fa7629\\\"]}\"}}"}

21:27:23 [Error] :: Error processing message in queue "notification-message-queue"
RabbitMQ.Client.Exceptions.AlreadyClosedException: Already closed: The AMQP operation was interrupted: AMQP close-reason, initiated by Library, code=0, text='End of stream', classId=0, methodId=0, exception=System.IO.EndOfStreamException: Reached the end of the stream. Possible authentication failure.
   at RabbitMQ.Client.Impl.InboundFrame.ReadFrom(Stream reader, Byte[] frameHeaderBuffer, ArrayPool`1 pool, UInt32 maxMessageSize)
   at RabbitMQ.Client.Impl.SocketFrameHandler.ReadFrame()
   at RabbitMQ.Client.Framing.Impl.Connection.MainLoopIteration()
   at RabbitMQ.Client.Framing.Impl.Connection.MainLoop()
   at RabbitMQ.Client.Impl.SessionBase.Transmit(OutgoingCommand& cmd)
   at RabbitMQ.Client.Impl.ModelBase.ModelSend(MethodBase method, ContentHeaderBase header, ReadOnlyMemory`1 body)
   at RabbitMQ.Client.Framing.Impl.Model.BasicAck(UInt64 deliveryTag, Boolean multiple)
   at RabbitMQ.Client.Impl.RecoveryAwareModel.BasicAck(UInt64 deliveryTag, Boolean multiple)
   at RabbitMQ.Client.Impl.AutorecoveringModel.BasicAck(UInt64 deliveryTag, Boolean multiple)
   at NotificationService.Infrastructure.Services.BaseConsumer.<StartConsumer>b__9_0(Object model, BasicDeliverEventArgs eventArgs)

21:27:23 [Information] :: Request starting "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/index.html""" - null null

21:27:23 [Information] :: Request finished "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/index.html""" - 200 null "text/html;charset=utf-8" 99.289ms

21:27:24 [Information] :: Request starting "HTTP/2" "GET" "https"://"localhost:7044""""/_framework/aspnetcore-browser-refresh.js""" - null null

21:27:24 [Information] :: Request finished "HTTP/2" "GET" "https"://"localhost:7044""""/_framework/aspnetcore-browser-refresh.js""" - 200 16531 "application/javascript; charset=utf-8" 17.1359ms

21:27:24 [Information] :: Request starting "HTTP/2" "GET" "https"://"localhost:7044""""/_vs/browserLink""" - null null

21:27:24 [Information] :: Request finished "HTTP/2" "GET" "https"://"localhost:7044""""/_vs/browserLink""" - 200 null "text/javascript; charset=UTF-8" 78.9233ms

21:27:24 [Information] :: Request starting "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/v1/swagger.json""" - null null

21:27:24 [Information] :: Request finished "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/v1/swagger.json""" - 200 null "application/json;charset=utf-8" 43.0512ms

21:28:02 [Information] :: Notification Message "{\"pattern\":\"notification-message-queue\",\"data\":\"{\\"cc\\":[],\\"attachments\\":[],\\"notificationTypes\\":[3],\\"messageId\\":\\"99d85add-5097-44d3-9924-95982bed2307\\",\\"priority\\":0,\\"isWhatsappNo\\":false,\\"application\\":1,\\"body\\":\\"c964a01d-d0f3-4553-b53d-07e773d66525 is calling in General\\",\\"subject\\":\\"Incoming audio call\\",\\"userId\\":\\"c964a01d-d0f3-4553-b53d-07e773d66525\\",\\"notificationKey\\":2,\\"templateWithPlaceHolders\\":{\\"template\\":\\"incoming_call\\",\\"props\\":{\\"callId\\":\\"02jjdajqslds\\",\\"callerId\\":\\"6894d2ed1bc552f27cad33b9\\",\\"callerName\\":\\"Chidozie\\",\\"chatId\\":\\"6894d2aa1bc552f27cad331d\\",\\"callType\\":\\"audio\\",\\"chatType\\":\\"CIRCLE\\",\\"key\\":\\"incoming_call\\",\\"initiateCallPayload\\":\\"{\\\\"event\\\\":\\\\"INITIATE_CALL\\\\",\\\\"data\\\\":{\\\\"callerSocket\\\\":\\\\"yHkUsVFqkRP4yp2bAAAc\\\\",\\\\"callerId\\\\":\\\\"6894d2ed1bc552f27cad33b9\\\\",\\\\"callId\\\\":\\\\"02jjdajqslds\\\\",\\\\"callerJobProUserId\\\\":\\\\"c964a01d-d0f3-4553-b53d-07e773d66525\\\\",\\\\"callerName\\\\":\\\\"Osigwe Chidozie\\\\",\\\\"format\\\\":\\\\"audio\\\\",\\\\"type\\\\":\\\\"dm\\\\",\\\\"meetingId\\\\":\\\\"6894d2aa1bc552f27cad331d\\\\",\\\\"title\\\\":\\\\"Osigwe Chidozie\\\\",\\\\"name\\\\":\\\\"Osigwe Chidozie\\\\",\\\\"profilePictureUrl\\\\":\\\\"https://jobpro-public-dev-eu-central-1-010526262538.s3.eu-central-1.amazonaws.com/BB1CDBE9077B5043C70A98A05EA4DEE162F1-oua7kXE5bDF2Y3x9mh4gEi2dSo.png?X-Amz-Expires=604800&X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=AKIAQE43KAUFBUX3QAH7%2F20250805%2Feu-central-1%2Fs3%2Faws4_request&X-Amz-Date=20250805T102022Z&X-Amz-SignedHeaders=host&X-Amz-Signature=6964630ce1cc19b586eea5cc6d68a53da562bbb3f52aab0893339c05ad779bb2\\\\",\\\\"status\\\\":\\\\"INITIATE_CALL\\\\",\\\\"participants\\\\":[\\\\"de56874f-9080-4a54-88c4-265231fa7629\\\\"]}}\\"}}}\"}"

21:28:02 [Information] :: Executed DbCommand ("1"ms) [Parameters=["@__model_UserId_0='?', @__ToString_1='?'"], CommandType='Text', CommandTimeout='30']"
""SELECT p.\"Id\", p.\"AppName\", p.\"CreatedBy\", p.\"CreatedOn\", p.\"IOSPushType\", p.\"Platform\", p.\"Token\", p.\"UpdatedBy\", p.\"UpdatedOn\", p.\"UserId\"
FROM \"PushTokens\" AS p
WHERE p.\"UserId\" = @__model_UserId_0 AND p.\"AppName\" = @__ToString_1"

21:28:02 [Information] :: Executed DbCommand ("1"ms) [Parameters=["@__ToString_0='?'"], CommandType='Text', CommandTimeout='30']"
""SELECT u.\"Id\", u.\"CompanyEmail\", u.\"Country\", u.\"CreatedBy\", u.\"CreatedOn\", u.\"Email\", u.\"FirstName\", u.\"LastName\", u.\"PhoneNumber\", u.\"TimeZone\", u.\"UpdatedBy\", u.\"UpdatedOn\", u.\"UserId\", u.\"Username\", u.\"ZipCode\"
FROM \"Users\" AS u
WHERE u.\"UserId\" = @__ToString_0
LIMIT 1"

21:28:02 [Information] :: Push token for user c964a01d-d0f3-4553-b53d-07e773d66525 and app Joble: c3AMk9G3pRZcKri84Dj_FC:APA91bGVvdz2xOYvcb3VgqshMyAZOjiugdoB0LStTGLbWTYMdihbfcNt1sTc8uUTyet4z3XLOVNJi9wo2VZ5GHYenFw-ahIemLh9G0NR12Og8OaWNdMIJ4g
