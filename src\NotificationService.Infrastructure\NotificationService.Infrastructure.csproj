﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<OutputType>Library</OutputType>
		<TargetFramework>net8.0</TargetFramework>
		<ImplicitUsings>enable</ImplicitUsings>
		<Nullable>enable</Nullable>
	</PropertyGroup>

	<PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
		<NoWarn>1701;1702,8602,8604,8601,8613</NoWarn>
	</PropertyGroup>

	<PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
		<NoWarn>1701;1702,8602,8604,8601,8613</NoWarn>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="AfricasTalking.NET" Version="1.2.4" />
		<PackageReference Include="AutoMapper" Version="13.0.1" />
		<PackageReference Include="FirebaseAdmin" Version="3.3.0" />
		<PackageReference Include="Google.Apis.Auth" Version="1.68.0" />
		<PackageReference Include="Google.Cloud.Storage.V1" Version="4.7.0" />
		<PackageReference Include="JobPro.Common.Services" Version="1.0.0" />
		<PackageReference Include="MailKit" Version="4.3.0" />
		<PackageReference Include="Microsoft.EntityFrameworkCore" Version="9.0.2" />
		<PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="9.0.2">
			<PrivateAssets>all</PrivateAssets>
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		</PackageReference>
		<PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="9.0.2">
			<PrivateAssets>all</PrivateAssets>
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		</PackageReference>
		<PackageReference Include="Microsoft.Extensions.Configuration.Abstractions" Version="9.0.2" />
		<PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="9.0.2" />
		<PackageReference Include="Microsoft.Extensions.Hosting.Abstractions" Version="9.0.2" />
		<PackageReference Include="MimeKit" Version="4.3.0" />
		<PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
		<PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL" Version="9.0.4" />
		<PackageReference Include="RabbitMQ.Client" Version="6.8.1" />
		<PackageReference Include="RazorLight" Version="2.3.1" />
		<PackageReference Include="RestSharp" Version="110.2.0" />
		<PackageReference Include="Serilog.AspNetCore" Version="7.0.0" />
		<PackageReference Include="Serilog.Sinks.File" Version="5.0.0" />
		<PackageReference Include="Twilio" Version="6.16.1" />
		<PackageReference Include="WatchDog.NET" Version="1.4.10" />
		<PackageReference Include="MassTransit.Kafka" Version="8.2.5" />
    <PackageReference Include="MassTransit.AmazonSQS" Version="8.3.4" />
    <PackageReference Include="MassTransit" Version="8.3.4" />
    <PackageReference Include="AWSSDK.Core" Version="3.7.400.70" />
    <PackageReference Include="AWSSDK.Extensions.NETCore.Setup" Version="3.7.301" />
    <PackageReference Include="AWSSDK.S3" Version="3.7.410.11" />
    <PackageReference Include="Kralizek.Extensions.Configuration.AWSSecretsManager" Version="1.7.0" />
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\NotificationService.Core\NotificationService.Core.csproj" />
	</ItemGroup>

</Project>
