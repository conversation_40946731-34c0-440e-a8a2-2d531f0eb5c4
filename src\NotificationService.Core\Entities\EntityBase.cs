﻿using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;

namespace NotificationService.Core.Entities
{
    [Index(nameof(CreatedBy))]
    [Index(nameof(CreatedOn))]
    public class EntityBase
    {
        [Key]
        public long Id { get; set; }
        public string? CreatedBy { get; set; }
        public DateTime CreatedOn { get; set; }
        public string? UpdatedBy { get; set; }
        public DateTime UpdatedOn { get; set; }

        public virtual void OnBeforeInsert()
        {
            UserContext user = UserContext.Current;
            if (user != null)
            {
                CreatedBy = user.Username;
            }
            CreatedOn = DateTime.UtcNow;
        }

        public virtual void OnBeforeUpdate()
        {
            UserContext user = UserContext.Current;
            if (user != null)
            {
                UpdatedBy = user.Username;
            }
            UpdatedOn = DateTime.UtcNow;
        }
    }
}
