﻿using NotificationService.Core.Constants;
using NotificationService.Core.Entities;

namespace NotificationService.Core.Models
{
    public class NotificationMessageModel
    {
        public string MessageId { get; set; }

        // This is the message body for sms and push notification
        public string? Body { get; set; }

        // For templates with actual params
        public string? TemplateWithParams { get; set; }
        public string? Subject { get; set; }
        public List<string>? RecipientEmails { get; set; }
        public string? PhoneNumber { get; set; }
        public string? PushToken { get; set; }
        public List<PushToken>? PushTokens { get; set; }
        public List<string> CC { get; set; }
        public string? UserId { get; set; }
        public Application Application { get; set; }
        public List<Attachments> Attachments { get; set; }
        public List<NotificationType> NotificationTypes { get; set; }
        public NotificationPriority Priority { get; set; }
        public bool IsWhatsappNo { get; set; }
        // For templates with params placeholders
        public MessageTemplate? TemplateWithPlaceHolders { get; set; }
        public string? ImageUrl { get; set; }
        public Platform? Platform { get; set; }
        public IOSPushType? IOSPushType { get; set; } = null;
        public NotificationKey? NotificationKey { get; set; }

        public NotificationMessageModel()
        {
            CC = new List<string>();
            NotificationTypes = new List<NotificationType>();
            Attachments = new List<Attachments>();
            IsWhatsappNo = false;
            Priority = NotificationPriority.High;
            MessageId = Guid.NewGuid().ToString();
        }
    }

    public class NotificationMessageWrapper
    {
        public string Pattern { get; set; }
        public string Data { get; set; }
    }

    public class Attachments
    {
        public string? FileName { get; set; }
        public string Base64Content { get; set; } = default!;
    }

    public class Message
    {
        public string? MessageId { get; set; }
        public string? UserId { get; set; }
    }

    public class MessageTemplate
    {
        public string? Template { get; set; }
        public Dictionary<string, string>? Props { get; set; }
        public Dictionary<string, object>? AdditionalProps { get; set; }
    }


    public class FirebaseMessage
    {
        public string token { get; set; } 
        public FirebaseNotification notification { get; set; }
    }

    public class FirebaseNotification
    {
        public string body { get; set; }
        public string title { get; set; }
        public string image { get; set; }
    }

    public class FBNotification
    {
        public FirebaseMessage message { get; set; }
    }

}