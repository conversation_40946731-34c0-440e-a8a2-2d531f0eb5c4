{"openapi": "3.0.1", "info": {"title": "NotificationService.API", "version": "1.0"}, "paths": {"/api/HealthCheck/get-all-env-variables": {"post": {"tags": ["HealthCheck"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ViewEnvVariablesDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ViewEnvVariablesDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ViewEnvVariablesDto"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/Notification/send": {"post": {"tags": ["Notification"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/NotificationMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/NotificationMessageModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/NotificationMessageModel"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StringApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StringApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StringApiResponse"}}}}}}}, "/api/Notification/queue": {"post": {"tags": ["Notification"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/NotificationMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/NotificationMessageModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/NotificationMessageModel"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StringApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StringApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StringApiResponse"}}}}}}}, "/api/Test/create": {"post": {"tags": ["Test"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateUserModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateUserModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateUserModel"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StringApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StringApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StringApiResponse"}}}}}}}, "/api/User/create-push-token": {"post": {"tags": ["User"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PushTokenModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PushTokenModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/PushTokenModel"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StringApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StringApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StringApiResponse"}}}}}}}}, "components": {"schemas": {"Application": {"enum": ["<PERSON><PERSON>", "JobPays", "Echo", "JobEvent", "JobID", "CaringBoss", "Jobfy"], "type": "string"}, "Attachments": {"type": "object", "properties": {"FileName": {"type": "string", "nullable": true}, "Base64Content": {"type": "string", "nullable": true}}, "additionalProperties": false}, "CreateUserModel": {"type": "object", "properties": {"UserId": {"type": "string", "nullable": true}, "Username": {"type": "string", "nullable": true}, "PhoneNumber": {"type": "string", "nullable": true}, "Email": {"type": "string", "nullable": true}, "FirstName": {"type": "string", "nullable": true}, "LastName": {"type": "string", "nullable": true}, "CompanyEmail": {"type": "string", "nullable": true}, "ZipCode": {"type": "string", "nullable": true}, "Country": {"type": "string", "nullable": true}, "TimeZone": {"type": "string", "nullable": true}}, "additionalProperties": false}, "IOSPushType": {"enum": ["<PERSON><PERSON>", "Voip"], "type": "string"}, "MessageTemplate": {"type": "object", "properties": {"Template": {"type": "string", "nullable": true}, "Props": {"type": "object", "additionalProperties": {"type": "string"}, "nullable": true}, "AdditionalProps": {"type": "object", "additionalProperties": {}, "nullable": true}}, "additionalProperties": false}, "NotificationKey": {"enum": ["Cha<PERSON>", "Call"], "type": "string"}, "NotificationMessageModel": {"type": "object", "properties": {"MessageId": {"type": "string", "nullable": true}, "Body": {"type": "string", "nullable": true}, "TemplateWithParams": {"type": "string", "nullable": true}, "Subject": {"type": "string", "nullable": true}, "RecipientEmails": {"type": "array", "items": {"type": "string"}, "nullable": true}, "PhoneNumber": {"type": "string", "nullable": true}, "PushToken": {"type": "string", "nullable": true}, "PushTokens": {"type": "array", "items": {"$ref": "#/components/schemas/PushToken"}, "nullable": true}, "CC": {"type": "array", "items": {"type": "string"}, "nullable": true}, "UserId": {"type": "string", "nullable": true}, "Application": {"$ref": "#/components/schemas/Application"}, "Attachments": {"type": "array", "items": {"$ref": "#/components/schemas/Attachments"}, "nullable": true}, "NotificationTypes": {"type": "array", "items": {"$ref": "#/components/schemas/NotificationType"}, "nullable": true}, "Priority": {"$ref": "#/components/schemas/NotificationPriority"}, "IsWhatsappNo": {"type": "boolean"}, "TemplateWithPlaceHolders": {"$ref": "#/components/schemas/MessageTemplate"}, "ImageUrl": {"type": "string", "nullable": true}, "Platform": {"$ref": "#/components/schemas/Platform"}, "IOSPushType": {"$ref": "#/components/schemas/IOSPushType"}, "NotificationKey": {"$ref": "#/components/schemas/NotificationKey"}}, "additionalProperties": false}, "NotificationPriority": {"enum": [1, 2, 3], "type": "integer", "format": "int32"}, "NotificationType": {"enum": ["Email", "Sms", "<PERSON><PERSON>", "Web"], "type": "string"}, "Platform": {"enum": ["Andriod", "IOS", "Web"], "type": "string"}, "PushToken": {"type": "object", "properties": {"Id": {"type": "integer", "format": "int64"}, "CreatedBy": {"type": "string", "nullable": true}, "CreatedOn": {"type": "string", "format": "date-time"}, "UpdatedBy": {"type": "string", "nullable": true}, "UpdatedOn": {"type": "string", "format": "date-time"}, "AppName": {"type": "string", "nullable": true}, "UserId": {"type": "string", "nullable": true}, "Token": {"type": "string", "nullable": true}, "Platform": {"$ref": "#/components/schemas/Platform"}, "IOSPushType": {"$ref": "#/components/schemas/IOSPushType"}}, "additionalProperties": false}, "PushTokenModel": {"required": ["Application", "Token", "userId"], "type": "object", "properties": {"Application": {"$ref": "#/components/schemas/Application"}, "Token": {"minLength": 1, "type": "string"}, "userId": {"minLength": 1, "type": "string"}, "Subdomain": {"type": "string", "nullable": true}, "Platform": {"$ref": "#/components/schemas/Platform"}, "IOSPushType": {"$ref": "#/components/schemas/IOSPushType"}}, "additionalProperties": false}, "StringApiResponse": {"type": "object", "properties": {"Status": {"type": "integer", "format": "int32"}, "Message": {"type": "string", "nullable": true}, "DevMessage": {"type": "string", "nullable": true}, "Data": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ViewEnvVariablesDto": {"required": ["Key", "Password", "Username"], "type": "object", "properties": {"Username": {"minLength": 1, "type": "string"}, "Password": {"minLength": 1, "type": "string"}, "Key": {"minLength": 1, "type": "string"}}, "additionalProperties": false}}}}