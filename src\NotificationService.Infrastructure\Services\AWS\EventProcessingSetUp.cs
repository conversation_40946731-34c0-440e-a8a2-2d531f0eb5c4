﻿using Amazon.SimpleNotificationService;
using Amazon.SimpleNotificationService.Model;
using Amazon.SQS;
using Amazon.SQS.Model;
using Microsoft.Extensions.Configuration;
using Serilog;
using System.Net;
using System.Text.Json;
using ILogger = Serilog.ILogger;

namespace NotificationService.Infrastructure.Services.AWS
{
    public interface IEventProcessingSetUp
    {
        Task<(ReceiveMessageResponse? MessageResponse, GetQueueUrlResponse? QueueUrlResponse)> CreateQueueAndNotificationAsync(string topic, CancellationToken cancellationToken);
        Task DeleteMessage(GetQueueUrlResponse queue, Message? message, CancellationToken cancellationToken);
    }

    public class EventProcessingSetUp : IEventProcessingSetUp
    {
        private readonly IAmazonSQS _sqs;
        private readonly IAmazonSimpleNotificationService _sns;
        private readonly List<string> _messageAttributeNames = new() { "All" };
        private readonly ILogger _logger = Log.ForContext<EventProcessingSetUp>();

        public EventProcessingSetUp(
            IAmazonSQS sqs,
        IConfiguration configuration,
        IAmazonSimpleNotificationService sns)
        {
            _sqs = sqs;
            _sns = sns;       
        }

        public async Task<(ReceiveMessageResponse? MessageResponse, GetQueueUrlResponse? QueueUrlResponse)> CreateQueueAndNotificationAsync(string topic, CancellationToken cancellationToken)
        {
            GetQueueUrlResponse queue = null;
            var queueCreationRes = await _sqs.CreateQueueAsync(topic, cancellationToken);
            if (queueCreationRes.HttpStatusCode != HttpStatusCode.OK)
            {
                _logger.Error("notificationservice:Failed to create queue for topic {topic}", topic);
                return (null, null);
            }
            else
            {
                queue = await _sqs.GetQueueUrlAsync($"{topic}", cancellationToken);
            }

            // Check if the SNS topic exists
            var topicArn = "";
            var snsTopicArn = await _sns.FindTopicAsync(topic);
            if (snsTopicArn is null)
            {
                _logger.Error("notificationService:Failed to create SNS topic for {topic}", topic);
                return (null, queue);
            }
            else
            {
                topicArn = snsTopicArn.TopicArn;
            }

            // Subscribe the SQS queue to the SNS topic
            var attributesResponse = await _sqs.GetQueueAttributesAsync(
                queue.QueueUrl,
                new List<string> { "QueueArn" },
                cancellationToken);

            // Update queue policy with specific ARNs
            var updatedPolicy = JsonSerializer.Serialize(new
            {
                Version = "2012-10-17",
                Statement = new[]
                {
                    new
                        {
                            Effect = "Allow",
                            Principal = new { Service = "sns.amazonaws.com" },
                            Action = "sqs:SendMessage",
                            Resource = attributesResponse.Attributes["QueueArn"],
                            Condition = new
                            {
                                ArnEquals = new Dictionary<string, string>
                                {
                                    ["aws:SourceArn"] = snsTopicArn.TopicArn
                                }
                            }
                        }
                    }
            });

            // Set the updated policy
            await _sqs.SetQueueAttributesAsync(queue.QueueUrl, new Dictionary<string, string>
            {
                ["Policy"] = updatedPolicy
            }, cancellationToken);

            var subscribeRequest = new SubscribeRequest
            {
                TopicArn = topicArn,
                Protocol = "sqs",
                Endpoint = attributesResponse.Attributes["QueueArn"]
            };

            var subscribeResponse = await _sns.SubscribeAsync(subscribeRequest, cancellationToken);
            if (subscribeResponse.HttpStatusCode != HttpStatusCode.OK)
            {
                _logger.Error("notificationService:Failed to subscribe queue to SNS topic for {topic}", topic);
                return (null, queue);
            }

            var receiveRequest = new ReceiveMessageRequest
            {
                QueueUrl = queue.QueueUrl,
                MessageAttributeNames = _messageAttributeNames,
                MessageSystemAttributeNames = _messageAttributeNames
            };

            var messageResponse = await _sqs.ReceiveMessageAsync(receiveRequest, cancellationToken);
            if (messageResponse.HttpStatusCode != HttpStatusCode.OK)
            {
                _logger.Error("notificationService:Failed to receive message from queue for {topic}", topic);
                return (null, queue);
            }

            return (messageResponse, queue);
        }

        public async Task DeleteMessage(GetQueueUrlResponse queue, Message? message, CancellationToken cancellationToken)
        {
            try
            {
                await _sqs.DeleteMessageAsync(queue.QueueUrl, message.ReceiptHandle, cancellationToken);
            }
            catch (Exception ex)
            {
                _logger.Error("notificationService:Failed to delete message from queue {queue} with message {message}", queue.QueueUrl, message.MessageId);
            }
        }
    }
}
