﻿using Microsoft.EntityFrameworkCore.Design;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;


namespace NotificationService.Infrastructure.Repositories
{
    public class DesignTimeDbContextFactory : IDesignTimeDbContextFactory<NotificationDbContext>
    {

        public NotificationDbContext CreateDbContext(string[] args)
        {
            IConfigurationRoot configuration = new ConfigurationBuilder()
                .SetBasePath(Directory.GetCurrentDirectory())
                .AddJsonFile("appsettings.json")
                .Build();

            var builder = new DbContextOptionsBuilder<NotificationDbContext>();
            var connectionString = configuration.GetConnectionString("NotificationConnection");

            builder.UseNpgsql(connectionString);

            return new NotificationDbContext(builder.Options);
        }
    }
}
