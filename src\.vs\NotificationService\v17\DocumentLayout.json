{"Version": 1, "WorkspaceRootPath": "C:\\Users\\<USER>\\source\\repos\\Notification-Service\\src\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{9717B996-7C5A-4665-857E-1C79FFF89CE7}|NotificationService.Infrastructure\\NotificationService.Infrastructure.csproj|c:\\users\\<USER>\\source\\repos\\notification-service\\src\\notificationservice.infrastructure\\services\\apnsservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{9717B996-7C5A-4665-857E-1C79FFF89CE7}|NotificationService.Infrastructure\\NotificationService.Infrastructure.csproj|solutionrelative:notificationservice.infrastructure\\services\\apnsservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{0C135DB8-1AA1-4D30-8626-9E8C124F7571}|NotificationService.API\\NotificationService.API.csproj|c:\\users\\<USER>\\source\\repos\\notification-service\\src\\notificationservice.api\\appsettings.development.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{0C135DB8-1AA1-4D30-8626-9E8C124F7571}|NotificationService.API\\NotificationService.API.csproj|solutionrelative:notificationservice.api\\appsettings.development.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{9717B996-7C5A-4665-857E-1C79FFF89CE7}|NotificationService.Infrastructure\\NotificationService.Infrastructure.csproj|c:\\users\\<USER>\\source\\repos\\notification-service\\src\\notificationservice.infrastructure\\services\\firebasepushmessageservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{9717B996-7C5A-4665-857E-1C79FFF89CE7}|NotificationService.Infrastructure\\NotificationService.Infrastructure.csproj|solutionrelative:notificationservice.infrastructure\\services\\firebasepushmessageservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{9717B996-7C5A-4665-857E-1C79FFF89CE7}|NotificationService.Infrastructure\\NotificationService.Infrastructure.csproj|c:\\users\\<USER>\\source\\repos\\notification-service\\src\\notificationservice.infrastructure\\services\\baseconsumer.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{9717B996-7C5A-4665-857E-1C79FFF89CE7}|NotificationService.Infrastructure\\NotificationService.Infrastructure.csproj|solutionrelative:notificationservice.infrastructure\\services\\baseconsumer.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{9717B996-7C5A-4665-857E-1C79FFF89CE7}|NotificationService.Infrastructure\\NotificationService.Infrastructure.csproj|c:\\users\\<USER>\\source\\repos\\notification-service\\src\\notificationservice.infrastructure\\services\\notificationservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{9717B996-7C5A-4665-857E-1C79FFF89CE7}|NotificationService.Infrastructure\\NotificationService.Infrastructure.csproj|solutionrelative:notificationservice.infrastructure\\services\\notificationservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{9717B996-7C5A-4665-857E-1C79FFF89CE7}|NotificationService.Infrastructure\\NotificationService.Infrastructure.csproj|c:\\users\\<USER>\\source\\repos\\notification-service\\src\\notificationservice.infrastructure\\services\\rabbitmqbrokerservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{9717B996-7C5A-4665-857E-1C79FFF89CE7}|NotificationService.Infrastructure\\NotificationService.Infrastructure.csproj|solutionrelative:notificationservice.infrastructure\\services\\rabbitmqbrokerservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 11, "Children": [{"$type": "Document", "DocumentIndex": 4, "Title": "NotificationService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Notification-Service\\src\\NotificationService.Infrastructure\\Services\\NotificationService.cs", "RelativeDocumentMoniker": "NotificationService.Infrastructure\\Services\\NotificationService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Notification-Service\\src\\NotificationService.Infrastructure\\Services\\NotificationService.cs", "RelativeToolTip": "NotificationService.Infrastructure\\Services\\NotificationService.cs", "ViewState": "AgIAACUAAAAAAAAAAAA3wCUAAAAuAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-10-22T10:03:21.754Z", "IsPinned": true, "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "FirebasePushMessageService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Notification-Service\\src\\NotificationService.Infrastructure\\Services\\FirebasePushMessageService.cs", "RelativeDocumentMoniker": "NotificationService.Infrastructure\\Services\\FirebasePushMessageService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Notification-Service\\src\\NotificationService.Infrastructure\\Services\\FirebasePushMessageService.cs*", "RelativeToolTip": "NotificationService.Infrastructure\\Services\\FirebasePushMessageService.cs*", "ViewState": "AgIAAFoAAAAAAAAAAAAgwGYAAAAUAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-14T14:54:48.255Z", "IsPinned": true, "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "BaseConsumer.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Notification-Service\\src\\NotificationService.Infrastructure\\Services\\BaseConsumer.cs", "RelativeDocumentMoniker": "NotificationService.Infrastructure\\Services\\BaseConsumer.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Notification-Service\\src\\NotificationService.Infrastructure\\Services\\BaseConsumer.cs", "RelativeToolTip": "NotificationService.Infrastructure\\Services\\BaseConsumer.cs", "ViewState": "AgIAAFAAAAAAAAAAAAAnwF0AAAAYAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-31T08:41:23.882Z", "IsPinned": true, "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 5, "Title": "RabbitMQBrokerService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Notification-Service\\src\\NotificationService.Infrastructure\\Services\\RabbitMQBrokerService.cs", "RelativeDocumentMoniker": "NotificationService.Infrastructure\\Services\\RabbitMQBrokerService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Notification-Service\\src\\NotificationService.Infrastructure\\Services\\RabbitMQBrokerService.cs", "RelativeToolTip": "NotificationService.Infrastructure\\Services\\RabbitMQBrokerService.cs", "ViewState": "AQIAABMAAAAAAAAAAAAYwBsAAAAqAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-07T15:18:31.537Z", "IsPinned": true}, {"$type": "Document", "DocumentIndex": 1, "Title": "appsettings.Development.json", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Notification-Service\\src\\NotificationService.API\\appsettings.Development.json", "RelativeDocumentMoniker": "NotificationService.API\\appsettings.Development.json", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Notification-Service\\src\\NotificationService.API\\appsettings.Development.json", "RelativeToolTip": "NotificationService.API\\appsettings.Development.json", "ViewState": "AgIAAEwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-08-10T21:31:43.148Z", "EditorCaption": ""}, {"$type": "Bookmark", "Name": "ST:0:0:{3ae79031-e1bc-11d0-8f78-00a0c9110057}"}, {"$type": "Bookmark", "Name": "ST:0:0:{0174dea2-fdbe-4ef1-8f99-c0beae78880f}"}, {"$type": "Bookmark", "Name": "ST:129:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Bookmark", "Name": "ST:129:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:0:0:{1c4feeaa-4718-4aa9-859d-94ce25d182ba}"}, {"$type": "Bookmark", "Name": "ST:254354193:0:{71f361cc-493f-47c0-923f-f2570b6f8618}"}, {"$type": "Document", "DocumentIndex": 0, "Title": "ApnsService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Notification-Service\\src\\NotificationService.Infrastructure\\Services\\ApnsService.cs", "RelativeDocumentMoniker": "NotificationService.Infrastructure\\Services\\ApnsService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Notification-Service\\src\\NotificationService.Infrastructure\\Services\\ApnsService.cs*", "RelativeToolTip": "NotificationService.Infrastructure\\Services\\ApnsService.cs*", "ViewState": "AgIAAJUAAAAAAAAAAAAgwKAAAABmAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-10T21:17:21.646Z", "EditorCaption": ""}]}]}]}