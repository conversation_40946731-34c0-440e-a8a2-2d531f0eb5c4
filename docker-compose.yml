version: '3.8'
name: notification-service-workload
services:
    api:
        container_name: notification-svc-api
        image: notification-svc
        
        environment:
            - ASPNETCORE_ENVIRONMENT=Production
            - ASPNETCORE_Kestrel__Certificates__Default__Password=password10$
            - ASPNETCORE_Kestrel__Certificates__Default__Path=/https/aspnetapp.pfx
            - ASPNETCORE_ENVIRONMENT=Development
            - ASPNETCORE_URLS=https://+;http://+:7005
            - ConnectionStrings__NotificationConnection=Server=host.docker.internal,1433;Initial Catalog=notification_service;Persist Security Info=False;User ID=sa;Password=***********;MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=True;Connection Timeout=30;
            - LogPath=\logs\nottification\log.txt
        ports:
          - 443:443
          - 7005:80
        volumes:
          - ~/.aspnet/https:/https:ro