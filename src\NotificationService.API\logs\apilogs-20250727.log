
12:18:11 [Information] :: Executed DbCommand ("40"ms) [Parameters=[""], CommandType='Text', CommandTimeout='30']"
""SELECT \"MigrationId\", \"ProductVersion\"
FROM \"__EFMigrationsHistory\"
ORDER BY \"MigrationId\";"

12:18:12 [Information] :: User profile is available. Using '"C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys"' as key repository and Windows DPAPI to encrypt keys at rest.

12:18:12 [Information] :: Now listening on: "https://localhost:7044"

12:18:12 [Information] :: Now listening on: "http://localhost:5179"

12:18:12 [Information] :: Application started. Press Ctrl+C to shut down.

12:18:12 [Information] :: Hosting environment: "Development"

12:18:12 [Information] :: Content root path: "C:\Users\<USER>\source\repos\Notification-Service\src\NotificationService.API"

12:18:17 [Information] :: Request starting "HTTP/2" "GET" "https"://"localhost:7044""""/swagger""" - null null

12:18:17 [Information] :: Request finished "HTTP/2" "GET" "https"://"localhost:7044""""/swagger""" - 301 0 null 541.8879ms

12:18:18 [Information] :: Request starting "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/index.html""" - null null

12:18:18 [Information] :: Request finished "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/index.html""" - 200 null "text/html;charset=utf-8" 281.7359ms

12:18:18 [Information] :: Request starting "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/swagger-ui.css""" - null null

12:18:18 [Information] :: Request starting "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/swagger-ui-bundle.js""" - null null

12:18:18 [Information] :: Request starting "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/swagger-ui-standalone-preset.js""" - null null

12:18:18 [Information] :: Request starting "HTTP/2" "GET" "https"://"localhost:7044""""/_framework/aspnetcore-browser-refresh.js""" - null null

12:18:18 [Information] :: Request finished "HTTP/2" "GET" "https"://"localhost:7044""""/_framework/aspnetcore-browser-refresh.js""" - 200 13782 "application/javascript; charset=utf-8" 45.4006ms

12:18:18 [Information] :: Sending file. Request path: '"/swagger-ui.css"'. Physical path: '"N/A"'

12:18:18 [Information] :: Sending file. Request path: '"/swagger-ui-standalone-preset.js"'. Physical path: '"N/A"'

12:18:18 [Information] :: Request finished "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/swagger-ui.css""" - 200 143943 "text/css" 197.3559ms

12:18:18 [Information] :: Sending file. Request path: '"/swagger-ui-bundle.js"'. Physical path: '"N/A"'

12:18:18 [Information] :: Request starting "HTTP/2" "GET" "https"://"localhost:7044""""/_vs/browserLink""" - null null

12:18:18 [Information] :: Request finished "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/swagger-ui-standalone-preset.js""" - 200 339486 "text/javascript" 189.9534ms

12:18:18 [Information] :: Request finished "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/swagger-ui-bundle.js""" - 200 1096145 "text/javascript" 213.0939ms

12:18:18 [Information] :: Request finished "HTTP/2" "GET" "https"://"localhost:7044""""/_vs/browserLink""" - 200 null "text/javascript; charset=UTF-8" 146.6973ms

12:18:20 [Information] :: Request starting "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/v1/swagger.json""" - null null

12:18:20 [Information] :: Request starting "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/favicon-32x32.png""" - null null

12:18:20 [Information] :: Sending file. Request path: '"/favicon-32x32.png"'. Physical path: '"N/A"'

12:18:20 [Information] :: Request finished "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/favicon-32x32.png""" - 200 628 "image/png" 39.0966ms

12:18:20 [Information] :: Request finished "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/v1/swagger.json""" - 200 null "application/json;charset=utf-8" 373.3319ms

12:33:27 [Information] :: Executed DbCommand ("23"ms) [Parameters=[""], CommandType='Text', CommandTimeout='30']"
""SELECT \"MigrationId\", \"ProductVersion\"
FROM \"__EFMigrationsHistory\"
ORDER BY \"MigrationId\";"

12:33:28 [Information] :: User profile is available. Using '"C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys"' as key repository and Windows DPAPI to encrypt keys at rest.

12:33:28 [Information] :: Now listening on: "https://localhost:7044"

12:33:28 [Information] :: Now listening on: "http://localhost:5179"

12:33:28 [Information] :: Application started. Press Ctrl+C to shut down.

12:33:28 [Information] :: Hosting environment: "Development"

12:33:28 [Information] :: Content root path: "C:\Users\<USER>\source\repos\Notification-Service\src\NotificationService.API"

12:33:32 [Information] :: Request starting "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/index.html""" - null null

12:33:32 [Information] :: Request finished "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/index.html""" - 200 null "text/html;charset=utf-8" 653.0576ms

12:33:32 [Information] :: Request starting "HTTP/2" "GET" "https"://"localhost:7044""""/_vs/browserLink""" - null null

12:33:32 [Information] :: Request starting "HTTP/2" "GET" "https"://"localhost:7044""""/_framework/aspnetcore-browser-refresh.js""" - null null

12:33:32 [Information] :: Request finished "HTTP/2" "GET" "https"://"localhost:7044""""/_vs/browserLink""" - 200 null "text/javascript; charset=UTF-8" 102.3073ms

12:33:32 [Information] :: Request finished "HTTP/2" "GET" "https"://"localhost:7044""""/_framework/aspnetcore-browser-refresh.js""" - 200 13782 "application/javascript; charset=utf-8" 30.8209ms

12:33:33 [Information] :: Request starting "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/v1/swagger.json""" - null null

12:33:33 [Information] :: Request finished "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/v1/swagger.json""" - 200 null "application/json;charset=utf-8" 210.4123ms

12:34:47 [Information] :: Request starting "HTTP/1.1" "POST" "https"://"localhost:7044""""/api/Notification/send""" - "application/json" 482

12:34:47 [Information] :: Executing endpoint '"NotificationService.API.Controllers.NotificationController.Send (NotificationService.API)"'

12:34:47 [Information] :: Route matched with "{action = \"Send\", controller = \"Notification\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Send(NotificationService.Core.Models.NotificationMessageModel)" on controller "NotificationService.API.Controllers.NotificationController" ("NotificationService.API").

12:34:47 [Information] :: Firebase Admin SDK initialized successfully

12:34:47 [Error] :: UserId is required for push notifications

12:34:48 [Information] :: Executed action "NotificationService.API.Controllers.NotificationController.Send (NotificationService.API)" in 528.7126ms

12:34:48 [Information] :: Executed endpoint '"NotificationService.API.Controllers.NotificationController.Send (NotificationService.API)"'

12:34:48 [Error] :: An unhandled exception has occurred while executing the request.
NotificationService.Core.Exceptions.ApiException: UserId is required for push notifications
   at NotificationService.Infrastructure.Services.NotificationService.ProcessMessage(NotificationMessageModel model) in C:\Users\<USER>\source\repos\Notification-Service\src\NotificationService.Infrastructure\Services\NotificationService.cs:line 46
   at NotificationService.API.Controllers.NotificationController.Send(NotificationMessageModel message) in C:\Users\<USER>\source\repos\Notification-Service\src\NotificationService.API\Controllers\NotificationController.cs:line 34
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.TaskOfIActionResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|7_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Session.SessionMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Session.SessionMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at WatchDog.src.WatchDog.LogResponse(HttpContext context)
   at WatchDog.src.WatchDog.InvokeAsync(HttpContext context)
   at WatchDog.src.WatchDogExceptionLogger.InvokeAsync(HttpContext context)
   at WatchDog.src.WatchDogExceptionLogger.InvokeAsync(HttpContext context)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Diagnostics.ExceptionHandlerMiddlewareImpl.<Invoke>g__Awaited|10_0(ExceptionHandlerMiddlewareImpl middleware, HttpContext context, Task task)

12:34:48 [Error] :: UserId is required for push notifications
NotificationService.Core.Exceptions.ApiException: UserId is required for push notifications
   at NotificationService.Infrastructure.Services.NotificationService.ProcessMessage(NotificationMessageModel model) in C:\Users\<USER>\source\repos\Notification-Service\src\NotificationService.Infrastructure\Services\NotificationService.cs:line 46
   at NotificationService.API.Controllers.NotificationController.Send(NotificationMessageModel message) in C:\Users\<USER>\source\repos\Notification-Service\src\NotificationService.API\Controllers\NotificationController.cs:line 34
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.TaskOfIActionResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|7_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Session.SessionMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Session.SessionMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at WatchDog.src.WatchDog.LogResponse(HttpContext context)
   at WatchDog.src.WatchDog.InvokeAsync(HttpContext context)
   at WatchDog.src.WatchDogExceptionLogger.InvokeAsync(HttpContext context)
   at WatchDog.src.WatchDogExceptionLogger.InvokeAsync(HttpContext context)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Diagnostics.ExceptionHandlerMiddlewareImpl.<Invoke>g__Awaited|10_0(ExceptionHandlerMiddlewareImpl middleware, HttpContext context, Task task)

12:34:48 [Information] :: Request finished "HTTP/1.1" "POST" "https"://"localhost:7044""""/api/Notification/send""" - 500 null "application/json" 1214.934ms

12:35:09 [Information] :: Request starting "HTTP/1.1" "POST" "https"://"localhost:7044""""/api/Notification/send""" - "application/json" 516

12:35:09 [Information] :: Executing endpoint '"NotificationService.API.Controllers.NotificationController.Send (NotificationService.API)"'

12:35:09 [Information] :: Route matched with "{action = \"Send\", controller = \"Notification\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Send(NotificationService.Core.Models.NotificationMessageModel)" on controller "NotificationService.API.Controllers.NotificationController" ("NotificationService.API").

12:35:10 [Information] :: Executed DbCommand ("28"ms) [Parameters=["@__model_UserId_0='?', @__ToString_1='?'"], CommandType='Text', CommandTimeout='30']"
""SELECT p.\"Id\", p.\"AppName\", p.\"CreatedBy\", p.\"CreatedOn\", p.\"Token\", p.\"UpdatedBy\", p.\"UpdatedOn\", p.\"UserId\"
FROM \"PushTokens\" AS p
WHERE p.\"UserId\" = @__model_UserId_0 AND p.\"AppName\" = @__ToString_1"

12:35:14 [Information] :: MailGun response {"id":"<<EMAIL>>","message":"Queued. Thank you."}


12:35:14 [Error] :: Error sending Firebase notification
System.ArgumentException: Exactly one of Token, Topic or Condition is required.
   at FirebaseAdmin.Messaging.Message.CopyAndValidate()
   at FirebaseAdmin.Messaging.FirebaseMessagingClient.SendAsync(Message message, Boolean dryRun, CancellationToken cancellationToken)
   at FirebaseAdmin.Messaging.FirebaseMessaging.SendAsync(Message message, Boolean dryRun, CancellationToken cancellationToken)
   at FirebaseAdmin.Messaging.FirebaseMessaging.SendAsync(Message message, Boolean dryRun)
   at FirebaseAdmin.Messaging.FirebaseMessaging.SendAsync(Message message)
   at NotificationService.Infrastructure.Services.FirebasePushNotificationService.SendMessage(NotificationMessageModel model) in C:\Users\<USER>\source\repos\Notification-Service\src\NotificationService.Infrastructure\Services\FirebasePushMessageService.cs:line 89

12:35:15 [Information] :: Executed DbCommand ("46"ms) [Parameters=["@p0='?', @p1='?', @p2='?' (DbType = DateTime), @p3='?', @p4='?', @p5='?', @p6='?', @p7='?', @p8='?', @p9='?', @p10='?', @p11='?', @p12='?', @p13='?', @p14='?', @p15='?', @p16='?', @p17='?' (DbType = DateTime), @p18='?', @p19='?', @p20='?', @p21='?' (DbType = DateTime), @p22='?', @p23='?', @p24='?', @p25='?', @p26='?', @p27='?', @p28='?', @p29='?', @p30='?', @p31='?', @p32='?', @p33='?', @p34='?', @p35='?', @p36='?' (DbType = DateTime), @p37='?'"], CommandType='Text', CommandTimeout='30']"
""INSERT INTO \"Notifications\" (\"Body\", \"CreatedBy\", \"CreatedOn\", \"Email\", \"MessageId\", \"MessageSid\", \"NotificationType\", \"PhoneNumber\", \"Priority\", \"PushToken\", \"ResponseId\", \"Status\", \"StatusCode\", \"StatusMessage\", \"Subject\", \"Template\", \"UpdatedBy\", \"UpdatedOn\", \"UserId\")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16, @p17, @p18)
RETURNING \"Id\";
INSERT INTO \"Notifications\" (\"Body\", \"CreatedBy\", \"CreatedOn\", \"Email\", \"MessageId\", \"MessageSid\", \"NotificationType\", \"PhoneNumber\", \"Priority\", \"PushToken\", \"ResponseId\", \"Status\", \"StatusCode\", \"StatusMessage\", \"Subject\", \"Template\", \"UpdatedBy\", \"UpdatedOn\", \"UserId\")
VALUES (@p19, @p20, @p21, @p22, @p23, @p24, @p25, @p26, @p27, @p28, @p29, @p30, @p31, @p32, @p33, @p34, @p35, @p36, @p37)
RETURNING \"Id\";"

12:35:15 [Information] :: Executing "OkObjectResult", writing value of type '"System.String"'.

12:35:15 [Information] :: Executed action "NotificationService.API.Controllers.NotificationController.Send (NotificationService.API)" in 5450.7622ms

12:35:15 [Information] :: Executed endpoint '"NotificationService.API.Controllers.NotificationController.Send (NotificationService.API)"'

12:35:15 [Information] :: Request finished "HTTP/1.1" "POST" "https"://"localhost:7044""""/api/Notification/send""" - 200 null "text/plain; charset=utf-8" 5495.1756ms

12:35:41 [Information] :: Request starting "HTTP/1.1" "POST" "https"://"localhost:7044""""/api/Notification/send""" - "application/json" 516

12:35:41 [Information] :: Executing endpoint '"NotificationService.API.Controllers.NotificationController.Send (NotificationService.API)"'

12:35:41 [Information] :: Route matched with "{action = \"Send\", controller = \"Notification\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Send(NotificationService.Core.Models.NotificationMessageModel)" on controller "NotificationService.API.Controllers.NotificationController" ("NotificationService.API").

12:35:41 [Information] :: Executed DbCommand ("1"ms) [Parameters=["@__model_UserId_0='?', @__ToString_1='?'"], CommandType='Text', CommandTimeout='30']"
""SELECT p.\"Id\", p.\"AppName\", p.\"CreatedBy\", p.\"CreatedOn\", p.\"Token\", p.\"UpdatedBy\", p.\"UpdatedOn\", p.\"UserId\"
FROM \"PushTokens\" AS p
WHERE p.\"UserId\" = @__model_UserId_0 AND p.\"AppName\" = @__ToString_1"

12:35:43 [Information] :: MailGun response {"id":"<<EMAIL>>","message":"Queued. Thank you."}


12:36:00 [Error] :: Error sending Firebase notification
System.ArgumentException: Exactly one of Token, Topic or Condition is required.
   at FirebaseAdmin.Messaging.Message.CopyAndValidate()
   at FirebaseAdmin.Messaging.FirebaseMessagingClient.SendAsync(Message message, Boolean dryRun, CancellationToken cancellationToken)
   at FirebaseAdmin.Messaging.FirebaseMessaging.SendAsync(Message message, Boolean dryRun, CancellationToken cancellationToken)
   at FirebaseAdmin.Messaging.FirebaseMessaging.SendAsync(Message message, Boolean dryRun)
   at FirebaseAdmin.Messaging.FirebaseMessaging.SendAsync(Message message)
   at NotificationService.Infrastructure.Services.FirebasePushNotificationService.SendMessage(NotificationMessageModel model) in C:\Users\<USER>\source\repos\Notification-Service\src\NotificationService.Infrastructure\Services\FirebasePushMessageService.cs:line 89

12:36:00 [Information] :: Executed DbCommand ("2"ms) [Parameters=["@p0='?', @p1='?', @p2='?' (DbType = DateTime), @p3='?', @p4='?', @p5='?', @p6='?', @p7='?', @p8='?', @p9='?', @p10='?', @p11='?', @p12='?', @p13='?', @p14='?', @p15='?', @p16='?', @p17='?' (DbType = DateTime), @p18='?', @p19='?', @p20='?', @p21='?' (DbType = DateTime), @p22='?', @p23='?', @p24='?', @p25='?', @p26='?', @p27='?', @p28='?', @p29='?', @p30='?', @p31='?', @p32='?', @p33='?', @p34='?', @p35='?', @p36='?' (DbType = DateTime), @p37='?'"], CommandType='Text', CommandTimeout='30']"
""INSERT INTO \"Notifications\" (\"Body\", \"CreatedBy\", \"CreatedOn\", \"Email\", \"MessageId\", \"MessageSid\", \"NotificationType\", \"PhoneNumber\", \"Priority\", \"PushToken\", \"ResponseId\", \"Status\", \"StatusCode\", \"StatusMessage\", \"Subject\", \"Template\", \"UpdatedBy\", \"UpdatedOn\", \"UserId\")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16, @p17, @p18)
RETURNING \"Id\";
INSERT INTO \"Notifications\" (\"Body\", \"CreatedBy\", \"CreatedOn\", \"Email\", \"MessageId\", \"MessageSid\", \"NotificationType\", \"PhoneNumber\", \"Priority\", \"PushToken\", \"ResponseId\", \"Status\", \"StatusCode\", \"StatusMessage\", \"Subject\", \"Template\", \"UpdatedBy\", \"UpdatedOn\", \"UserId\")
VALUES (@p19, @p20, @p21, @p22, @p23, @p24, @p25, @p26, @p27, @p28, @p29, @p30, @p31, @p32, @p33, @p34, @p35, @p36, @p37)
RETURNING \"Id\";"

12:36:00 [Information] :: Executing "OkObjectResult", writing value of type '"System.String"'.

12:36:00 [Information] :: Executed action "NotificationService.API.Controllers.NotificationController.Send (NotificationService.API)" in 18951.1686ms

12:36:00 [Information] :: Executed endpoint '"NotificationService.API.Controllers.NotificationController.Send (NotificationService.API)"'

12:36:00 [Information] :: Request finished "HTTP/1.1" "POST" "https"://"localhost:7044""""/api/Notification/send""" - 200 null "text/plain; charset=utf-8" 19022.7314ms

12:40:42 [Information] :: Request starting "HTTP/1.1" "GET" "https"://"localhost:7044""""/api/User/create-push-token""" - "application/json" 281

12:40:42 [Information] :: Executing endpoint '"405 HTTP Method Not Supported"'

12:40:42 [Information] :: Executed endpoint '"405 HTTP Method Not Supported"'

12:40:42 [Information] :: Request finished "HTTP/1.1" "GET" "https"://"localhost:7044""""/api/User/create-push-token""" - 405 0 null 24.2988ms

12:40:48 [Information] :: Request starting "HTTP/1.1" "POST" "https"://"localhost:7044""""/api/User/create-push-token""" - "application/json" 281

12:40:48 [Information] :: Executing endpoint '"NotificationService.API.Controllers.UserController.PushToken (NotificationService.API)"'

12:40:48 [Information] :: Route matched with "{action = \"PushToken\", controller = \"User\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] PushToken(NotificationService.Core.Models.PushTokenModel)" on controller "NotificationService.API.Controllers.UserController" ("NotificationService.API").

12:40:51 [Error] :: Error occurred while validating user ID "cfd54e01-21d0-44d5-877f-4d2092f92876"
NotificationService.Core.Exceptions.ApiException: User Id not found
   at NotificationService.Infrastructure.Services.UserService.CreatePushToken(PushTokenModel model) in C:\Users\<USER>\source\repos\Notification-Service\src\NotificationService.Infrastructure\Services\UserService.cs:line 45

12:40:51 [Information] :: Executed action "NotificationService.API.Controllers.UserController.PushToken (NotificationService.API)" in 2533.4425ms

12:40:51 [Information] :: Executed endpoint '"NotificationService.API.Controllers.UserController.PushToken (NotificationService.API)"'

12:40:51 [Error] :: An unhandled exception has occurred while executing the request.
NotificationService.Core.Exceptions.ApiException: User Id not found
   at NotificationService.Infrastructure.Services.UserService.CreatePushToken(PushTokenModel model) in C:\Users\<USER>\source\repos\Notification-Service\src\NotificationService.Infrastructure\Services\UserService.cs:line 45
   at NotificationService.API.Controllers.UserController.PushToken(PushTokenModel model) in C:\Users\<USER>\source\repos\Notification-Service\src\NotificationService.API\Controllers\UserController.cs:line 23
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.TaskOfIActionResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|7_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Session.SessionMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Session.SessionMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at WatchDog.src.WatchDog.LogResponse(HttpContext context)
   at WatchDog.src.WatchDog.InvokeAsync(HttpContext context)
   at WatchDog.src.WatchDogExceptionLogger.InvokeAsync(HttpContext context)
   at WatchDog.src.WatchDogExceptionLogger.InvokeAsync(HttpContext context)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Diagnostics.ExceptionHandlerMiddlewareImpl.<Invoke>g__Awaited|10_0(ExceptionHandlerMiddlewareImpl middleware, HttpContext context, Task task)

12:40:51 [Error] :: User Id not found
NotificationService.Core.Exceptions.ApiException: User Id not found
   at NotificationService.Infrastructure.Services.UserService.CreatePushToken(PushTokenModel model) in C:\Users\<USER>\source\repos\Notification-Service\src\NotificationService.Infrastructure\Services\UserService.cs:line 45
   at NotificationService.API.Controllers.UserController.PushToken(PushTokenModel model) in C:\Users\<USER>\source\repos\Notification-Service\src\NotificationService.API\Controllers\UserController.cs:line 23
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.TaskOfIActionResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|7_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Session.SessionMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Session.SessionMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at WatchDog.src.WatchDog.LogResponse(HttpContext context)
   at WatchDog.src.WatchDog.InvokeAsync(HttpContext context)
   at WatchDog.src.WatchDogExceptionLogger.InvokeAsync(HttpContext context)
   at WatchDog.src.WatchDogExceptionLogger.InvokeAsync(HttpContext context)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Diagnostics.ExceptionHandlerMiddlewareImpl.<Invoke>g__Awaited|10_0(ExceptionHandlerMiddlewareImpl middleware, HttpContext context, Task task)

12:40:51 [Information] :: Request finished "HTTP/1.1" "POST" "https"://"localhost:7044""""/api/User/create-push-token""" - 500 null "application/json" 2599.1265ms

12:51:27 [Information] :: Request starting "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/index.html""" - null null

12:51:27 [Information] :: Request finished "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/index.html""" - 200 null "text/html;charset=utf-8" 25.2671ms

12:51:27 [Information] :: Request starting "HTTP/2" "GET" "https"://"localhost:7044""""/_vs/browserLink""" - null null

12:51:27 [Information] :: Request starting "HTTP/2" "GET" "https"://"localhost:7044""""/_framework/aspnetcore-browser-refresh.js""" - null null

12:51:27 [Information] :: Request finished "HTTP/2" "GET" "https"://"localhost:7044""""/_framework/aspnetcore-browser-refresh.js""" - 200 13782 "application/javascript; charset=utf-8" 14.105ms

12:51:27 [Information] :: Request finished "HTTP/2" "GET" "https"://"localhost:7044""""/_vs/browserLink""" - 200 null "text/javascript; charset=UTF-8" 35.7389ms

12:51:27 [Information] :: Request starting "HTTP/1.1" "POST" "https"://"localhost:7044""""/api/User/create-push-token""" - "application/json" 281

12:51:27 [Information] :: Executing endpoint '"NotificationService.API.Controllers.UserController.PushToken (NotificationService.API)"'

12:51:27 [Information] :: Route matched with "{action = \"PushToken\", controller = \"User\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] PushToken(NotificationService.Core.Models.PushTokenModel)" on controller "NotificationService.API.Controllers.UserController" ("NotificationService.API").

12:51:29 [Information] :: Request starting "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/v1/swagger.json""" - null null

12:51:33 [Information] :: Request finished "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/v1/swagger.json""" - 200 null "application/json;charset=utf-8" 3777.1805ms

12:51:53 [Error] :: Error occurred while validating user ID "cfd54e01-21d0-44d5-877f-4d2092f92876"
NotificationService.Core.Exceptions.ApiException: User Id not found
   at NotificationService.Infrastructure.Services.UserService.CreatePushToken(PushTokenModel model)

12:51:53 [Information] :: Executed action "NotificationService.API.Controllers.UserController.PushToken (NotificationService.API)" in 25598.2183ms

12:51:53 [Information] :: Executed endpoint '"NotificationService.API.Controllers.UserController.PushToken (NotificationService.API)"'

12:51:53 [Error] :: An unhandled exception has occurred while executing the request.
NotificationService.Core.Exceptions.ApiException: User Id not found
   at NotificationService.Infrastructure.Services.UserService.CreatePushToken(PushTokenModel model)
   at NotificationService.API.Controllers.UserController.PushToken(PushTokenModel model) in C:\Users\<USER>\source\repos\Notification-Service\src\NotificationService.API\Controllers\UserController.cs:line 23
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.TaskOfIActionResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|7_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Session.SessionMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Session.SessionMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at WatchDog.src.WatchDog.LogResponse(HttpContext context)
   at WatchDog.src.WatchDog.InvokeAsync(HttpContext context)
   at WatchDog.src.WatchDogExceptionLogger.InvokeAsync(HttpContext context)
   at WatchDog.src.WatchDogExceptionLogger.InvokeAsync(HttpContext context)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Diagnostics.ExceptionHandlerMiddlewareImpl.<Invoke>g__Awaited|10_0(ExceptionHandlerMiddlewareImpl middleware, HttpContext context, Task task)

12:51:53 [Error] :: User Id not found
NotificationService.Core.Exceptions.ApiException: User Id not found
   at NotificationService.Infrastructure.Services.UserService.CreatePushToken(PushTokenModel model)
   at NotificationService.API.Controllers.UserController.PushToken(PushTokenModel model) in C:\Users\<USER>\source\repos\Notification-Service\src\NotificationService.API\Controllers\UserController.cs:line 23
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.TaskOfIActionResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|7_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Session.SessionMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Session.SessionMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at WatchDog.src.WatchDog.LogResponse(HttpContext context)
   at WatchDog.src.WatchDog.InvokeAsync(HttpContext context)
   at WatchDog.src.WatchDogExceptionLogger.InvokeAsync(HttpContext context)
   at WatchDog.src.WatchDogExceptionLogger.InvokeAsync(HttpContext context)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Diagnostics.ExceptionHandlerMiddlewareImpl.<Invoke>g__Awaited|10_0(ExceptionHandlerMiddlewareImpl middleware, HttpContext context, Task task)

12:51:53 [Information] :: Request finished "HTTP/1.1" "POST" "https"://"localhost:7044""""/api/User/create-push-token""" - 500 null "application/json" 25667.6663ms

12:52:06 [Information] :: Request starting "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/index.html""" - null null

12:52:06 [Information] :: Request finished "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/index.html""" - 200 null "text/html;charset=utf-8" 15.3131ms

12:52:06 [Information] :: Request starting "HTTP/2" "GET" "https"://"localhost:7044""""/_framework/aspnetcore-browser-refresh.js""" - null null

12:52:06 [Information] :: Request finished "HTTP/2" "GET" "https"://"localhost:7044""""/_framework/aspnetcore-browser-refresh.js""" - 200 13782 "application/javascript; charset=utf-8" 8.2725ms

12:52:06 [Information] :: Request starting "HTTP/2" "GET" "https"://"localhost:7044""""/_vs/browserLink""" - null null

12:52:06 [Information] :: Request finished "HTTP/2" "GET" "https"://"localhost:7044""""/_vs/browserLink""" - 200 null "text/javascript; charset=UTF-8" 37.8697ms

12:52:06 [Information] :: Request starting "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/v1/swagger.json""" - null null

12:52:07 [Information] :: Request finished "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/v1/swagger.json""" - 200 null "application/json;charset=utf-8" 18.4288ms

12:52:08 [Information] :: Request starting "HTTP/1.1" "POST" "https"://"localhost:7044""""/api/User/create-push-token""" - "application/json" 281

12:52:08 [Information] :: Executing endpoint '"NotificationService.API.Controllers.UserController.PushToken (NotificationService.API)"'

12:52:08 [Information] :: Route matched with "{action = \"PushToken\", controller = \"User\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] PushToken(NotificationService.Core.Models.PushTokenModel)" on controller "NotificationService.API.Controllers.UserController" ("NotificationService.API").

12:52:21 [Information] :: Executed DbCommand ("10"ms) [Parameters=["@__model_userId_0='?'"], CommandType='Text', CommandTimeout='30']"
""SELECT u.\"Id\", u.\"CompanyEmail\", u.\"Country\", u.\"CreatedBy\", u.\"CreatedOn\", u.\"Email\", u.\"FirstName\", u.\"LastName\", u.\"PhoneNumber\", u.\"TimeZone\", u.\"UpdatedBy\", u.\"UpdatedOn\", u.\"UserId\", u.\"Username\", u.\"ZipCode\"
FROM \"Users\" AS u
WHERE u.\"UserId\" = @__model_userId_0
LIMIT 1"

12:52:24 [Error] :: Failed executing DbCommand ("57"ms) [Parameters=["@p0='?', @p1='?', @p2='?', @p3='?' (DbType = DateTime), @p4='?', @p5='?', @p6='?', @p7='?', @p8='?', @p9='?', @p10='?' (DbType = DateTime), @p11='?', @p12='?', @p13='?'"], CommandType='Text', CommandTimeout='30']"
""INSERT INTO \"Users\" (\"CompanyEmail\", \"Country\", \"CreatedBy\", \"CreatedOn\", \"Email\", \"FirstName\", \"LastName\", \"PhoneNumber\", \"TimeZone\", \"UpdatedBy\", \"UpdatedOn\", \"UserId\", \"Username\", \"ZipCode\")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13)
RETURNING \"Id\";"

12:52:24 [Error] :: An exception occurred in the database while saving changes for context type '"NotificationService.Infrastructure.Repositories.NotificationDbContext"'."
""Microsoft.EntityFrameworkCore.DbUpdateException: An error occurred while saving the entity changes. See the inner exception for details.
 ---> Npgsql.PostgresException (0x80004005): 23502: null value in column \"ZipCode\" of relation \"Users\" violates not-null constraint

DETAIL: Failing row contains (1, c964a01d-d0f3-4553-b53d-07e773d66525, <EMAIL>, +2347062746869, <EMAIL>, Osigwe, Chidozie, <EMAIL>, null, Nigeria, null, null, 2025-07-27 12:52:24.626502+01, null, -infinity).
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
  Exception data:
    Severity: ERROR
    SqlState: 23502
    MessageText: null value in column \"ZipCode\" of relation \"Users\" violates not-null constraint
    Detail: Failing row contains (1, c964a01d-d0f3-4553-b53d-07e773d66525, <EMAIL>, +2347062746869, <EMAIL>, Osigwe, Chidozie, <EMAIL>, null, Nigeria, null, null, 2025-07-27 12:52:24.626502+01, null, -infinity).
    SchemaName: public
    TableName: Users
    ColumnName: ZipCode
    File: execMain.c
    Line: 2009
    Routine: ExecConstraints
   --- End of inner exception stack trace ---
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.<>c__DisplayClass30_0`2.<<ExecuteAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)"
Microsoft.EntityFrameworkCore.DbUpdateException: An error occurred while saving the entity changes. See the inner exception for details.
 ---> Npgsql.PostgresException (0x80004005): 23502: null value in column "ZipCode" of relation "Users" violates not-null constraint

DETAIL: Failing row contains (1, c964a01d-d0f3-4553-b53d-07e773d66525, <EMAIL>, +2347062746869, <EMAIL>, Osigwe, Chidozie, <EMAIL>, null, Nigeria, null, null, 2025-07-27 12:52:24.626502+01, null, -infinity).
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
  Exception data:
    Severity: ERROR
    SqlState: 23502
    MessageText: null value in column "ZipCode" of relation "Users" violates not-null constraint
    Detail: Failing row contains (1, c964a01d-d0f3-4553-b53d-07e773d66525, <EMAIL>, +2347062746869, <EMAIL>, Osigwe, Chidozie, <EMAIL>, null, Nigeria, null, null, 2025-07-27 12:52:24.626502+01, null, -infinity).
    SchemaName: public
    TableName: Users
    ColumnName: ZipCode
    File: execMain.c
    Line: 2009
    Routine: ExecConstraints
   --- End of inner exception stack trace ---
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.<>c__DisplayClass30_0`2.<<ExecuteAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)

13:05:28 [Information] :: Executed DbCommand ("24"ms) [Parameters=[""], CommandType='Text', CommandTimeout='30']"
""SELECT \"MigrationId\", \"ProductVersion\"
FROM \"__EFMigrationsHistory\"
ORDER BY \"MigrationId\";"

13:05:28 [Information] :: Executed DbCommand ("15"ms) [Parameters=[""], CommandType='Text', CommandTimeout='30']"
""SELECT \"MigrationId\", \"ProductVersion\"
FROM \"__EFMigrationsHistory\"
ORDER BY \"MigrationId\";"

13:05:28 [Information] :: Executed DbCommand ("12"ms) [Parameters=[""], CommandType='Text', CommandTimeout='30']"
""CREATE TABLE IF NOT EXISTS \"__EFMigrationsHistory\" (
    \"MigrationId\" character varying(150) NOT NULL,
    \"ProductVersion\" character varying(32) NOT NULL,
    CONSTRAINT \"PK___EFMigrationsHistory\" PRIMARY KEY (\"MigrationId\")
);"

13:05:28 [Information] :: Executed DbCommand ("5"ms) [Parameters=[""], CommandType='Text', CommandTimeout='30']"
""LOCK TABLE \"__EFMigrationsHistory\" IN ACCESS EXCLUSIVE MODE"

13:05:28 [Information] :: Executed DbCommand ("1"ms) [Parameters=[""], CommandType='Text', CommandTimeout='30']"
""SELECT \"MigrationId\", \"ProductVersion\"
FROM \"__EFMigrationsHistory\"
ORDER BY \"MigrationId\";"

13:05:28 [Information] :: Applying migration '"20250727115948_updated_user_tbl"'.

13:05:28 [Information] :: Executed DbCommand ("14"ms) [Parameters=[""], CommandType='Text', CommandTimeout='30']"
""ALTER TABLE \"Users\" ALTER COLUMN \"ZipCode\" DROP NOT NULL;"

13:05:28 [Information] :: Executed DbCommand ("1"ms) [Parameters=[""], CommandType='Text', CommandTimeout='30']"
""ALTER TABLE \"Users\" ALTER COLUMN \"TimeZone\" DROP NOT NULL;"

13:05:28 [Information] :: Executed DbCommand ("0"ms) [Parameters=[""], CommandType='Text', CommandTimeout='30']"
""ALTER TABLE \"Users\" ALTER COLUMN \"Country\" DROP NOT NULL;"

13:05:28 [Information] :: Executed DbCommand ("6"ms) [Parameters=[""], CommandType='Text', CommandTimeout='30']"
""INSERT INTO \"__EFMigrationsHistory\" (\"MigrationId\", \"ProductVersion\")
VALUES ('20250727115948_updated_user_tbl', '9.0.2');"

13:05:29 [Information] :: User profile is available. Using '"C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys"' as key repository and Windows DPAPI to encrypt keys at rest.

13:05:29 [Information] :: Now listening on: "https://localhost:7044"

13:05:29 [Information] :: Now listening on: "http://localhost:5179"

13:05:29 [Information] :: Application started. Press Ctrl+C to shut down.

13:05:29 [Information] :: Hosting environment: "Development"

13:05:29 [Information] :: Content root path: "C:\Users\<USER>\source\repos\Notification-Service\src\NotificationService.API"

13:05:31 [Information] :: Request starting "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/index.html""" - null null

13:05:32 [Information] :: Request finished "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/index.html""" - 200 null "text/html;charset=utf-8" 611.4414ms

13:05:32 [Information] :: Request starting "HTTP/2" "GET" "https"://"localhost:7044""""/_vs/browserLink""" - null null

13:05:32 [Information] :: Request starting "HTTP/2" "GET" "https"://"localhost:7044""""/_framework/aspnetcore-browser-refresh.js""" - null null

13:05:32 [Information] :: Request finished "HTTP/2" "GET" "https"://"localhost:7044""""/_framework/aspnetcore-browser-refresh.js""" - 200 13782 "application/javascript; charset=utf-8" 207.5901ms

13:05:32 [Information] :: Request finished "HTTP/2" "GET" "https"://"localhost:7044""""/_vs/browserLink""" - 200 null "text/javascript; charset=UTF-8" 288.8673ms

13:05:33 [Information] :: Request starting "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/v1/swagger.json""" - null null

13:05:34 [Information] :: Request finished "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/v1/swagger.json""" - 200 null "application/json;charset=utf-8" 479.0871ms

13:05:40 [Information] :: Request starting "HTTP/1.1" "POST" "https"://"localhost:7044""""/api/User/create-push-token""" - "application/json" 281

13:05:41 [Information] :: Executing endpoint '"NotificationService.API.Controllers.UserController.PushToken (NotificationService.API)"'

13:05:41 [Information] :: Route matched with "{action = \"PushToken\", controller = \"User\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] PushToken(NotificationService.Core.Models.PushTokenModel)" on controller "NotificationService.API.Controllers.UserController" ("NotificationService.API").

13:05:51 [Information] :: Executed DbCommand ("100"ms) [Parameters=["@__model_userId_0='?'"], CommandType='Text', CommandTimeout='30']"
""SELECT u.\"Id\", u.\"CompanyEmail\", u.\"Country\", u.\"CreatedBy\", u.\"CreatedOn\", u.\"Email\", u.\"FirstName\", u.\"LastName\", u.\"PhoneNumber\", u.\"TimeZone\", u.\"UpdatedBy\", u.\"UpdatedOn\", u.\"UserId\", u.\"Username\", u.\"ZipCode\"
FROM \"Users\" AS u
WHERE u.\"UserId\" = @__model_userId_0
LIMIT 1"

13:05:55 [Information] :: Executed DbCommand ("15"ms) [Parameters=["@p0='?', @p1='?', @p2='?', @p3='?' (DbType = DateTime), @p4='?', @p5='?', @p6='?', @p7='?', @p8='?', @p9='?', @p10='?' (DbType = DateTime), @p11='?', @p12='?', @p13='?'"], CommandType='Text', CommandTimeout='30']"
""INSERT INTO \"Users\" (\"CompanyEmail\", \"Country\", \"CreatedBy\", \"CreatedOn\", \"Email\", \"FirstName\", \"LastName\", \"PhoneNumber\", \"TimeZone\", \"UpdatedBy\", \"UpdatedOn\", \"UserId\", \"Username\", \"ZipCode\")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13)
RETURNING \"Id\";"

13:05:57 [Information] :: Executed DbCommand ("5"ms) [Parameters=["@__model_userId_0='?', @__ToString_1='?'"], CommandType='Text', CommandTimeout='30']"
""SELECT p.\"Id\", p.\"AppName\", p.\"CreatedBy\", p.\"CreatedOn\", p.\"Token\", p.\"UpdatedBy\", p.\"UpdatedOn\", p.\"UserId\"
FROM \"PushTokens\" AS p
WHERE p.\"UserId\" = @__model_userId_0 AND p.\"AppName\" = @__ToString_1
LIMIT 1"

13:06:01 [Information] :: Executed DbCommand ("6"ms) [Parameters=["@p0='?', @p1='?', @p2='?' (DbType = DateTime), @p3='?', @p4='?', @p5='?' (DbType = DateTime), @p6='?'"], CommandType='Text', CommandTimeout='30']"
""INSERT INTO \"PushTokens\" (\"AppName\", \"CreatedBy\", \"CreatedOn\", \"Token\", \"UpdatedBy\", \"UpdatedOn\", \"UserId\")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6)
RETURNING \"Id\";"

13:06:07 [Information] :: Executing StatusCodeResult, setting HTTP status code 200

13:06:07 [Information] :: Executed action "NotificationService.API.Controllers.UserController.PushToken (NotificationService.API)" in 26666.8895ms

13:06:07 [Information] :: Executed endpoint '"NotificationService.API.Controllers.UserController.PushToken (NotificationService.API)"'

13:06:10 [Information] :: Request finished "HTTP/1.1" "POST" "https"://"localhost:7044""""/api/User/create-push-token""" - 200 0 null 29741.1557ms

13:09:16 [Information] :: Executed DbCommand ("33"ms) [Parameters=[""], CommandType='Text', CommandTimeout='30']"
""SELECT \"MigrationId\", \"ProductVersion\"
FROM \"__EFMigrationsHistory\"
ORDER BY \"MigrationId\";"

13:09:16 [Information] :: User profile is available. Using '"C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys"' as key repository and Windows DPAPI to encrypt keys at rest.

13:09:17 [Information] :: Now listening on: "https://localhost:7044"

13:09:17 [Information] :: Now listening on: "http://localhost:5179"

13:09:17 [Information] :: Application started. Press Ctrl+C to shut down.

13:09:17 [Information] :: Hosting environment: "Development"

13:09:17 [Information] :: Content root path: "C:\Users\<USER>\source\repos\Notification-Service\src\NotificationService.API"

13:09:19 [Information] :: Request starting "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/index.html""" - null null

13:09:19 [Information] :: Request finished "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/index.html""" - 200 null "text/html;charset=utf-8" 535.862ms

13:09:20 [Information] :: Request starting "HTTP/2" "GET" "https"://"localhost:7044""""/_framework/aspnetcore-browser-refresh.js""" - null null

13:09:20 [Information] :: Request starting "HTTP/2" "GET" "https"://"localhost:7044""""/_vs/browserLink""" - null null

13:09:20 [Information] :: Request finished "HTTP/2" "GET" "https"://"localhost:7044""""/_framework/aspnetcore-browser-refresh.js""" - 200 13782 "application/javascript; charset=utf-8" 460.8876ms

13:09:20 [Information] :: Request finished "HTTP/2" "GET" "https"://"localhost:7044""""/_vs/browserLink""" - 200 null "text/javascript; charset=UTF-8" 397.363ms

13:09:21 [Information] :: Request starting "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/v1/swagger.json""" - null null

13:09:22 [Information] :: Request finished "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/v1/swagger.json""" - 200 null "application/json;charset=utf-8" 176.8817ms

13:09:34 [Information] :: Request starting "HTTP/1.1" "POST" "https"://"localhost:7044""""/api/User/create-push-token""" - "application/json" 281

13:09:35 [Information] :: Executing endpoint '"NotificationService.API.Controllers.UserController.PushToken (NotificationService.API)"'

13:09:35 [Information] :: Route matched with "{action = \"PushToken\", controller = \"User\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] PushToken(NotificationService.Core.Models.PushTokenModel)" on controller "NotificationService.API.Controllers.UserController" ("NotificationService.API").

13:09:35 [Information] :: Executed DbCommand ("33"ms) [Parameters=["@__model_userId_0='?'"], CommandType='Text', CommandTimeout='30']"
""SELECT u.\"Id\", u.\"CompanyEmail\", u.\"Country\", u.\"CreatedBy\", u.\"CreatedOn\", u.\"Email\", u.\"FirstName\", u.\"LastName\", u.\"PhoneNumber\", u.\"TimeZone\", u.\"UpdatedBy\", u.\"UpdatedOn\", u.\"UserId\", u.\"Username\", u.\"ZipCode\"
FROM \"Users\" AS u
WHERE u.\"UserId\" = @__model_userId_0
LIMIT 1"

13:09:36 [Information] :: Executed DbCommand ("6"ms) [Parameters=["@__model_userId_0='?', @__ToString_1='?'"], CommandType='Text', CommandTimeout='30']"
""SELECT p.\"Id\", p.\"AppName\", p.\"CreatedBy\", p.\"CreatedOn\", p.\"Token\", p.\"UpdatedBy\", p.\"UpdatedOn\", p.\"UserId\"
FROM \"PushTokens\" AS p
WHERE p.\"UserId\" = @__model_userId_0 AND p.\"AppName\" = @__ToString_1
LIMIT 1"

13:09:36 [Information] :: Executed DbCommand ("25"ms) [Parameters=["@p7='?' (DbType = Int64), @p0='?', @p1='?', @p2='?' (DbType = DateTime), @p3='?', @p4='?', @p5='?' (DbType = DateTime), @p6='?'"], CommandType='Text', CommandTimeout='30']"
""UPDATE \"PushTokens\" SET \"AppName\" = @p0, \"CreatedBy\" = @p1, \"CreatedOn\" = @p2, \"Token\" = @p3, \"UpdatedBy\" = @p4, \"UpdatedOn\" = @p5, \"UserId\" = @p6
WHERE \"Id\" = @p7;"

13:09:36 [Information] :: Executing "OkObjectResult", writing value of type '"<>f__AnonymousType1`3[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[NotificationService.Core.Models.PushTokenModel, NotificationService.Core, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]"'.

13:09:36 [Information] :: Executed action "NotificationService.API.Controllers.UserController.PushToken (NotificationService.API)" in 1167.5584ms

13:09:36 [Information] :: Executed endpoint '"NotificationService.API.Controllers.UserController.PushToken (NotificationService.API)"'

13:09:37 [Information] :: Request finished "HTTP/1.1" "POST" "https"://"localhost:7044""""/api/User/create-push-token""" - 200 null "application/json; charset=utf-8" 2170.6788ms

13:09:47 [Information] :: Request starting "HTTP/1.1" "POST" "https"://"localhost:7044""""/api/Notification/send""" - "application/json" 516

13:09:47 [Information] :: Executing endpoint '"NotificationService.API.Controllers.NotificationController.Send (NotificationService.API)"'

13:09:47 [Information] :: Route matched with "{action = \"Send\", controller = \"Notification\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Send(NotificationService.Core.Models.NotificationMessageModel)" on controller "NotificationService.API.Controllers.NotificationController" ("NotificationService.API").

13:09:48 [Information] :: Firebase Admin SDK initialized successfully

13:09:48 [Information] :: Executed DbCommand ("1"ms) [Parameters=["@__model_UserId_0='?', @__ToString_1='?'"], CommandType='Text', CommandTimeout='30']"
""SELECT p.\"Id\", p.\"AppName\", p.\"CreatedBy\", p.\"CreatedOn\", p.\"Token\", p.\"UpdatedBy\", p.\"UpdatedOn\", p.\"UserId\"
FROM \"PushTokens\" AS p
WHERE p.\"UserId\" = @__model_UserId_0 AND p.\"AppName\" = @__ToString_1"

13:09:51 [Information] :: MailGun response {"id":"<<EMAIL>>","message":"Queued. Thank you."}


13:10:08 [Error] :: Firebase messaging error: InvalidArgument - "The registration token is not a valid FCM registration token"
FirebaseAdmin.Messaging.FirebaseMessagingException: The registration token is not a valid FCM registration token
   at FirebaseAdmin.Util.ErrorHandlingHttpClient`1.SendAndReadAsync(HttpRequestMessage request, CancellationToken cancellationToken)
   at FirebaseAdmin.Util.ErrorHandlingHttpClient`1.SendAndDeserializeAsync[TResult](HttpRequestMessage request, CancellationToken cancellationToken)
   at FirebaseAdmin.Messaging.FirebaseMessagingClient.SendAsync(Message message, Boolean dryRun, CancellationToken cancellationToken)
   at FirebaseAdmin.Messaging.FirebaseMessaging.SendAsync(Message message, Boolean dryRun, CancellationToken cancellationToken)
   at FirebaseAdmin.Messaging.FirebaseMessaging.SendAsync(Message message, Boolean dryRun)
   at FirebaseAdmin.Messaging.FirebaseMessaging.SendAsync(Message message)
   at NotificationService.Infrastructure.Services.FirebasePushNotificationService.SendMessage(NotificationMessageModel model) in C:\Users\<USER>\source\repos\Notification-Service\src\NotificationService.Infrastructure\Services\FirebasePushMessageService.cs:line 89

13:10:08 [Information] :: Executed DbCommand ("15"ms) [Parameters=["@p0='?', @p1='?', @p2='?' (DbType = DateTime), @p3='?', @p4='?', @p5='?', @p6='?', @p7='?', @p8='?', @p9='?', @p10='?', @p11='?', @p12='?', @p13='?', @p14='?', @p15='?', @p16='?', @p17='?' (DbType = DateTime), @p18='?', @p19='?', @p20='?', @p21='?' (DbType = DateTime), @p22='?', @p23='?', @p24='?', @p25='?', @p26='?', @p27='?', @p28='?', @p29='?', @p30='?', @p31='?', @p32='?', @p33='?', @p34='?', @p35='?', @p36='?' (DbType = DateTime), @p37='?'"], CommandType='Text', CommandTimeout='30']"
""INSERT INTO \"Notifications\" (\"Body\", \"CreatedBy\", \"CreatedOn\", \"Email\", \"MessageId\", \"MessageSid\", \"NotificationType\", \"PhoneNumber\", \"Priority\", \"PushToken\", \"ResponseId\", \"Status\", \"StatusCode\", \"StatusMessage\", \"Subject\", \"Template\", \"UpdatedBy\", \"UpdatedOn\", \"UserId\")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16, @p17, @p18)
RETURNING \"Id\";
INSERT INTO \"Notifications\" (\"Body\", \"CreatedBy\", \"CreatedOn\", \"Email\", \"MessageId\", \"MessageSid\", \"NotificationType\", \"PhoneNumber\", \"Priority\", \"PushToken\", \"ResponseId\", \"Status\", \"StatusCode\", \"StatusMessage\", \"Subject\", \"Template\", \"UpdatedBy\", \"UpdatedOn\", \"UserId\")
VALUES (@p19, @p20, @p21, @p22, @p23, @p24, @p25, @p26, @p27, @p28, @p29, @p30, @p31, @p32, @p33, @p34, @p35, @p36, @p37)
RETURNING \"Id\";"

13:10:08 [Information] :: Executing "OkObjectResult", writing value of type '"System.String"'.

13:10:08 [Information] :: Executed action "NotificationService.API.Controllers.NotificationController.Send (NotificationService.API)" in 20979.6178ms

13:10:08 [Information] :: Executed endpoint '"NotificationService.API.Controllers.NotificationController.Send (NotificationService.API)"'

13:10:08 [Information] :: Request finished "HTTP/1.1" "POST" "https"://"localhost:7044""""/api/Notification/send""" - 200 null "text/plain; charset=utf-8" 21032.2189ms

17:39:10 [Information] :: Executed DbCommand ("29"ms) [Parameters=[""], CommandType='Text', CommandTimeout='30']"
""SELECT \"MigrationId\", \"ProductVersion\"
FROM \"__EFMigrationsHistory\"
ORDER BY \"MigrationId\";"

17:39:11 [Information] :: User profile is available. Using '"C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys"' as key repository and Windows DPAPI to encrypt keys at rest.

17:39:12 [Information] :: Now listening on: "https://localhost:7044"

17:39:12 [Information] :: Now listening on: "http://localhost:5179"

17:39:12 [Information] :: Application started. Press Ctrl+C to shut down.

17:39:12 [Information] :: Hosting environment: "Development"

17:39:12 [Information] :: Content root path: "C:\Users\<USER>\source\repos\Notification-Service\src\NotificationService.API"

17:39:13 [Information] :: Request starting "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/index.html""" - null null

17:39:13 [Information] :: Request finished "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/index.html""" - 200 null "text/html;charset=utf-8" 503.718ms

17:39:13 [Information] :: Request starting "HTTP/2" "GET" "https"://"localhost:7044""""/_framework/aspnetcore-browser-refresh.js""" - null null

17:39:13 [Information] :: Request finished "HTTP/2" "GET" "https"://"localhost:7044""""/_framework/aspnetcore-browser-refresh.js""" - 200 13782 "application/javascript; charset=utf-8" 17.2458ms

17:39:13 [Information] :: Request starting "HTTP/2" "GET" "https"://"localhost:7044""""/_vs/browserLink""" - null null

17:39:13 [Information] :: Request finished "HTTP/2" "GET" "https"://"localhost:7044""""/_vs/browserLink""" - 200 null "text/javascript; charset=UTF-8" 100.356ms

17:39:14 [Information] :: Request starting "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/v1/swagger.json""" - null null

17:39:15 [Information] :: Request finished "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/v1/swagger.json""" - 200 null "application/json;charset=utf-8" 373.3154ms

17:39:43 [Information] :: Request starting "HTTP/1.1" "POST" "https"://"localhost:7044""""/api/Notification/queue""" - "application/json" 799

17:39:44 [Information] :: Executing endpoint '"NotificationService.API.Controllers.NotificationController.Queue (NotificationService.API)"'

17:39:44 [Information] :: Route matched with "{action = \"Queue\", controller = \"Notification\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Queue(NotificationService.Core.Models.NotificationMessageModel)" on controller "NotificationService.API.Controllers.NotificationController" ("NotificationService.API").

17:39:45 [Information] :: Firebase Admin SDK initialized successfully

17:41:03 [Information] :: Executed DbCommand ("27"ms) [Parameters=[""], CommandType='Text', CommandTimeout='30']"
""SELECT \"MigrationId\", \"ProductVersion\"
FROM \"__EFMigrationsHistory\"
ORDER BY \"MigrationId\";"

17:41:04 [Information] :: User profile is available. Using '"C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys"' as key repository and Windows DPAPI to encrypt keys at rest.

17:41:04 [Information] :: Now listening on: "https://localhost:7044"

17:41:04 [Information] :: Now listening on: "http://localhost:5179"

17:41:05 [Information] :: Application started. Press Ctrl+C to shut down.

17:41:05 [Information] :: Hosting environment: "Development"

17:41:05 [Information] :: Content root path: "C:\Users\<USER>\source\repos\Notification-Service\src\NotificationService.API"

17:41:05 [Information] :: Request starting "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/index.html""" - null null

17:41:06 [Information] :: Request finished "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/index.html""" - 200 null "text/html;charset=utf-8" 618.5079ms

17:41:06 [Information] :: Request starting "HTTP/2" "GET" "https"://"localhost:7044""""/_framework/aspnetcore-browser-refresh.js""" - null null

17:41:06 [Information] :: Request starting "HTTP/2" "GET" "https"://"localhost:7044""""/_vs/browserLink""" - null null

17:41:06 [Information] :: Request finished "HTTP/2" "GET" "https"://"localhost:7044""""/_framework/aspnetcore-browser-refresh.js""" - 200 13782 "application/javascript; charset=utf-8" 21.1006ms

17:41:06 [Information] :: Request finished "HTTP/2" "GET" "https"://"localhost:7044""""/_vs/browserLink""" - 200 null "text/javascript; charset=UTF-8" 103.4853ms

17:41:07 [Information] :: Request starting "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/v1/swagger.json""" - null null

17:41:07 [Information] :: Request finished "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/v1/swagger.json""" - 200 null "application/json;charset=utf-8" 384.5519ms

17:41:16 [Information] :: Request starting "HTTP/1.1" "POST" "https"://"localhost:7044""""/api/Notification/queue""" - "application/json" 799

17:41:17 [Information] :: Executing endpoint '"NotificationService.API.Controllers.NotificationController.Queue (NotificationService.API)"'

17:41:17 [Information] :: Route matched with "{action = \"Queue\", controller = \"Notification\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Queue(NotificationService.Core.Models.NotificationMessageModel)" on controller "NotificationService.API.Controllers.NotificationController" ("NotificationService.API").

17:41:17 [Information] :: Firebase Admin SDK initialized successfully

17:41:24 [Information] :: Successfully published message to RabbitMQ queue: "notification"

17:41:25 [Information] :: Executing "OkObjectResult", writing value of type '"System.String"'.

17:41:25 [Information] :: Executed action "NotificationService.API.Controllers.NotificationController.Queue (NotificationService.API)" in 8163.4192ms

17:41:25 [Information] :: Executed endpoint '"NotificationService.API.Controllers.NotificationController.Queue (NotificationService.API)"'

17:41:26 [Information] :: Request finished "HTTP/1.1" "POST" "https"://"localhost:7044""""/api/Notification/queue""" - 200 null "text/plain; charset=utf-8" 9384.5916ms

17:46:30 [Information] :: Executed DbCommand ("22"ms) [Parameters=[""], CommandType='Text', CommandTimeout='30']"
""SELECT \"MigrationId\", \"ProductVersion\"
FROM \"__EFMigrationsHistory\"
ORDER BY \"MigrationId\";"

17:46:31 [Information] :: User profile is available. Using '"C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys"' as key repository and Windows DPAPI to encrypt keys at rest.

17:46:31 [Information] :: Now listening on: "https://localhost:7044"

17:46:31 [Information] :: Now listening on: "http://localhost:5179"

17:46:31 [Information] :: Application started. Press Ctrl+C to shut down.

17:46:31 [Information] :: Hosting environment: "Development"

17:46:31 [Information] :: Content root path: "C:\Users\<USER>\source\repos\Notification-Service\src\NotificationService.API"

17:46:33 [Information] :: Request starting "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/index.html""" - null null

17:46:34 [Information] :: Request finished "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/index.html""" - 200 null "text/html;charset=utf-8" 351.6206ms

17:46:34 [Information] :: Request starting "HTTP/2" "GET" "https"://"localhost:7044""""/_framework/aspnetcore-browser-refresh.js""" - null null

17:46:34 [Information] :: Request starting "HTTP/2" "GET" "https"://"localhost:7044""""/_vs/browserLink""" - null null

17:46:34 [Information] :: Request finished "HTTP/2" "GET" "https"://"localhost:7044""""/_framework/aspnetcore-browser-refresh.js""" - 200 13782 "application/javascript; charset=utf-8" 21.4917ms

17:46:34 [Information] :: Request finished "HTTP/2" "GET" "https"://"localhost:7044""""/_vs/browserLink""" - 200 null "text/javascript; charset=UTF-8" 70.0843ms

17:46:35 [Information] :: Request starting "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/v1/swagger.json""" - null null

17:46:35 [Information] :: Request finished "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/v1/swagger.json""" - 200 null "application/json;charset=utf-8" 371.0409ms

17:51:29 [Information] :: Executed DbCommand ("21"ms) [Parameters=[""], CommandType='Text', CommandTimeout='30']"
""SELECT \"MigrationId\", \"ProductVersion\"
FROM \"__EFMigrationsHistory\"
ORDER BY \"MigrationId\";"

17:51:29 [Information] :: User profile is available. Using '"C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys"' as key repository and Windows DPAPI to encrypt keys at rest.

17:51:29 [Error] :: Hosting failed to start
System.NullReferenceException: Object reference not set to an instance of an object.
   at Microsoft.Extensions.Hosting.Internal.Host.<StartAsync>b__15_1(IHostedService service, CancellationToken token)
   at Microsoft.Extensions.Hosting.Internal.Host.ForeachService[T](IEnumerable`1 services, CancellationToken token, Boolean concurrent, Boolean abortOnFirstException, List`1 exceptions, Func`3 operation)

17:56:17 [Information] :: Executed DbCommand ("56"ms) [Parameters=[""], CommandType='Text', CommandTimeout='30']"
""SELECT \"MigrationId\", \"ProductVersion\"
FROM \"__EFMigrationsHistory\"
ORDER BY \"MigrationId\";"

17:56:18 [Information] :: User profile is available. Using '"C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys"' as key repository and Windows DPAPI to encrypt keys at rest.

17:56:18 [Information] :: Now listening on: "https://localhost:7044"

17:56:18 [Information] :: Now listening on: "http://localhost:5179"

17:56:18 [Information] :: Application started. Press Ctrl+C to shut down.

17:56:18 [Information] :: Hosting environment: "Development"

17:56:18 [Information] :: Content root path: "C:\Users\<USER>\source\repos\Notification-Service\src\NotificationService.API"

17:56:20 [Information] :: Request starting "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/index.html""" - null null

17:56:20 [Information] :: Request finished "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/index.html""" - 200 null "text/html;charset=utf-8" 388.6094ms

17:56:20 [Information] :: Request starting "HTTP/2" "GET" "https"://"localhost:7044""""/_framework/aspnetcore-browser-refresh.js""" - null null

17:56:20 [Information] :: Request starting "HTTP/2" "GET" "https"://"localhost:7044""""/_vs/browserLink""" - null null

17:56:20 [Information] :: Request finished "HTTP/2" "GET" "https"://"localhost:7044""""/_framework/aspnetcore-browser-refresh.js""" - 200 13782 "application/javascript; charset=utf-8" 34.3269ms

17:56:20 [Information] :: Request finished "HTTP/2" "GET" "https"://"localhost:7044""""/_vs/browserLink""" - 200 null "text/javascript; charset=UTF-8" 99.9094ms

17:56:22 [Information] :: Request starting "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/v1/swagger.json""" - null null

17:56:22 [Information] :: Request finished "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/v1/swagger.json""" - 200 null "application/json;charset=utf-8" 348.7579ms

17:57:24 [Information] :: Executed DbCommand ("21"ms) [Parameters=[""], CommandType='Text', CommandTimeout='30']"
""SELECT \"MigrationId\", \"ProductVersion\"
FROM \"__EFMigrationsHistory\"
ORDER BY \"MigrationId\";"

17:57:24 [Information] :: User profile is available. Using '"C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys"' as key repository and Windows DPAPI to encrypt keys at rest.

17:57:24 [Information] :: Now listening on: "https://localhost:7044"

17:57:24 [Information] :: Now listening on: "http://localhost:5179"

17:57:24 [Information] :: Application started. Press Ctrl+C to shut down.

17:57:24 [Information] :: Hosting environment: "Development"

17:57:24 [Information] :: Content root path: "C:\Users\<USER>\source\repos\Notification-Service\src\NotificationService.API"

17:57:27 [Information] :: Request starting "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/index.html""" - null null

17:57:27 [Information] :: Request finished "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/index.html""" - 200 null "text/html;charset=utf-8" 333.2521ms

17:57:27 [Information] :: Request starting "HTTP/2" "GET" "https"://"localhost:7044""""/_vs/browserLink""" - null null

17:57:27 [Information] :: Request starting "HTTP/2" "GET" "https"://"localhost:7044""""/_framework/aspnetcore-browser-refresh.js""" - null null

17:57:27 [Information] :: Request finished "HTTP/2" "GET" "https"://"localhost:7044""""/_framework/aspnetcore-browser-refresh.js""" - 200 13782 "application/javascript; charset=utf-8" 43.5567ms

17:57:27 [Information] :: Request finished "HTTP/2" "GET" "https"://"localhost:7044""""/_vs/browserLink""" - 200 null "text/javascript; charset=UTF-8" 99.0674ms

17:57:28 [Information] :: Request starting "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/v1/swagger.json""" - null null

17:57:28 [Information] :: Request finished "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/v1/swagger.json""" - 200 null "application/json;charset=utf-8" 225.977ms

18:00:25 [Information] :: Executed DbCommand ("42"ms) [Parameters=[""], CommandType='Text', CommandTimeout='30']"
""SELECT \"MigrationId\", \"ProductVersion\"
FROM \"__EFMigrationsHistory\"
ORDER BY \"MigrationId\";"

18:00:26 [Information] :: User profile is available. Using '"C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys"' as key repository and Windows DPAPI to encrypt keys at rest.

18:00:26 [Information] :: Now listening on: "https://localhost:7044"

18:00:26 [Information] :: Now listening on: "http://localhost:5179"

18:00:26 [Information] :: Application started. Press Ctrl+C to shut down.

18:00:26 [Information] :: Hosting environment: "Development"

18:00:26 [Information] :: Content root path: "C:\Users\<USER>\source\repos\Notification-Service\src\NotificationService.API"

18:00:26 [Information] :: Request starting "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/index.html""" - null null

18:00:27 [Information] :: Request finished "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/index.html""" - 200 null "text/html;charset=utf-8" 473.8749ms

18:00:27 [Information] :: Request starting "HTTP/2" "GET" "https"://"localhost:7044""""/_framework/aspnetcore-browser-refresh.js""" - null null

18:00:27 [Information] :: Request starting "HTTP/2" "GET" "https"://"localhost:7044""""/_vs/browserLink""" - null null

18:00:27 [Information] :: Request finished "HTTP/2" "GET" "https"://"localhost:7044""""/_framework/aspnetcore-browser-refresh.js""" - 200 13782 "application/javascript; charset=utf-8" 103.6153ms

18:00:27 [Information] :: Request finished "HTTP/2" "GET" "https"://"localhost:7044""""/_vs/browserLink""" - 200 null "text/javascript; charset=UTF-8" 212.6703ms

18:00:28 [Information] :: Request starting "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/v1/swagger.json""" - null null

18:00:29 [Information] :: Request finished "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/v1/swagger.json""" - 200 null "application/json;charset=utf-8" 307.7448ms

18:01:51 [Information] :: Executed DbCommand ("25"ms) [Parameters=[""], CommandType='Text', CommandTimeout='30']"
""SELECT \"MigrationId\", \"ProductVersion\"
FROM \"__EFMigrationsHistory\"
ORDER BY \"MigrationId\";"

18:01:51 [Information] :: Firebase Admin SDK initialized successfully

18:01:52 [Information] :: RabbitMQ Broker Url: amqps://xruwodqv:<EMAIL>/xruwodqv

18:01:52 [Information] :: RabbitMQ Broker Url: amqps://xruwodqv:<EMAIL>/xruwodqv

18:01:52 [Information] :: User profile is available. Using '"C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys"' as key repository and Windows DPAPI to encrypt keys at rest.

18:02:06 [Information] :: Now listening on: "https://localhost:7044"

18:02:06 [Information] :: Now listening on: "http://localhost:5179"

18:02:06 [Information] :: Application started. Press Ctrl+C to shut down.

18:02:06 [Information] :: Hosting environment: "Development"

18:02:06 [Information] :: Content root path: "C:\Users\<USER>\source\repos\Notification-Service\src\NotificationService.API"

18:02:07 [Information] :: Request starting "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/index.html""" - null null

18:02:08 [Information] :: Request finished "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/index.html""" - 200 null "text/html;charset=utf-8" 391.369ms

18:02:08 [Information] :: Request starting "HTTP/2" "GET" "https"://"localhost:7044""""/_framework/aspnetcore-browser-refresh.js""" - null null

18:02:08 [Information] :: Request starting "HTTP/2" "GET" "https"://"localhost:7044""""/_vs/browserLink""" - null null

18:02:08 [Information] :: Request finished "HTTP/2" "GET" "https"://"localhost:7044""""/_framework/aspnetcore-browser-refresh.js""" - 200 13782 "application/javascript; charset=utf-8" 71.3822ms

18:02:08 [Information] :: Request finished "HTTP/2" "GET" "https"://"localhost:7044""""/_vs/browserLink""" - 200 null "text/javascript; charset=UTF-8" 210.1119ms

18:02:09 [Information] :: Request starting "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/v1/swagger.json""" - null null

18:02:09 [Information] :: Request finished "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/v1/swagger.json""" - 200 null "application/json;charset=utf-8" 354.2299ms

18:07:22 [Information] :: Executed DbCommand ("35"ms) [Parameters=[""], CommandType='Text', CommandTimeout='30']"
""SELECT \"MigrationId\", \"ProductVersion\"
FROM \"__EFMigrationsHistory\"
ORDER BY \"MigrationId\";"

18:07:23 [Information] :: Firebase Admin SDK initialized successfully

18:07:23 [Information] :: RabbitMQ Broker Url: amqps://xruwodqv:<EMAIL>/xruwodqv

18:07:23 [Information] :: RabbitMQ Broker Url: amqps://xruwodqv:<EMAIL>/xruwodqv

18:07:23 [Information] :: User profile is available. Using '"C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys"' as key repository and Windows DPAPI to encrypt keys at rest.

18:07:27 [Information] :: Now listening on: "https://localhost:7044"

18:07:27 [Information] :: Now listening on: "http://localhost:5179"

18:07:27 [Information] :: Application started. Press Ctrl+C to shut down.

18:07:27 [Information] :: Hosting environment: "Development"

18:07:27 [Information] :: Content root path: "C:\Users\<USER>\source\repos\Notification-Service\src\NotificationService.API"

18:07:28 [Information] :: Request starting "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/index.html""" - null null

18:07:28 [Information] :: Request finished "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/index.html""" - 200 null "text/html;charset=utf-8" 364.6718ms

18:07:28 [Information] :: Request starting "HTTP/2" "GET" "https"://"localhost:7044""""/_vs/browserLink""" - null null

18:07:28 [Information] :: Request starting "HTTP/2" "GET" "https"://"localhost:7044""""/_framework/aspnetcore-browser-refresh.js""" - null null

18:07:28 [Information] :: Request finished "HTTP/2" "GET" "https"://"localhost:7044""""/_framework/aspnetcore-browser-refresh.js""" - 200 13782 "application/javascript; charset=utf-8" 43.346ms

18:07:28 [Information] :: Request finished "HTTP/2" "GET" "https"://"localhost:7044""""/_vs/browserLink""" - 200 null "text/javascript; charset=UTF-8" 105.2139ms

18:07:29 [Information] :: Request starting "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/v1/swagger.json""" - null null

18:07:30 [Information] :: Request finished "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/v1/swagger.json""" - 200 null "application/json;charset=utf-8" 308.9104ms

18:08:34 [Information] :: Request starting "HTTP/1.1" "POST" "https"://"localhost:7044""""/api/Notification/queue""" - "application/json" 799

18:08:34 [Information] :: Executing endpoint '"NotificationService.API.Controllers.NotificationController.Queue (NotificationService.API)"'

18:08:34 [Information] :: Route matched with "{action = \"Queue\", controller = \"Notification\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Queue(NotificationService.Core.Models.NotificationMessageModel)" on controller "NotificationService.API.Controllers.NotificationController" ("NotificationService.API").

18:08:39 [Information] :: Successfully published message to RabbitMQ queue: "notification-message-queue"

18:08:40 [Information] :: Executing "OkObjectResult", writing value of type '"System.String"'.

18:08:40 [Information] :: Executed action "NotificationService.API.Controllers.NotificationController.Queue (NotificationService.API)" in 5471.1951ms

18:08:47 [Information] :: Notification Message "{\"MessageId\":\"\",\"Body\":\"\",\"TemplateWithParams\":\"a bipedal primate mammal (Homo sapiens) that is anatomically related to the great apes but distinguished especially by notable development of the brain with a resultant capacity for articulate (see ARTICULATE entry 1 sense 1a) speech and abstract reasoning, and is the sole living representative of the hominid family\",\"Subject\":\"Three Onboarding Invitation: Onboarding New Hire.\",\"RecipientEmails\":[\"<EMAIL>\"],\"PhoneNumber\":\"\",\"PushToken\":\"\",\"CC\":[],\"UserId\":\"c964a01d-d0f3-4553-b53d-07e773d66525\",\"Application\":1,\"Attachments\":[],\"NotificationTypes\":[1,3],\"Priority\":0,\"IsWhatsappNo\":false,\"TemplateWithPlaceHolders\":null}"

18:08:47 [Information] :: Executed endpoint '"NotificationService.API.Controllers.NotificationController.Queue (NotificationService.API)"'

18:08:48 [Information] :: Request finished "HTTP/1.1" "POST" "https"://"localhost:7044""""/api/Notification/queue""" - 200 null "text/plain; charset=utf-8" 13787.2317ms

18:08:49 [Information] :: Executed DbCommand ("44"ms) [Parameters=["@__model_UserId_0='?', @__ToString_1='?'"], CommandType='Text', CommandTimeout='30']"
""SELECT p.\"Id\", p.\"AppName\", p.\"CreatedBy\", p.\"CreatedOn\", p.\"Token\", p.\"UpdatedBy\", p.\"UpdatedOn\", p.\"UserId\"
FROM \"PushTokens\" AS p
WHERE p.\"UserId\" = @__model_UserId_0 AND p.\"AppName\" = @__ToString_1"

18:08:49 [Error] :: Error getting message processors Cannot access a disposed object.
Object name: 'IServiceProvider'.

18:08:49 [Information] :: notification-message-queue message received: {"MessageId":"","Body":"","TemplateWithParams":"a bipedal primate mammal (Homo sapiens) that is anatomically related to the great apes but distinguished especially by notable development of the brain with a resultant capacity for articulate (see ARTICULATE entry 1 sense 1a) speech and abstract reasoning, and is the sole living representative of the hominid family","Subject":"Three Onboarding Invitation: Onboarding New Hire.","RecipientEmails":["<EMAIL>"],"PhoneNumber":"","PushToken":"","CC":[],"UserId":"c964a01d-d0f3-4553-b53d-07e773d66525","Application":1,"Attachments":[],"NotificationTypes":[1,3],"Priority":0,"IsWhatsappNo":false,"TemplateWithPlaceHolders":null}

18:09:01 [Information] :: Notification Message "{\"MessageId\":\"\",\"Body\":\"\",\"TemplateWithParams\":\"a bipedal primate mammal (Homo sapiens) that is anatomically related to the great apes but distinguished especially by notable development of the brain with a resultant capacity for articulate (see ARTICULATE entry 1 sense 1a) speech and abstract reasoning, and is the sole living representative of the hominid family\",\"Subject\":\"Three Onboarding Invitation: Onboarding New Hire.\",\"RecipientEmails\":[\"<EMAIL>\"],\"PhoneNumber\":\"\",\"PushToken\":\"\",\"CC\":[],\"UserId\":\"c964a01d-d0f3-4553-b53d-07e773d66525\",\"Application\":1,\"Attachments\":[],\"NotificationTypes\":[1,3],\"Priority\":0,\"IsWhatsappNo\":false,\"TemplateWithPlaceHolders\":null}"

18:09:01 [Information] :: Executed DbCommand ("1"ms) [Parameters=["@__model_UserId_0='?', @__ToString_1='?'"], CommandType='Text', CommandTimeout='30']"
""SELECT p.\"Id\", p.\"AppName\", p.\"CreatedBy\", p.\"CreatedOn\", p.\"Token\", p.\"UpdatedBy\", p.\"UpdatedOn\", p.\"UserId\"
FROM \"PushTokens\" AS p
WHERE p.\"UserId\" = @__model_UserId_0 AND p.\"AppName\" = @__ToString_1"

18:09:01 [Error] :: Error getting message processors Cannot access a disposed object.
Object name: 'IServiceProvider'.

18:09:01 [Information] :: notification-message-queue message received: {"MessageId":"","Body":"","TemplateWithParams":"a bipedal primate mammal (Homo sapiens) that is anatomically related to the great apes but distinguished especially by notable development of the brain with a resultant capacity for articulate (see ARTICULATE entry 1 sense 1a) speech and abstract reasoning, and is the sole living representative of the hominid family","Subject":"Three Onboarding Invitation: Onboarding New Hire.","RecipientEmails":["<EMAIL>"],"PhoneNumber":"","PushToken":"","CC":[],"UserId":"c964a01d-d0f3-4553-b53d-07e773d66525","Application":1,"Attachments":[],"NotificationTypes":[1,3],"Priority":0,"IsWhatsappNo":false,"TemplateWithPlaceHolders":null}

18:09:16 [Information] :: Notification Message "{\"MessageId\":\"\",\"Body\":\"\",\"TemplateWithParams\":\"a bipedal primate mammal (Homo sapiens) that is anatomically related to the great apes but distinguished especially by notable development of the brain with a resultant capacity for articulate (see ARTICULATE entry 1 sense 1a) speech and abstract reasoning, and is the sole living representative of the hominid family\",\"Subject\":\"Three Onboarding Invitation: Onboarding New Hire.\",\"RecipientEmails\":[\"<EMAIL>\"],\"PhoneNumber\":\"\",\"PushToken\":\"\",\"CC\":[],\"UserId\":\"c964a01d-d0f3-4553-b53d-07e773d66525\",\"Application\":1,\"Attachments\":[],\"NotificationTypes\":[1,3],\"Priority\":0,\"IsWhatsappNo\":false,\"TemplateWithPlaceHolders\":null}"

18:09:30 [Information] :: Executed DbCommand ("1"ms) [Parameters=["@__model_UserId_0='?', @__ToString_1='?'"], CommandType='Text', CommandTimeout='30']"
""SELECT p.\"Id\", p.\"AppName\", p.\"CreatedBy\", p.\"CreatedOn\", p.\"Token\", p.\"UpdatedBy\", p.\"UpdatedOn\", p.\"UserId\"
FROM \"PushTokens\" AS p
WHERE p.\"UserId\" = @__model_UserId_0 AND p.\"AppName\" = @__ToString_1"

18:14:52 [Information] :: Executed DbCommand ("63"ms) [Parameters=[""], CommandType='Text', CommandTimeout='30']"
""SELECT \"MigrationId\", \"ProductVersion\"
FROM \"__EFMigrationsHistory\"
ORDER BY \"MigrationId\";"

18:14:53 [Information] :: Firebase Admin SDK initialized successfully

18:14:53 [Information] :: RabbitMQ Broker Url: amqps://xruwodqv:<EMAIL>/xruwodqv

18:14:53 [Information] :: RabbitMQ Broker Url: amqps://xruwodqv:<EMAIL>/xruwodqv

18:14:53 [Information] :: User profile is available. Using '"C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys"' as key repository and Windows DPAPI to encrypt keys at rest.

18:14:54 [Information] :: Now listening on: "https://localhost:7044"

18:14:54 [Information] :: Now listening on: "http://localhost:5179"

18:14:54 [Information] :: Application started. Press Ctrl+C to shut down.

18:14:54 [Information] :: Hosting environment: "Development"

18:14:54 [Information] :: Content root path: "C:\Users\<USER>\source\repos\Notification-Service\src\NotificationService.API"

18:14:55 [Information] :: Request starting "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/index.html""" - null null

18:14:57 [Information] :: Request finished "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/index.html""" - 200 null "text/html;charset=utf-8" 2106.8097ms

18:14:57 [Information] :: Request starting "HTTP/2" "GET" "https"://"localhost:7044""""/_framework/aspnetcore-browser-refresh.js""" - null null

18:14:57 [Information] :: Request starting "HTTP/2" "GET" "https"://"localhost:7044""""/_vs/browserLink""" - null null

18:14:57 [Information] :: Request finished "HTTP/2" "GET" "https"://"localhost:7044""""/_framework/aspnetcore-browser-refresh.js""" - 200 13782 "application/javascript; charset=utf-8" 19.4928ms

18:14:57 [Information] :: Request finished "HTTP/2" "GET" "https"://"localhost:7044""""/_vs/browserLink""" - 200 null "text/javascript; charset=UTF-8" 79.3671ms

18:14:59 [Information] :: Request starting "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/v1/swagger.json""" - null null

18:14:59 [Information] :: Request finished "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/v1/swagger.json""" - 200 null "application/json;charset=utf-8" 297.6802ms

18:15:04 [Information] :: Notification Message "{\"MessageId\":\"\",\"Body\":\"\",\"TemplateWithParams\":\"a bipedal primate mammal (Homo sapiens) that is anatomically related to the great apes but distinguished especially by notable development of the brain with a resultant capacity for articulate (see ARTICULATE entry 1 sense 1a) speech and abstract reasoning, and is the sole living representative of the hominid family\",\"Subject\":\"Three Onboarding Invitation: Onboarding New Hire.\",\"RecipientEmails\":[\"<EMAIL>\"],\"PhoneNumber\":\"\",\"PushToken\":\"\",\"CC\":[],\"UserId\":\"c964a01d-d0f3-4553-b53d-07e773d66525\",\"Application\":1,\"Attachments\":[],\"NotificationTypes\":[1,3],\"Priority\":0,\"IsWhatsappNo\":false,\"TemplateWithPlaceHolders\":null}"

18:15:05 [Information] :: Executed DbCommand ("34"ms) [Parameters=["@__model_UserId_0='?', @__ToString_1='?'"], CommandType='Text', CommandTimeout='30']"
""SELECT p.\"Id\", p.\"AppName\", p.\"CreatedBy\", p.\"CreatedOn\", p.\"Token\", p.\"UpdatedBy\", p.\"UpdatedOn\", p.\"UserId\"
FROM \"PushTokens\" AS p
WHERE p.\"UserId\" = @__model_UserId_0 AND p.\"AppName\" = @__ToString_1"

18:20:30 [Information] :: Executed DbCommand ("42"ms) [Parameters=[""], CommandType='Text', CommandTimeout='30']"
""SELECT \"MigrationId\", \"ProductVersion\"
FROM \"__EFMigrationsHistory\"
ORDER BY \"MigrationId\";"

18:20:31 [Information] :: Firebase Admin SDK initialized successfully

18:20:31 [Information] :: RabbitMQ Broker Url: amqps://xruwodqv:<EMAIL>/xruwodqv

18:20:31 [Information] :: RabbitMQ Broker Url: amqps://xruwodqv:<EMAIL>/xruwodqv

18:20:31 [Information] :: User profile is available. Using '"C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys"' as key repository and Windows DPAPI to encrypt keys at rest.

18:20:31 [Information] :: Now listening on: "https://localhost:7044"

18:20:31 [Information] :: Now listening on: "http://localhost:5179"

18:20:31 [Information] :: Application started. Press Ctrl+C to shut down.

18:20:31 [Information] :: Hosting environment: "Development"

18:20:31 [Information] :: Content root path: "C:\Users\<USER>\source\repos\Notification-Service\src\NotificationService.API"

18:20:32 [Information] :: Request starting "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/index.html""" - null null

18:20:33 [Information] :: Request finished "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/index.html""" - 200 null "text/html;charset=utf-8" 309.7022ms

18:20:33 [Information] :: Request starting "HTTP/2" "GET" "https"://"localhost:7044""""/_vs/browserLink""" - null null

18:20:33 [Information] :: Request starting "HTTP/2" "GET" "https"://"localhost:7044""""/_framework/aspnetcore-browser-refresh.js""" - null null

18:20:33 [Information] :: Request finished "HTTP/2" "GET" "https"://"localhost:7044""""/_framework/aspnetcore-browser-refresh.js""" - 200 13782 "application/javascript; charset=utf-8" 31.8056ms

18:20:33 [Information] :: Request finished "HTTP/2" "GET" "https"://"localhost:7044""""/_vs/browserLink""" - 200 null "text/javascript; charset=UTF-8" 71.6292ms

18:20:34 [Information] :: Request starting "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/v1/swagger.json""" - null null

18:20:34 [Information] :: Request finished "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/v1/swagger.json""" - 200 null "application/json;charset=utf-8" 383.3765ms

18:20:39 [Information] :: Notification Message "{\"MessageId\":\"\",\"Body\":\"\",\"TemplateWithParams\":\"a bipedal primate mammal (Homo sapiens) that is anatomically related to the great apes but distinguished especially by notable development of the brain with a resultant capacity for articulate (see ARTICULATE entry 1 sense 1a) speech and abstract reasoning, and is the sole living representative of the hominid family\",\"Subject\":\"Three Onboarding Invitation: Onboarding New Hire.\",\"RecipientEmails\":[\"<EMAIL>\"],\"PhoneNumber\":\"\",\"PushToken\":\"\",\"CC\":[],\"UserId\":\"c964a01d-d0f3-4553-b53d-07e773d66525\",\"Application\":1,\"Attachments\":[],\"NotificationTypes\":[1,3],\"Priority\":0,\"IsWhatsappNo\":false,\"TemplateWithPlaceHolders\":null}"

18:20:39 [Information] :: notification-message-queue message received: {"MessageId":"","Body":"","TemplateWithParams":"a bipedal primate mammal (Homo sapiens) that is anatomically related to the great apes but distinguished especially by notable development of the brain with a resultant capacity for articulate (see ARTICULATE entry 1 sense 1a) speech and abstract reasoning, and is the sole living representative of the hominid family","Subject":"Three Onboarding Invitation: Onboarding New Hire.","RecipientEmails":["<EMAIL>"],"PhoneNumber":"","PushToken":"","CC":[],"UserId":"c964a01d-d0f3-4553-b53d-07e773d66525","Application":1,"Attachments":[],"NotificationTypes":[1,3],"Priority":0,"IsWhatsappNo":false,"TemplateWithPlaceHolders":null}

18:21:02 [Information] :: Notification Message "{\"MessageId\":\"\",\"Body\":\"\",\"TemplateWithParams\":\"a bipedal primate mammal (Homo sapiens) that is anatomically related to the great apes but distinguished especially by notable development of the brain with a resultant capacity for articulate (see ARTICULATE entry 1 sense 1a) speech and abstract reasoning, and is the sole living representative of the hominid family\",\"Subject\":\"Three Onboarding Invitation: Onboarding New Hire.\",\"RecipientEmails\":[\"<EMAIL>\"],\"PhoneNumber\":\"\",\"PushToken\":\"\",\"CC\":[],\"UserId\":\"c964a01d-d0f3-4553-b53d-07e773d66525\",\"Application\":1,\"Attachments\":[],\"NotificationTypes\":[1,3],\"Priority\":0,\"IsWhatsappNo\":false,\"TemplateWithPlaceHolders\":null}"

18:21:02 [Information] :: notification-message-queue message received: {"MessageId":"","Body":"","TemplateWithParams":"a bipedal primate mammal (Homo sapiens) that is anatomically related to the great apes but distinguished especially by notable development of the brain with a resultant capacity for articulate (see ARTICULATE entry 1 sense 1a) speech and abstract reasoning, and is the sole living representative of the hominid family","Subject":"Three Onboarding Invitation: Onboarding New Hire.","RecipientEmails":["<EMAIL>"],"PhoneNumber":"","PushToken":"","CC":[],"UserId":"c964a01d-d0f3-4553-b53d-07e773d66525","Application":1,"Attachments":[],"NotificationTypes":[1,3],"Priority":0,"IsWhatsappNo":false,"TemplateWithPlaceHolders":null}

18:21:14 [Information] :: Notification Message "{\"MessageId\":\"\",\"Body\":\"\",\"TemplateWithParams\":\"a bipedal primate mammal (Homo sapiens) that is anatomically related to the great apes but distinguished especially by notable development of the brain with a resultant capacity for articulate (see ARTICULATE entry 1 sense 1a) speech and abstract reasoning, and is the sole living representative of the hominid family\",\"Subject\":\"Three Onboarding Invitation: Onboarding New Hire.\",\"RecipientEmails\":[\"<EMAIL>\"],\"PhoneNumber\":\"\",\"PushToken\":\"\",\"CC\":[],\"UserId\":\"c964a01d-d0f3-4553-b53d-07e773d66525\",\"Application\":1,\"Attachments\":[],\"NotificationTypes\":[1,3],\"Priority\":0,\"IsWhatsappNo\":false,\"TemplateWithPlaceHolders\":null}"

18:21:44 [Information] :: notification-message-queue message received: {"MessageId":"","Body":"","TemplateWithParams":"a bipedal primate mammal (Homo sapiens) that is anatomically related to the great apes but distinguished especially by notable development of the brain with a resultant capacity for articulate (see ARTICULATE entry 1 sense 1a) speech and abstract reasoning, and is the sole living representative of the hominid family","Subject":"Three Onboarding Invitation: Onboarding New Hire.","RecipientEmails":["<EMAIL>"],"PhoneNumber":"","PushToken":"","CC":[],"UserId":"c964a01d-d0f3-4553-b53d-07e773d66525","Application":1,"Attachments":[],"NotificationTypes":[1,3],"Priority":0,"IsWhatsappNo":false,"TemplateWithPlaceHolders":null}

18:21:45 [Error] :: Error while starting consumer
System.TimeoutException: The operation has timed out.
   at RabbitMQ.Util.BlockingCell`1.WaitForValue(TimeSpan timeout)
   at RabbitMQ.Client.Impl.SimpleBlockingRpcContinuation.GetReply(TimeSpan timeout)
   at RabbitMQ.Client.Impl.ModelBase.ModelRpc(MethodBase method, ContentHeaderBase header, Byte[] body)
   at RabbitMQ.Client.Framing.Impl.Model.BasicQos(UInt32 prefetchSize, UInt16 prefetchCount, Boolean global)
   at RabbitMQ.Client.Impl.AutorecoveringModel.BasicQos(UInt32 prefetchSize, UInt16 prefetchCount, Boolean global)
   at NotificationService.Infrastructure.Services.BaseConsumer.StartConsumer() in C:\Users\<USER>\source\repos\Notification-Service\src\NotificationService.Infrastructure\Services\BaseConsumer.cs:line 56

18:21:53 [Information] :: Notification Message "{\"MessageId\":\"\",\"Body\":\"\",\"TemplateWithParams\":\"a bipedal primate mammal (Homo sapiens) that is anatomically related to the great apes but distinguished especially by notable development of the brain with a resultant capacity for articulate (see ARTICULATE entry 1 sense 1a) speech and abstract reasoning, and is the sole living representative of the hominid family\",\"Subject\":\"Three Onboarding Invitation: Onboarding New Hire.\",\"RecipientEmails\":[\"<EMAIL>\"],\"PhoneNumber\":\"\",\"PushToken\":\"\",\"CC\":[],\"UserId\":\"c964a01d-d0f3-4553-b53d-07e773d66525\",\"Application\":1,\"Attachments\":[],\"NotificationTypes\":[1,3],\"Priority\":0,\"IsWhatsappNo\":false,\"TemplateWithPlaceHolders\":null}"

18:22:41 [Information] :: notification-message-queue message received: {"MessageId":"","Body":"","TemplateWithParams":"a bipedal primate mammal (Homo sapiens) that is anatomically related to the great apes but distinguished especially by notable development of the brain with a resultant capacity for articulate (see ARTICULATE entry 1 sense 1a) speech and abstract reasoning, and is the sole living representative of the hominid family","Subject":"Three Onboarding Invitation: Onboarding New Hire.","RecipientEmails":["<EMAIL>"],"PhoneNumber":"","PushToken":"","CC":[],"UserId":"c964a01d-d0f3-4553-b53d-07e773d66525","Application":1,"Attachments":[],"NotificationTypes":[1,3],"Priority":0,"IsWhatsappNo":false,"TemplateWithPlaceHolders":null}

18:22:41 [Error] :: Error while starting consumer
RabbitMQ.Client.Exceptions.BrokerUnreachableException: None of the specified endpoints were reachable
 ---> System.IO.IOException: connection.start was never received, likely due to a network timeout
 ---> System.IO.EndOfStreamException: Reached the end of the stream. Possible authentication failure.
   at RabbitMQ.Client.Impl.InboundFrame.ReadFrom(Stream reader, Byte[] frameHeaderBuffer, ArrayPool`1 pool, UInt32 maxMessageSize)
   at RabbitMQ.Client.Framing.Impl.Connection.MainLoopIteration()
   at RabbitMQ.Client.Framing.Impl.Connection.MainLoop()
   --- End of inner exception stack trace ---
   at RabbitMQ.Client.Framing.Impl.Connection.StartAndTune()
   at RabbitMQ.Client.Framing.Impl.Connection.Open(Boolean insist)
   at RabbitMQ.Client.Framing.Impl.Connection..ctor(IConnectionFactory factory, Boolean insist, IFrameHandler frameHandler, String clientProvidedName)
   at RabbitMQ.Client.Framing.Impl.Connection..ctor(IConnectionFactory factory, Boolean insist, IFrameHandler frameHandler, ArrayPool`1 memoryPool, String clientProvidedName)
   at RabbitMQ.Client.Framing.Impl.AutorecoveringConnection.Init(IFrameHandler fh)
   at RabbitMQ.Client.Framing.Impl.AutorecoveringConnection.Init(IEndpointResolver endpoints)
   at RabbitMQ.Client.ConnectionFactory.CreateConnection(IEndpointResolver endpointResolver, String clientProvidedName)
   --- End of inner exception stack trace ---
   at RabbitMQ.Client.ConnectionFactory.CreateConnection(IEndpointResolver endpointResolver, String clientProvidedName)
   at RabbitMQ.Client.ConnectionFactory.CreateConnection(String clientProvidedName)
   at RabbitMQ.Client.ConnectionFactory.CreateConnection()
   at NotificationService.Infrastructure.Services.BaseConsumer.StartConsumer() in C:\Users\<USER>\source\repos\Notification-Service\src\NotificationService.Infrastructure\Services\BaseConsumer.cs:line 35

18:22:58 [Information] :: Notification Message "{\"MessageId\":\"\",\"Body\":\"\",\"TemplateWithParams\":\"a bipedal primate mammal (Homo sapiens) that is anatomically related to the great apes but distinguished especially by notable development of the brain with a resultant capacity for articulate (see ARTICULATE entry 1 sense 1a) speech and abstract reasoning, and is the sole living representative of the hominid family\",\"Subject\":\"Three Onboarding Invitation: Onboarding New Hire.\",\"RecipientEmails\":[\"<EMAIL>\"],\"PhoneNumber\":\"\",\"PushToken\":\"\",\"CC\":[],\"UserId\":\"c964a01d-d0f3-4553-b53d-07e773d66525\",\"Application\":1,\"Attachments\":[],\"NotificationTypes\":[1,3],\"Priority\":0,\"IsWhatsappNo\":false,\"TemplateWithPlaceHolders\":null}"

18:23:08 [Information] :: notification-message-queue message received: {"MessageId":"","Body":"","TemplateWithParams":"a bipedal primate mammal (Homo sapiens) that is anatomically related to the great apes but distinguished especially by notable development of the brain with a resultant capacity for articulate (see ARTICULATE entry 1 sense 1a) speech and abstract reasoning, and is the sole living representative of the hominid family","Subject":"Three Onboarding Invitation: Onboarding New Hire.","RecipientEmails":["<EMAIL>"],"PhoneNumber":"","PushToken":"","CC":[],"UserId":"c964a01d-d0f3-4553-b53d-07e773d66525","Application":1,"Attachments":[],"NotificationTypes":[1,3],"Priority":0,"IsWhatsappNo":false,"TemplateWithPlaceHolders":null}

18:23:24 [Information] :: Notification Message "{\"MessageId\":\"\",\"Body\":\"\",\"TemplateWithParams\":\"a bipedal primate mammal (Homo sapiens) that is anatomically related to the great apes but distinguished especially by notable development of the brain with a resultant capacity for articulate (see ARTICULATE entry 1 sense 1a) speech and abstract reasoning, and is the sole living representative of the hominid family\",\"Subject\":\"Three Onboarding Invitation: Onboarding New Hire.\",\"RecipientEmails\":[\"<EMAIL>\"],\"PhoneNumber\":\"\",\"PushToken\":\"\",\"CC\":[],\"UserId\":\"c964a01d-d0f3-4553-b53d-07e773d66525\",\"Application\":1,\"Attachments\":[],\"NotificationTypes\":[1,3],\"Priority\":0,\"IsWhatsappNo\":false,\"TemplateWithPlaceHolders\":null}"

18:27:32 [Information] :: Executed DbCommand ("47"ms) [Parameters=[""], CommandType='Text', CommandTimeout='30']"
""SELECT \"MigrationId\", \"ProductVersion\"
FROM \"__EFMigrationsHistory\"
ORDER BY \"MigrationId\";"

18:27:32 [Information] :: Firebase Admin SDK initialized successfully

18:27:32 [Information] :: RabbitMQ Broker Url: amqps://xruwodqv:<EMAIL>/xruwodqv

18:27:32 [Information] :: RabbitMQ Broker Url: amqps://xruwodqv:<EMAIL>/xruwodqv

18:27:32 [Information] :: User profile is available. Using '"C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys"' as key repository and Windows DPAPI to encrypt keys at rest.

18:27:33 [Information] :: Now listening on: "https://localhost:7044"

18:27:33 [Information] :: Now listening on: "http://localhost:5179"

18:27:33 [Information] :: Application started. Press Ctrl+C to shut down.

18:27:33 [Information] :: Hosting environment: "Development"

18:27:33 [Information] :: Content root path: "C:\Users\<USER>\source\repos\Notification-Service\src\NotificationService.API"

18:27:33 [Information] :: Request starting "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/index.html""" - null null

18:27:33 [Information] :: Request finished "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/index.html""" - 200 null "text/html;charset=utf-8" 361.4053ms

18:27:33 [Information] :: Request starting "HTTP/2" "GET" "https"://"localhost:7044""""/_framework/aspnetcore-browser-refresh.js""" - null null

18:27:33 [Information] :: Request starting "HTTP/2" "GET" "https"://"localhost:7044""""/_vs/browserLink""" - null null

18:27:33 [Information] :: Request finished "HTTP/2" "GET" "https"://"localhost:7044""""/_framework/aspnetcore-browser-refresh.js""" - 200 13782 "application/javascript; charset=utf-8" 20.4144ms

18:27:34 [Information] :: Request finished "HTTP/2" "GET" "https"://"localhost:7044""""/_vs/browserLink""" - 200 null "text/javascript; charset=UTF-8" 92.1548ms

18:27:35 [Information] :: Request starting "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/v1/swagger.json""" - null null

18:27:35 [Information] :: Request finished "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/v1/swagger.json""" - 200 null "application/json;charset=utf-8" 394.1677ms

18:27:40 [Information] :: Notification Message "{\"MessageId\":\"\",\"Body\":\"\",\"TemplateWithParams\":\"a bipedal primate mammal (Homo sapiens) that is anatomically related to the great apes but distinguished especially by notable development of the brain with a resultant capacity for articulate (see ARTICULATE entry 1 sense 1a) speech and abstract reasoning, and is the sole living representative of the hominid family\",\"Subject\":\"Three Onboarding Invitation: Onboarding New Hire.\",\"RecipientEmails\":[\"<EMAIL>\"],\"PhoneNumber\":\"\",\"PushToken\":\"\",\"CC\":[],\"UserId\":\"c964a01d-d0f3-4553-b53d-07e773d66525\",\"Application\":1,\"Attachments\":[],\"NotificationTypes\":[1,3],\"Priority\":0,\"IsWhatsappNo\":false,\"TemplateWithPlaceHolders\":null}"

18:27:40 [Information] :: notification-message-queue message received: {"MessageId":"","Body":"","TemplateWithParams":"a bipedal primate mammal (Homo sapiens) that is anatomically related to the great apes but distinguished especially by notable development of the brain with a resultant capacity for articulate (see ARTICULATE entry 1 sense 1a) speech and abstract reasoning, and is the sole living representative of the hominid family","Subject":"Three Onboarding Invitation: Onboarding New Hire.","RecipientEmails":["<EMAIL>"],"PhoneNumber":"","PushToken":"","CC":[],"UserId":"c964a01d-d0f3-4553-b53d-07e773d66525","Application":1,"Attachments":[],"NotificationTypes":[1,3],"Priority":0,"IsWhatsappNo":false,"TemplateWithPlaceHolders":null}

18:27:54 [Information] :: Notification Message "{\"MessageId\":\"\",\"Body\":\"\",\"TemplateWithParams\":\"a bipedal primate mammal (Homo sapiens) that is anatomically related to the great apes but distinguished especially by notable development of the brain with a resultant capacity for articulate (see ARTICULATE entry 1 sense 1a) speech and abstract reasoning, and is the sole living representative of the hominid family\",\"Subject\":\"Three Onboarding Invitation: Onboarding New Hire.\",\"RecipientEmails\":[\"<EMAIL>\"],\"PhoneNumber\":\"\",\"PushToken\":\"\",\"CC\":[],\"UserId\":\"c964a01d-d0f3-4553-b53d-07e773d66525\",\"Application\":1,\"Attachments\":[],\"NotificationTypes\":[1,3],\"Priority\":0,\"IsWhatsappNo\":false,\"TemplateWithPlaceHolders\":null}"

18:27:54 [Information] :: notification-message-queue message received: {"MessageId":"","Body":"","TemplateWithParams":"a bipedal primate mammal (Homo sapiens) that is anatomically related to the great apes but distinguished especially by notable development of the brain with a resultant capacity for articulate (see ARTICULATE entry 1 sense 1a) speech and abstract reasoning, and is the sole living representative of the hominid family","Subject":"Three Onboarding Invitation: Onboarding New Hire.","RecipientEmails":["<EMAIL>"],"PhoneNumber":"","PushToken":"","CC":[],"UserId":"c964a01d-d0f3-4553-b53d-07e773d66525","Application":1,"Attachments":[],"NotificationTypes":[1,3],"Priority":0,"IsWhatsappNo":false,"TemplateWithPlaceHolders":null}

18:28:05 [Information] :: Notification Message "{\"MessageId\":\"\",\"Body\":\"\",\"TemplateWithParams\":\"a bipedal primate mammal (Homo sapiens) that is anatomically related to the great apes but distinguished especially by notable development of the brain with a resultant capacity for articulate (see ARTICULATE entry 1 sense 1a) speech and abstract reasoning, and is the sole living representative of the hominid family\",\"Subject\":\"Three Onboarding Invitation: Onboarding New Hire.\",\"RecipientEmails\":[\"<EMAIL>\"],\"PhoneNumber\":\"\",\"PushToken\":\"\",\"CC\":[],\"UserId\":\"c964a01d-d0f3-4553-b53d-07e773d66525\",\"Application\":1,\"Attachments\":[],\"NotificationTypes\":[1,3],\"Priority\":0,\"IsWhatsappNo\":false,\"TemplateWithPlaceHolders\":null}"

18:28:26 [Information] :: notification-message-queue message received: {"MessageId":"","Body":"","TemplateWithParams":"a bipedal primate mammal (Homo sapiens) that is anatomically related to the great apes but distinguished especially by notable development of the brain with a resultant capacity for articulate (see ARTICULATE entry 1 sense 1a) speech and abstract reasoning, and is the sole living representative of the hominid family","Subject":"Three Onboarding Invitation: Onboarding New Hire.","RecipientEmails":["<EMAIL>"],"PhoneNumber":"","PushToken":"","CC":[],"UserId":"c964a01d-d0f3-4553-b53d-07e773d66525","Application":1,"Attachments":[],"NotificationTypes":[1,3],"Priority":0,"IsWhatsappNo":false,"TemplateWithPlaceHolders":null}

18:28:34 [Information] :: Notification Message "{\"MessageId\":\"\",\"Body\":\"\",\"TemplateWithParams\":\"a bipedal primate mammal (Homo sapiens) that is anatomically related to the great apes but distinguished especially by notable development of the brain with a resultant capacity for articulate (see ARTICULATE entry 1 sense 1a) speech and abstract reasoning, and is the sole living representative of the hominid family\",\"Subject\":\"Three Onboarding Invitation: Onboarding New Hire.\",\"RecipientEmails\":[\"<EMAIL>\"],\"PhoneNumber\":\"\",\"PushToken\":\"\",\"CC\":[],\"UserId\":\"c964a01d-d0f3-4553-b53d-07e773d66525\",\"Application\":1,\"Attachments\":[],\"NotificationTypes\":[1,3],\"Priority\":0,\"IsWhatsappNo\":false,\"TemplateWithPlaceHolders\":null}"

18:28:55 [Information] :: notification-message-queue message received: {"MessageId":"","Body":"","TemplateWithParams":"a bipedal primate mammal (Homo sapiens) that is anatomically related to the great apes but distinguished especially by notable development of the brain with a resultant capacity for articulate (see ARTICULATE entry 1 sense 1a) speech and abstract reasoning, and is the sole living representative of the hominid family","Subject":"Three Onboarding Invitation: Onboarding New Hire.","RecipientEmails":["<EMAIL>"],"PhoneNumber":"","PushToken":"","CC":[],"UserId":"c964a01d-d0f3-4553-b53d-07e773d66525","Application":1,"Attachments":[],"NotificationTypes":[1,3],"Priority":0,"IsWhatsappNo":false,"TemplateWithPlaceHolders":null}

18:29:05 [Information] :: Notification Message "{\"MessageId\":\"\",\"Body\":\"\",\"TemplateWithParams\":\"a bipedal primate mammal (Homo sapiens) that is anatomically related to the great apes but distinguished especially by notable development of the brain with a resultant capacity for articulate (see ARTICULATE entry 1 sense 1a) speech and abstract reasoning, and is the sole living representative of the hominid family\",\"Subject\":\"Three Onboarding Invitation: Onboarding New Hire.\",\"RecipientEmails\":[\"<EMAIL>\"],\"PhoneNumber\":\"\",\"PushToken\":\"\",\"CC\":[],\"UserId\":\"c964a01d-d0f3-4553-b53d-07e773d66525\",\"Application\":1,\"Attachments\":[],\"NotificationTypes\":[1,3],\"Priority\":0,\"IsWhatsappNo\":false,\"TemplateWithPlaceHolders\":null}"

18:29:06 [Information] :: notification-message-queue message received: {"MessageId":"","Body":"","TemplateWithParams":"a bipedal primate mammal (Homo sapiens) that is anatomically related to the great apes but distinguished especially by notable development of the brain with a resultant capacity for articulate (see ARTICULATE entry 1 sense 1a) speech and abstract reasoning, and is the sole living representative of the hominid family","Subject":"Three Onboarding Invitation: Onboarding New Hire.","RecipientEmails":["<EMAIL>"],"PhoneNumber":"","PushToken":"","CC":[],"UserId":"c964a01d-d0f3-4553-b53d-07e773d66525","Application":1,"Attachments":[],"NotificationTypes":[1,3],"Priority":0,"IsWhatsappNo":false,"TemplateWithPlaceHolders":null}

18:31:45 [Information] :: Executed DbCommand ("42"ms) [Parameters=[""], CommandType='Text', CommandTimeout='30']"
""SELECT \"MigrationId\", \"ProductVersion\"
FROM \"__EFMigrationsHistory\"
ORDER BY \"MigrationId\";"

18:31:46 [Information] :: Firebase Admin SDK initialized successfully

18:31:46 [Information] :: RabbitMQ Broker Url: amqps://xruwodqv:<EMAIL>/xruwodqv

18:31:46 [Information] :: RabbitMQ Broker Url: amqps://xruwodqv:<EMAIL>/xruwodqv

18:31:46 [Information] :: User profile is available. Using '"C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys"' as key repository and Windows DPAPI to encrypt keys at rest.

18:31:46 [Information] :: Now listening on: "https://localhost:7044"

18:31:46 [Information] :: Now listening on: "http://localhost:5179"

18:31:46 [Information] :: Application started. Press Ctrl+C to shut down.

18:31:46 [Information] :: Hosting environment: "Development"

18:31:46 [Information] :: Content root path: "C:\Users\<USER>\source\repos\Notification-Service\src\NotificationService.API"

18:31:47 [Information] :: Request starting "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/index.html""" - null null

18:31:48 [Information] :: Request finished "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/index.html""" - 200 null "text/html;charset=utf-8" 434.9492ms

18:31:48 [Information] :: Request starting "HTTP/2" "GET" "https"://"localhost:7044""""/_vs/browserLink""" - null null

18:31:48 [Information] :: Request starting "HTTP/2" "GET" "https"://"localhost:7044""""/_framework/aspnetcore-browser-refresh.js""" - null null

18:31:48 [Information] :: Request finished "HTTP/2" "GET" "https"://"localhost:7044""""/_framework/aspnetcore-browser-refresh.js""" - 200 13782 "application/javascript; charset=utf-8" 36.9477ms

18:31:48 [Information] :: Request finished "HTTP/2" "GET" "https"://"localhost:7044""""/_vs/browserLink""" - 200 null "text/javascript; charset=UTF-8" 91.4155ms

18:31:49 [Information] :: Request starting "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/v1/swagger.json""" - null null

18:31:50 [Information] :: Request finished "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/v1/swagger.json""" - 200 null "application/json;charset=utf-8" 402.8047ms

18:31:54 [Information] :: Notification Message "{\"MessageId\":\"\",\"Body\":\"\",\"TemplateWithParams\":\"a bipedal primate mammal (Homo sapiens) that is anatomically related to the great apes but distinguished especially by notable development of the brain with a resultant capacity for articulate (see ARTICULATE entry 1 sense 1a) speech and abstract reasoning, and is the sole living representative of the hominid family\",\"Subject\":\"Three Onboarding Invitation: Onboarding New Hire.\",\"RecipientEmails\":[\"<EMAIL>\"],\"PhoneNumber\":\"\",\"PushToken\":\"\",\"CC\":[],\"UserId\":\"c964a01d-d0f3-4553-b53d-07e773d66525\",\"Application\":1,\"Attachments\":[],\"NotificationTypes\":[1,3],\"Priority\":0,\"IsWhatsappNo\":false,\"TemplateWithPlaceHolders\":null}"

18:31:54 [Information] :: Attempting to retrieve push token for UserId: "c964a01d-d0f3-4553-b53d-07e773d66525", Application: Joble

18:34:40 [Information] :: Executed DbCommand ("39"ms) [Parameters=[""], CommandType='Text', CommandTimeout='30']"
""SELECT \"MigrationId\", \"ProductVersion\"
FROM \"__EFMigrationsHistory\"
ORDER BY \"MigrationId\";"

18:34:41 [Information] :: Firebase Admin SDK initialized successfully

18:34:41 [Information] :: RabbitMQ Broker Url: amqps://xruwodqv:<EMAIL>/xruwodqv

18:34:41 [Information] :: RabbitMQ Broker Url: amqps://xruwodqv:<EMAIL>/xruwodqv

18:34:41 [Information] :: User profile is available. Using '"C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys"' as key repository and Windows DPAPI to encrypt keys at rest.

18:34:41 [Information] :: Now listening on: "https://localhost:7044"

18:34:41 [Information] :: Now listening on: "http://localhost:5179"

18:34:41 [Information] :: Application started. Press Ctrl+C to shut down.

18:34:41 [Information] :: Hosting environment: "Development"

18:34:41 [Information] :: Content root path: "C:\Users\<USER>\source\repos\Notification-Service\src\NotificationService.API"

18:34:43 [Information] :: Request starting "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/index.html""" - null null

18:34:43 [Information] :: Request finished "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/index.html""" - 200 null "text/html;charset=utf-8" 460.3718ms

18:34:43 [Information] :: Request starting "HTTP/2" "GET" "https"://"localhost:7044""""/_framework/aspnetcore-browser-refresh.js""" - null null

18:34:43 [Information] :: Request starting "HTTP/2" "GET" "https"://"localhost:7044""""/_vs/browserLink""" - null null

18:34:43 [Information] :: Request finished "HTTP/2" "GET" "https"://"localhost:7044""""/_framework/aspnetcore-browser-refresh.js""" - 200 13782 "application/javascript; charset=utf-8" 29.5851ms

18:34:43 [Information] :: Request finished "HTTP/2" "GET" "https"://"localhost:7044""""/_vs/browserLink""" - 200 null "text/javascript; charset=UTF-8" 87.5378ms

18:34:44 [Information] :: Request starting "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/v1/swagger.json""" - null null

18:34:45 [Information] :: Request finished "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/v1/swagger.json""" - 200 null "application/json;charset=utf-8" 283.0501ms

18:34:54 [Information] :: Notification Message "{\"MessageId\":\"\",\"Body\":\"\",\"TemplateWithParams\":\"a bipedal primate mammal (Homo sapiens) that is anatomically related to the great apes but distinguished especially by notable development of the brain with a resultant capacity for articulate (see ARTICULATE entry 1 sense 1a) speech and abstract reasoning, and is the sole living representative of the hominid family\",\"Subject\":\"Three Onboarding Invitation: Onboarding New Hire.\",\"RecipientEmails\":[\"<EMAIL>\"],\"PhoneNumber\":\"\",\"PushToken\":\"\",\"CC\":[],\"UserId\":\"c964a01d-d0f3-4553-b53d-07e773d66525\",\"Application\":1,\"Attachments\":[],\"NotificationTypes\":[1,3],\"Priority\":0,\"IsWhatsappNo\":false,\"TemplateWithPlaceHolders\":null}"

18:34:55 [Information] :: Executed DbCommand ("71"ms) [Parameters=["@__model_UserId_0='?', @__ToString_1='?'"], CommandType='Text', CommandTimeout='30']"
""SELECT p.\"Id\", p.\"AppName\", p.\"CreatedBy\", p.\"CreatedOn\", p.\"Token\", p.\"UpdatedBy\", p.\"UpdatedOn\", p.\"UserId\"
FROM \"PushTokens\" AS p
WHERE p.\"UserId\" = @__model_UserId_0 AND p.\"AppName\" = @__ToString_1"

21:04:15 [Information] :: Executed DbCommand ("26"ms) [Parameters=[""], CommandType='Text', CommandTimeout='30']"
""SELECT \"MigrationId\", \"ProductVersion\"
FROM \"__EFMigrationsHistory\"
ORDER BY \"MigrationId\";"

21:04:15 [Information] :: Firebase Admin SDK initialized successfully

21:04:15 [Information] :: RabbitMQ Broker Url: amqps://xruwodqv:<EMAIL>/xruwodqv

21:04:15 [Information] :: RabbitMQ Broker Url: amqps://xruwodqv:<EMAIL>/xruwodqv

21:04:15 [Information] :: User profile is available. Using '"C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys"' as key repository and Windows DPAPI to encrypt keys at rest.

21:04:16 [Information] :: Now listening on: "https://localhost:7044"

21:04:16 [Information] :: Now listening on: "http://localhost:5179"

21:04:16 [Information] :: Application started. Press Ctrl+C to shut down.

21:04:16 [Information] :: Hosting environment: "Development"

21:04:16 [Information] :: Content root path: "C:\Users\<USER>\source\repos\Notification-Service\src\NotificationService.API"

21:04:17 [Information] :: Request starting "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/index.html""" - null null

21:04:18 [Information] :: Request finished "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/index.html""" - 200 null "text/html;charset=utf-8" 739.1208ms

21:04:18 [Information] :: Request starting "HTTP/2" "GET" "https"://"localhost:7044""""/_framework/aspnetcore-browser-refresh.js""" - null null

21:04:18 [Information] :: Request starting "HTTP/2" "GET" "https"://"localhost:7044""""/_vs/browserLink""" - null null

21:04:18 [Information] :: Request finished "HTTP/2" "GET" "https"://"localhost:7044""""/_framework/aspnetcore-browser-refresh.js""" - 200 13782 "application/javascript; charset=utf-8" 36.242ms

21:04:18 [Information] :: Request finished "HTTP/2" "GET" "https"://"localhost:7044""""/_vs/browserLink""" - 200 null "text/javascript; charset=UTF-8" 166.1482ms

21:04:19 [Information] :: Request starting "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/v1/swagger.json""" - null null

21:04:19 [Information] :: Request finished "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/v1/swagger.json""" - 200 null "application/json;charset=utf-8" 192.6907ms

21:04:29 [Information] :: Notification Message "{\"MessageId\":\"\",\"Body\":\"\",\"TemplateWithParams\":\"a bipedal primate mammal (Homo sapiens) that is anatomically related to the great apes but distinguished especially by notable development of the brain with a resultant capacity for articulate (see ARTICULATE entry 1 sense 1a) speech and abstract reasoning, and is the sole living representative of the hominid family\",\"Subject\":\"Three Onboarding Invitation: Onboarding New Hire.\",\"RecipientEmails\":[\"<EMAIL>\"],\"PhoneNumber\":\"\",\"PushToken\":\"\",\"CC\":[],\"UserId\":\"c964a01d-d0f3-4553-b53d-07e773d66525\",\"Application\":1,\"Attachments\":[],\"NotificationTypes\":[1,3],\"Priority\":0,\"IsWhatsappNo\":false,\"TemplateWithPlaceHolders\":null}"

21:04:30 [Information] :: Executed DbCommand ("21"ms) [Parameters=["@__model_UserId_0='?', @__ToString_1='?'"], CommandType='Text', CommandTimeout='30']"
""SELECT p.\"Id\", p.\"AppName\", p.\"CreatedBy\", p.\"CreatedOn\", p.\"Token\", p.\"UpdatedBy\", p.\"UpdatedOn\", p.\"UserId\"
FROM \"PushTokens\" AS p
WHERE p.\"UserId\" = @__model_UserId_0 AND p.\"AppName\" = @__ToString_1"

21:04:36 [Information] :: notification-message-queue message received: {"MessageId":"","Body":"","TemplateWithParams":"a bipedal primate mammal (Homo sapiens) that is anatomically related to the great apes but distinguished especially by notable development of the brain with a resultant capacity for articulate (see ARTICULATE entry 1 sense 1a) speech and abstract reasoning, and is the sole living representative of the hominid family","Subject":"Three Onboarding Invitation: Onboarding New Hire.","RecipientEmails":["<EMAIL>"],"PhoneNumber":"","PushToken":"","CC":[],"UserId":"c964a01d-d0f3-4553-b53d-07e773d66525","Application":1,"Attachments":[],"NotificationTypes":[1,3],"Priority":0,"IsWhatsappNo":false,"TemplateWithPlaceHolders":null}

21:04:39 [Information] :: MailGun response {"id":"<<EMAIL>>","message":"Queued. Thank you."}


21:04:43 [Error] :: Firebase messaging error: InvalidArgument - "The registration token is not a valid FCM registration token"
FirebaseAdmin.Messaging.FirebaseMessagingException: The registration token is not a valid FCM registration token
   at FirebaseAdmin.Util.ErrorHandlingHttpClient`1.SendAndReadAsync(HttpRequestMessage request, CancellationToken cancellationToken)
   at FirebaseAdmin.Util.ErrorHandlingHttpClient`1.SendAndDeserializeAsync[TResult](HttpRequestMessage request, CancellationToken cancellationToken)
   at FirebaseAdmin.Messaging.FirebaseMessagingClient.SendAsync(Message message, Boolean dryRun, CancellationToken cancellationToken)
   at FirebaseAdmin.Messaging.FirebaseMessaging.SendAsync(Message message, Boolean dryRun, CancellationToken cancellationToken)
   at FirebaseAdmin.Messaging.FirebaseMessaging.SendAsync(Message message, Boolean dryRun)
   at FirebaseAdmin.Messaging.FirebaseMessaging.SendAsync(Message message)
   at NotificationService.Infrastructure.Services.FirebasePushNotificationService.SendMessage(NotificationMessageModel model) in C:\Users\<USER>\source\repos\Notification-Service\src\NotificationService.Infrastructure\Services\FirebasePushMessageService.cs:line 89

21:04:43 [Information] :: Executed DbCommand ("33"ms) [Parameters=["@p0='?', @p1='?', @p2='?' (DbType = DateTime), @p3='?', @p4='?', @p5='?', @p6='?', @p7='?', @p8='?', @p9='?', @p10='?', @p11='?', @p12='?', @p13='?', @p14='?', @p15='?', @p16='?', @p17='?' (DbType = DateTime), @p18='?', @p19='?', @p20='?', @p21='?' (DbType = DateTime), @p22='?', @p23='?', @p24='?', @p25='?', @p26='?', @p27='?', @p28='?', @p29='?', @p30='?', @p31='?', @p32='?', @p33='?', @p34='?', @p35='?', @p36='?' (DbType = DateTime), @p37='?'"], CommandType='Text', CommandTimeout='30']"
""INSERT INTO \"Notifications\" (\"Body\", \"CreatedBy\", \"CreatedOn\", \"Email\", \"MessageId\", \"MessageSid\", \"NotificationType\", \"PhoneNumber\", \"Priority\", \"PushToken\", \"ResponseId\", \"Status\", \"StatusCode\", \"StatusMessage\", \"Subject\", \"Template\", \"UpdatedBy\", \"UpdatedOn\", \"UserId\")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16, @p17, @p18)
RETURNING \"Id\";
INSERT INTO \"Notifications\" (\"Body\", \"CreatedBy\", \"CreatedOn\", \"Email\", \"MessageId\", \"MessageSid\", \"NotificationType\", \"PhoneNumber\", \"Priority\", \"PushToken\", \"ResponseId\", \"Status\", \"StatusCode\", \"StatusMessage\", \"Subject\", \"Template\", \"UpdatedBy\", \"UpdatedOn\", \"UserId\")
VALUES (@p19, @p20, @p21, @p22, @p23, @p24, @p25, @p26, @p27, @p28, @p29, @p30, @p31, @p32, @p33, @p34, @p35, @p36, @p37)
RETURNING \"Id\";"

21:04:54 [Information] :: Notification Message "{\"MessageId\":\"\",\"Body\":\"\",\"TemplateWithParams\":\"a bipedal primate mammal (Homo sapiens) that is anatomically related to the great apes but distinguished especially by notable development of the brain with a resultant capacity for articulate (see ARTICULATE entry 1 sense 1a) speech and abstract reasoning, and is the sole living representative of the hominid family\",\"Subject\":\"Three Onboarding Invitation: Onboarding New Hire.\",\"RecipientEmails\":[\"<EMAIL>\"],\"PhoneNumber\":\"\",\"PushToken\":\"\",\"CC\":[],\"UserId\":\"c964a01d-d0f3-4553-b53d-07e773d66525\",\"Application\":1,\"Attachments\":[],\"NotificationTypes\":[1,3],\"Priority\":0,\"IsWhatsappNo\":false,\"TemplateWithPlaceHolders\":null}"

21:04:54 [Information] :: Executed DbCommand ("1"ms) [Parameters=["@__model_UserId_0='?', @__ToString_1='?'"], CommandType='Text', CommandTimeout='30']"
""SELECT p.\"Id\", p.\"AppName\", p.\"CreatedBy\", p.\"CreatedOn\", p.\"Token\", p.\"UpdatedBy\", p.\"UpdatedOn\", p.\"UserId\"
FROM \"PushTokens\" AS p
WHERE p.\"UserId\" = @__model_UserId_0 AND p.\"AppName\" = @__ToString_1"

21:04:55 [Information] :: notification-message-queue message received: {"MessageId":"","Body":"","TemplateWithParams":"a bipedal primate mammal (Homo sapiens) that is anatomically related to the great apes but distinguished especially by notable development of the brain with a resultant capacity for articulate (see ARTICULATE entry 1 sense 1a) speech and abstract reasoning, and is the sole living representative of the hominid family","Subject":"Three Onboarding Invitation: Onboarding New Hire.","RecipientEmails":["<EMAIL>"],"PhoneNumber":"","PushToken":"","CC":[],"UserId":"c964a01d-d0f3-4553-b53d-07e773d66525","Application":1,"Attachments":[],"NotificationTypes":[1,3],"Priority":0,"IsWhatsappNo":false,"TemplateWithPlaceHolders":null}

21:04:58 [Information] :: MailGun response {"id":"<<EMAIL>>","message":"Queued. Thank you."}


21:04:59 [Error] :: Firebase messaging error: InvalidArgument - "The registration token is not a valid FCM registration token"
FirebaseAdmin.Messaging.FirebaseMessagingException: The registration token is not a valid FCM registration token
   at FirebaseAdmin.Util.ErrorHandlingHttpClient`1.SendAndReadAsync(HttpRequestMessage request, CancellationToken cancellationToken)
   at FirebaseAdmin.Util.ErrorHandlingHttpClient`1.SendAndDeserializeAsync[TResult](HttpRequestMessage request, CancellationToken cancellationToken)
   at FirebaseAdmin.Messaging.FirebaseMessagingClient.SendAsync(Message message, Boolean dryRun, CancellationToken cancellationToken)
   at FirebaseAdmin.Messaging.FirebaseMessaging.SendAsync(Message message, Boolean dryRun, CancellationToken cancellationToken)
   at FirebaseAdmin.Messaging.FirebaseMessaging.SendAsync(Message message, Boolean dryRun)
   at FirebaseAdmin.Messaging.FirebaseMessaging.SendAsync(Message message)
   at NotificationService.Infrastructure.Services.FirebasePushNotificationService.SendMessage(NotificationMessageModel model) in C:\Users\<USER>\source\repos\Notification-Service\src\NotificationService.Infrastructure\Services\FirebasePushMessageService.cs:line 89

21:04:59 [Information] :: Executed DbCommand ("1"ms) [Parameters=["@p0='?', @p1='?', @p2='?' (DbType = DateTime), @p3='?', @p4='?', @p5='?', @p6='?', @p7='?', @p8='?', @p9='?', @p10='?', @p11='?', @p12='?', @p13='?', @p14='?', @p15='?', @p16='?', @p17='?' (DbType = DateTime), @p18='?', @p19='?', @p20='?', @p21='?' (DbType = DateTime), @p22='?', @p23='?', @p24='?', @p25='?', @p26='?', @p27='?', @p28='?', @p29='?', @p30='?', @p31='?', @p32='?', @p33='?', @p34='?', @p35='?', @p36='?' (DbType = DateTime), @p37='?'"], CommandType='Text', CommandTimeout='30']"
""INSERT INTO \"Notifications\" (\"Body\", \"CreatedBy\", \"CreatedOn\", \"Email\", \"MessageId\", \"MessageSid\", \"NotificationType\", \"PhoneNumber\", \"Priority\", \"PushToken\", \"ResponseId\", \"Status\", \"StatusCode\", \"StatusMessage\", \"Subject\", \"Template\", \"UpdatedBy\", \"UpdatedOn\", \"UserId\")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16, @p17, @p18)
RETURNING \"Id\";
INSERT INTO \"Notifications\" (\"Body\", \"CreatedBy\", \"CreatedOn\", \"Email\", \"MessageId\", \"MessageSid\", \"NotificationType\", \"PhoneNumber\", \"Priority\", \"PushToken\", \"ResponseId\", \"Status\", \"StatusCode\", \"StatusMessage\", \"Subject\", \"Template\", \"UpdatedBy\", \"UpdatedOn\", \"UserId\")
VALUES (@p19, @p20, @p21, @p22, @p23, @p24, @p25, @p26, @p27, @p28, @p29, @p30, @p31, @p32, @p33, @p34, @p35, @p36, @p37)
RETURNING \"Id\";"

21:05:23 [Information] :: Notification Message "{\"MessageId\":\"\",\"Body\":\"\",\"TemplateWithParams\":\"a bipedal primate mammal (Homo sapiens) that is anatomically related to the great apes but distinguished especially by notable development of the brain with a resultant capacity for articulate (see ARTICULATE entry 1 sense 1a) speech and abstract reasoning, and is the sole living representative of the hominid family\",\"Subject\":\"Three Onboarding Invitation: Onboarding New Hire.\",\"RecipientEmails\":[\"<EMAIL>\"],\"PhoneNumber\":\"\",\"PushToken\":\"\",\"CC\":[],\"UserId\":\"c964a01d-d0f3-4553-b53d-07e773d66525\",\"Application\":1,\"Attachments\":[],\"NotificationTypes\":[1,3],\"Priority\":0,\"IsWhatsappNo\":false,\"TemplateWithPlaceHolders\":null}"

21:05:23 [Information] :: Executed DbCommand ("1"ms) [Parameters=["@__model_UserId_0='?', @__ToString_1='?'"], CommandType='Text', CommandTimeout='30']"
""SELECT p.\"Id\", p.\"AppName\", p.\"CreatedBy\", p.\"CreatedOn\", p.\"Token\", p.\"UpdatedBy\", p.\"UpdatedOn\", p.\"UserId\"
FROM \"PushTokens\" AS p
WHERE p.\"UserId\" = @__model_UserId_0 AND p.\"AppName\" = @__ToString_1"

21:05:23 [Information] :: notification-message-queue message received: {"MessageId":"","Body":"","TemplateWithParams":"a bipedal primate mammal (Homo sapiens) that is anatomically related to the great apes but distinguished especially by notable development of the brain with a resultant capacity for articulate (see ARTICULATE entry 1 sense 1a) speech and abstract reasoning, and is the sole living representative of the hominid family","Subject":"Three Onboarding Invitation: Onboarding New Hire.","RecipientEmails":["<EMAIL>"],"PhoneNumber":"","PushToken":"","CC":[],"UserId":"c964a01d-d0f3-4553-b53d-07e773d66525","Application":1,"Attachments":[],"NotificationTypes":[1,3],"Priority":0,"IsWhatsappNo":false,"TemplateWithPlaceHolders":null}

21:05:24 [Information] :: Request starting "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/index.html""" - null null

21:05:24 [Information] :: Request finished "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/index.html""" - 200 null "text/html;charset=utf-8" 24.155ms

21:05:24 [Information] :: Request starting "HTTP/2" "GET" "https"://"localhost:7044""""/_vs/browserLink""" - null null

21:05:24 [Information] :: Request starting "HTTP/2" "GET" "https"://"localhost:7044""""/_framework/aspnetcore-browser-refresh.js""" - null null

21:05:24 [Information] :: Request finished "HTTP/2" "GET" "https"://"localhost:7044""""/_framework/aspnetcore-browser-refresh.js""" - 200 13782 "application/javascript; charset=utf-8" 17.6716ms

21:05:24 [Information] :: Request finished "HTTP/2" "GET" "https"://"localhost:7044""""/_vs/browserLink""" - 200 null "text/javascript; charset=UTF-8" 40.2657ms

21:05:24 [Information] :: Request starting "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/v1/swagger.json""" - null null

21:05:24 [Information] :: Request finished "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/v1/swagger.json""" - 200 null "application/json;charset=utf-8" 20.5254ms

21:05:26 [Information] :: MailGun response {"id":"<<EMAIL>>","message":"Queued. Thank you."}


21:05:27 [Error] :: Firebase messaging error: InvalidArgument - "The registration token is not a valid FCM registration token"
FirebaseAdmin.Messaging.FirebaseMessagingException: The registration token is not a valid FCM registration token
   at FirebaseAdmin.Util.ErrorHandlingHttpClient`1.SendAndReadAsync(HttpRequestMessage request, CancellationToken cancellationToken)
   at FirebaseAdmin.Util.ErrorHandlingHttpClient`1.SendAndDeserializeAsync[TResult](HttpRequestMessage request, CancellationToken cancellationToken)
   at FirebaseAdmin.Messaging.FirebaseMessagingClient.SendAsync(Message message, Boolean dryRun, CancellationToken cancellationToken)
   at FirebaseAdmin.Messaging.FirebaseMessaging.SendAsync(Message message, Boolean dryRun, CancellationToken cancellationToken)
   at FirebaseAdmin.Messaging.FirebaseMessaging.SendAsync(Message message, Boolean dryRun)
   at FirebaseAdmin.Messaging.FirebaseMessaging.SendAsync(Message message)
   at NotificationService.Infrastructure.Services.FirebasePushNotificationService.SendMessage(NotificationMessageModel model)

21:05:27 [Information] :: Executed DbCommand ("1"ms) [Parameters=["@p0='?', @p1='?', @p2='?' (DbType = DateTime), @p3='?', @p4='?', @p5='?', @p6='?', @p7='?', @p8='?', @p9='?', @p10='?', @p11='?', @p12='?', @p13='?', @p14='?', @p15='?', @p16='?', @p17='?' (DbType = DateTime), @p18='?', @p19='?', @p20='?', @p21='?' (DbType = DateTime), @p22='?', @p23='?', @p24='?', @p25='?', @p26='?', @p27='?', @p28='?', @p29='?', @p30='?', @p31='?', @p32='?', @p33='?', @p34='?', @p35='?', @p36='?' (DbType = DateTime), @p37='?'"], CommandType='Text', CommandTimeout='30']"
""INSERT INTO \"Notifications\" (\"Body\", \"CreatedBy\", \"CreatedOn\", \"Email\", \"MessageId\", \"MessageSid\", \"NotificationType\", \"PhoneNumber\", \"Priority\", \"PushToken\", \"ResponseId\", \"Status\", \"StatusCode\", \"StatusMessage\", \"Subject\", \"Template\", \"UpdatedBy\", \"UpdatedOn\", \"UserId\")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16, @p17, @p18)
RETURNING \"Id\";
INSERT INTO \"Notifications\" (\"Body\", \"CreatedBy\", \"CreatedOn\", \"Email\", \"MessageId\", \"MessageSid\", \"NotificationType\", \"PhoneNumber\", \"Priority\", \"PushToken\", \"ResponseId\", \"Status\", \"StatusCode\", \"StatusMessage\", \"Subject\", \"Template\", \"UpdatedBy\", \"UpdatedOn\", \"UserId\")
VALUES (@p19, @p20, @p21, @p22, @p23, @p24, @p25, @p26, @p27, @p28, @p29, @p30, @p31, @p32, @p33, @p34, @p35, @p36, @p37)
RETURNING \"Id\";"

21:08:40 [Information] :: Executed DbCommand ("21"ms) [Parameters=[""], CommandType='Text', CommandTimeout='30']"
""SELECT \"MigrationId\", \"ProductVersion\"
FROM \"__EFMigrationsHistory\"
ORDER BY \"MigrationId\";"

21:08:41 [Information] :: Firebase Admin SDK initialized successfully

21:08:41 [Information] :: RabbitMQ Broker Url: amqps://xruwodqv:<EMAIL>/xruwodqv

21:08:41 [Information] :: RabbitMQ Broker Url: amqps://xruwodqv:<EMAIL>/xruwodqv

21:08:41 [Information] :: User profile is available. Using '"C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys"' as key repository and Windows DPAPI to encrypt keys at rest.

21:08:41 [Information] :: Now listening on: "https://localhost:7044"

21:08:41 [Information] :: Now listening on: "http://localhost:5179"

21:08:41 [Information] :: Application started. Press Ctrl+C to shut down.

21:08:41 [Information] :: Hosting environment: "Development"

21:08:41 [Information] :: Content root path: "C:\Users\<USER>\source\repos\Notification-Service\src\NotificationService.API"

21:08:43 [Information] :: Request starting "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/index.html""" - null null

21:08:44 [Information] :: Request finished "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/index.html""" - 200 null "text/html;charset=utf-8" 728.6484ms

21:08:44 [Information] :: Request starting "HTTP/2" "GET" "https"://"localhost:7044""""/_framework/aspnetcore-browser-refresh.js""" - null null

21:08:44 [Information] :: Request starting "HTTP/2" "GET" "https"://"localhost:7044""""/_vs/browserLink""" - null null

21:08:44 [Information] :: Request finished "HTTP/2" "GET" "https"://"localhost:7044""""/_framework/aspnetcore-browser-refresh.js""" - 200 13782 "application/javascript; charset=utf-8" 23.327ms

21:08:44 [Information] :: Request finished "HTTP/2" "GET" "https"://"localhost:7044""""/_vs/browserLink""" - 200 null "text/javascript; charset=UTF-8" 134.8117ms

21:08:45 [Information] :: Request starting "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/v1/swagger.json""" - null null

21:08:45 [Information] :: Request finished "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/v1/swagger.json""" - 200 null "application/json;charset=utf-8" 385.1873ms

21:08:48 [Information] :: Notification Message "{\"MessageId\":\"\",\"Body\":\"\",\"TemplateWithParams\":\"a bipedal primate mammal (Homo sapiens) that is anatomically related to the great apes but distinguished especially by notable development of the brain with a resultant capacity for articulate (see ARTICULATE entry 1 sense 1a) speech and abstract reasoning, and is the sole living representative of the hominid family\",\"Subject\":\"Three Onboarding Invitation: Onboarding New Hire.\",\"RecipientEmails\":[\"<EMAIL>\"],\"PhoneNumber\":\"\",\"PushToken\":\"\",\"CC\":[],\"UserId\":\"c964a01d-d0f3-4553-b53d-07e773d66525\",\"Application\":1,\"Attachments\":[],\"NotificationTypes\":[1,3],\"Priority\":0,\"IsWhatsappNo\":false,\"TemplateWithPlaceHolders\":null}"

21:08:49 [Information] :: Executed DbCommand ("20"ms) [Parameters=["@__model_UserId_0='?', @__ToString_1='?'"], CommandType='Text', CommandTimeout='30']"
""SELECT p.\"Id\", p.\"AppName\", p.\"CreatedBy\", p.\"CreatedOn\", p.\"Token\", p.\"UpdatedBy\", p.\"UpdatedOn\", p.\"UserId\"
FROM \"PushTokens\" AS p
WHERE p.\"UserId\" = @__model_UserId_0 AND p.\"AppName\" = @__ToString_1"

21:08:49 [Information] :: notification-message-queue message received: {"MessageId":"","Body":"","TemplateWithParams":"a bipedal primate mammal (Homo sapiens) that is anatomically related to the great apes but distinguished especially by notable development of the brain with a resultant capacity for articulate (see ARTICULATE entry 1 sense 1a) speech and abstract reasoning, and is the sole living representative of the hominid family","Subject":"Three Onboarding Invitation: Onboarding New Hire.","RecipientEmails":["<EMAIL>"],"PhoneNumber":"","PushToken":"","CC":[],"UserId":"c964a01d-d0f3-4553-b53d-07e773d66525","Application":1,"Attachments":[],"NotificationTypes":[1,3],"Priority":0,"IsWhatsappNo":false,"TemplateWithPlaceHolders":null}

21:08:57 [Information] :: MailGun response {"id":"<<EMAIL>>","message":"Queued. Thank you."}


21:09:03 [Error] :: Firebase messaging error: InvalidArgument - "The registration token is not a valid FCM registration token"
FirebaseAdmin.Messaging.FirebaseMessagingException: The registration token is not a valid FCM registration token
   at FirebaseAdmin.Util.ErrorHandlingHttpClient`1.SendAndReadAsync(HttpRequestMessage request, CancellationToken cancellationToken)
   at FirebaseAdmin.Util.ErrorHandlingHttpClient`1.SendAndDeserializeAsync[TResult](HttpRequestMessage request, CancellationToken cancellationToken)
   at FirebaseAdmin.Messaging.FirebaseMessagingClient.SendAsync(Message message, Boolean dryRun, CancellationToken cancellationToken)
   at FirebaseAdmin.Messaging.FirebaseMessaging.SendAsync(Message message, Boolean dryRun, CancellationToken cancellationToken)
   at FirebaseAdmin.Messaging.FirebaseMessaging.SendAsync(Message message, Boolean dryRun)
   at FirebaseAdmin.Messaging.FirebaseMessaging.SendAsync(Message message)
   at NotificationService.Infrastructure.Services.FirebasePushNotificationService.SendMessage(NotificationMessageModel model) in C:\Users\<USER>\source\repos\Notification-Service\src\NotificationService.Infrastructure\Services\FirebasePushMessageService.cs:line 89

21:09:03 [Information] :: Executed DbCommand ("27"ms) [Parameters=["@p0='?', @p1='?', @p2='?' (DbType = DateTime), @p3='?', @p4='?', @p5='?', @p6='?', @p7='?', @p8='?', @p9='?', @p10='?', @p11='?', @p12='?', @p13='?', @p14='?', @p15='?', @p16='?', @p17='?' (DbType = DateTime), @p18='?', @p19='?', @p20='?', @p21='?' (DbType = DateTime), @p22='?', @p23='?', @p24='?', @p25='?', @p26='?', @p27='?', @p28='?', @p29='?', @p30='?', @p31='?', @p32='?', @p33='?', @p34='?', @p35='?', @p36='?' (DbType = DateTime), @p37='?'"], CommandType='Text', CommandTimeout='30']"
""INSERT INTO \"Notifications\" (\"Body\", \"CreatedBy\", \"CreatedOn\", \"Email\", \"MessageId\", \"MessageSid\", \"NotificationType\", \"PhoneNumber\", \"Priority\", \"PushToken\", \"ResponseId\", \"Status\", \"StatusCode\", \"StatusMessage\", \"Subject\", \"Template\", \"UpdatedBy\", \"UpdatedOn\", \"UserId\")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16, @p17, @p18)
RETURNING \"Id\";
INSERT INTO \"Notifications\" (\"Body\", \"CreatedBy\", \"CreatedOn\", \"Email\", \"MessageId\", \"MessageSid\", \"NotificationType\", \"PhoneNumber\", \"Priority\", \"PushToken\", \"ResponseId\", \"Status\", \"StatusCode\", \"StatusMessage\", \"Subject\", \"Template\", \"UpdatedBy\", \"UpdatedOn\", \"UserId\")
VALUES (@p19, @p20, @p21, @p22, @p23, @p24, @p25, @p26, @p27, @p28, @p29, @p30, @p31, @p32, @p33, @p34, @p35, @p36, @p37)
RETURNING \"Id\";"

21:15:19 [Information] :: Request starting "HTTP/1.1" "POST" "https"://"localhost:7044""""/api/Notification/queue""" - "application/json" 799

21:15:20 [Information] :: Executing endpoint '"NotificationService.API.Controllers.NotificationController.Queue (NotificationService.API)"'

21:15:20 [Information] :: Route matched with "{action = \"Queue\", controller = \"Notification\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Queue(NotificationService.Core.Models.NotificationMessageModel)" on controller "NotificationService.API.Controllers.NotificationController" ("NotificationService.API").

21:15:30 [Information] :: Successfully published message to RabbitMQ queue: "notification-message-queue"

21:15:32 [Information] :: Executing "OkObjectResult", writing value of type '"System.String"'.

21:15:32 [Information] :: Executed action "NotificationService.API.Controllers.NotificationController.Queue (NotificationService.API)" in 11813.742ms

21:15:32 [Information] :: Executed endpoint '"NotificationService.API.Controllers.NotificationController.Queue (NotificationService.API)"'

21:15:32 [Information] :: Request finished "HTTP/1.1" "POST" "https"://"localhost:7044""""/api/Notification/queue""" - 200 null "text/plain; charset=utf-8" 12728.9323ms

21:15:34 [Information] :: Notification Message "{\"MessageId\":\"\",\"Body\":\"\",\"TemplateWithParams\":\"a bipedal primate mammal (Homo sapiens) that is anatomically related to the great apes but distinguished especially by notable development of the brain with a resultant capacity for articulate (see ARTICULATE entry 1 sense 1a) speech and abstract reasoning, and is the sole living representative of the hominid family\",\"Subject\":\"Three Onboarding Invitation: Onboarding New Hire.\",\"RecipientEmails\":[\"<EMAIL>\"],\"PhoneNumber\":\"\",\"PushToken\":\"\",\"CC\":[],\"UserId\":\"c964a01d-d0f3-4553-b53d-07e773d66525\",\"Application\":1,\"Attachments\":[],\"NotificationTypes\":[1,3],\"Priority\":0,\"IsWhatsappNo\":false,\"TemplateWithPlaceHolders\":null}"

21:15:34 [Information] :: Executed DbCommand ("4"ms) [Parameters=["@__model_UserId_0='?', @__ToString_1='?'"], CommandType='Text', CommandTimeout='30']"
""SELECT p.\"Id\", p.\"AppName\", p.\"CreatedBy\", p.\"CreatedOn\", p.\"Token\", p.\"UpdatedBy\", p.\"UpdatedOn\", p.\"UserId\"
FROM \"PushTokens\" AS p
WHERE p.\"UserId\" = @__model_UserId_0 AND p.\"AppName\" = @__ToString_1"

21:15:34 [Information] :: notification-message-queue message received: {"MessageId":"","Body":"","TemplateWithParams":"a bipedal primate mammal (Homo sapiens) that is anatomically related to the great apes but distinguished especially by notable development of the brain with a resultant capacity for articulate (see ARTICULATE entry 1 sense 1a) speech and abstract reasoning, and is the sole living representative of the hominid family","Subject":"Three Onboarding Invitation: Onboarding New Hire.","RecipientEmails":["<EMAIL>"],"PhoneNumber":"","PushToken":"","CC":[],"UserId":"c964a01d-d0f3-4553-b53d-07e773d66525","Application":1,"Attachments":[],"NotificationTypes":[1,3],"Priority":0,"IsWhatsappNo":false,"TemplateWithPlaceHolders":null}

21:15:37 [Information] :: MailGun response {"id":"<<EMAIL>>","message":"Queued. Thank you."}


21:15:39 [Error] :: Firebase messaging error: InvalidArgument - "The registration token is not a valid FCM registration token"
FirebaseAdmin.Messaging.FirebaseMessagingException: The registration token is not a valid FCM registration token
   at FirebaseAdmin.Util.ErrorHandlingHttpClient`1.SendAndReadAsync(HttpRequestMessage request, CancellationToken cancellationToken)
   at FirebaseAdmin.Util.ErrorHandlingHttpClient`1.SendAndDeserializeAsync[TResult](HttpRequestMessage request, CancellationToken cancellationToken)
   at FirebaseAdmin.Messaging.FirebaseMessagingClient.SendAsync(Message message, Boolean dryRun, CancellationToken cancellationToken)
   at FirebaseAdmin.Messaging.FirebaseMessaging.SendAsync(Message message, Boolean dryRun, CancellationToken cancellationToken)
   at FirebaseAdmin.Messaging.FirebaseMessaging.SendAsync(Message message, Boolean dryRun)
   at FirebaseAdmin.Messaging.FirebaseMessaging.SendAsync(Message message)
   at NotificationService.Infrastructure.Services.FirebasePushNotificationService.SendMessage(NotificationMessageModel model) in C:\Users\<USER>\source\repos\Notification-Service\src\NotificationService.Infrastructure\Services\FirebasePushMessageService.cs:line 89

21:15:39 [Information] :: Executed DbCommand ("4"ms) [Parameters=["@p0='?', @p1='?', @p2='?' (DbType = DateTime), @p3='?', @p4='?', @p5='?', @p6='?', @p7='?', @p8='?', @p9='?', @p10='?', @p11='?', @p12='?', @p13='?', @p14='?', @p15='?', @p16='?', @p17='?' (DbType = DateTime), @p18='?', @p19='?', @p20='?', @p21='?' (DbType = DateTime), @p22='?', @p23='?', @p24='?', @p25='?', @p26='?', @p27='?', @p28='?', @p29='?', @p30='?', @p31='?', @p32='?', @p33='?', @p34='?', @p35='?', @p36='?' (DbType = DateTime), @p37='?'"], CommandType='Text', CommandTimeout='30']"
""INSERT INTO \"Notifications\" (\"Body\", \"CreatedBy\", \"CreatedOn\", \"Email\", \"MessageId\", \"MessageSid\", \"NotificationType\", \"PhoneNumber\", \"Priority\", \"PushToken\", \"ResponseId\", \"Status\", \"StatusCode\", \"StatusMessage\", \"Subject\", \"Template\", \"UpdatedBy\", \"UpdatedOn\", \"UserId\")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16, @p17, @p18)
RETURNING \"Id\";
INSERT INTO \"Notifications\" (\"Body\", \"CreatedBy\", \"CreatedOn\", \"Email\", \"MessageId\", \"MessageSid\", \"NotificationType\", \"PhoneNumber\", \"Priority\", \"PushToken\", \"ResponseId\", \"Status\", \"StatusCode\", \"StatusMessage\", \"Subject\", \"Template\", \"UpdatedBy\", \"UpdatedOn\", \"UserId\")
VALUES (@p19, @p20, @p21, @p22, @p23, @p24, @p25, @p26, @p27, @p28, @p29, @p30, @p31, @p32, @p33, @p34, @p35, @p36, @p37)
RETURNING \"Id\";"

21:32:42 [Information] :: Executed DbCommand ("22"ms) [Parameters=[""], CommandType='Text', CommandTimeout='30']"
""SELECT \"MigrationId\", \"ProductVersion\"
FROM \"__EFMigrationsHistory\"
ORDER BY \"MigrationId\";"

21:32:43 [Information] :: Firebase Admin SDK initialized successfully

21:32:43 [Information] :: RabbitMQ Broker Url: amqps://xruwodqv:<EMAIL>/xruwodqv

21:32:43 [Information] :: RabbitMQ Broker Url: amqps://xruwodqv:<EMAIL>/xruwodqv

21:32:43 [Information] :: User profile is available. Using '"C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys"' as key repository and Windows DPAPI to encrypt keys at rest.

21:32:43 [Information] :: Now listening on: "https://localhost:7044"

21:32:43 [Information] :: Now listening on: "http://localhost:5179"

21:32:43 [Information] :: Application started. Press Ctrl+C to shut down.

21:32:43 [Information] :: Hosting environment: "Development"

21:32:43 [Information] :: Content root path: "C:\Users\<USER>\source\repos\Notification-Service\src\NotificationService.API"

21:32:43 [Information] :: Request starting "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/index.html""" - null null

21:32:44 [Information] :: Request finished "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/index.html""" - 200 null "text/html;charset=utf-8" 418.9221ms

21:32:44 [Information] :: Request starting "HTTP/2" "GET" "https"://"localhost:7044""""/_framework/aspnetcore-browser-refresh.js""" - null null

21:32:44 [Information] :: Request starting "HTTP/2" "GET" "https"://"localhost:7044""""/_vs/browserLink""" - null null

21:32:44 [Information] :: Request finished "HTTP/2" "GET" "https"://"localhost:7044""""/_framework/aspnetcore-browser-refresh.js""" - 200 13782 "application/javascript; charset=utf-8" 18.3063ms

21:32:44 [Information] :: Request finished "HTTP/2" "GET" "https"://"localhost:7044""""/_vs/browserLink""" - 200 null "text/javascript; charset=UTF-8" 64.7111ms

21:32:45 [Information] :: Request starting "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/v1/swagger.json""" - null null

21:32:46 [Information] :: Request finished "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/v1/swagger.json""" - 200 null "application/json;charset=utf-8" 295.3766ms

21:33:12 [Information] :: Request starting "HTTP/1.1" "POST" "https"://"localhost:7044""""/api/Notification/queue""" - "application/json" 799

21:33:12 [Information] :: Executing endpoint '"NotificationService.API.Controllers.NotificationController.Queue (NotificationService.API)"'

21:33:12 [Information] :: Route matched with "{action = \"Queue\", controller = \"Notification\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Queue(NotificationService.Core.Models.NotificationMessageModel)" on controller "NotificationService.API.Controllers.NotificationController" ("NotificationService.API").

21:33:21 [Information] :: Successfully published message to RabbitMQ queue: "notification-message-queue"

21:33:23 [Information] :: Executing "OkObjectResult", writing value of type '"System.String"'.

21:33:23 [Information] :: Executed action "NotificationService.API.Controllers.NotificationController.Queue (NotificationService.API)" in 10431.2262ms

21:33:23 [Information] :: Executed endpoint '"NotificationService.API.Controllers.NotificationController.Queue (NotificationService.API)"'

21:33:23 [Information] :: Request finished "HTTP/1.1" "POST" "https"://"localhost:7044""""/api/Notification/queue""" - 200 null "text/plain; charset=utf-8" 11037.5202ms

21:34:32 [Information] :: Request starting "HTTP/1.1" "POST" "https"://"localhost:7044""""/api/Notification/queue""" - "application/json" 799

21:34:32 [Information] :: Executing endpoint '"NotificationService.API.Controllers.NotificationController.Queue (NotificationService.API)"'

21:34:32 [Information] :: Route matched with "{action = \"Queue\", controller = \"Notification\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Queue(NotificationService.Core.Models.NotificationMessageModel)" on controller "NotificationService.API.Controllers.NotificationController" ("NotificationService.API").

21:34:39 [Information] :: Successfully published message to RabbitMQ queue: "notification-message-queue"

21:34:41 [Information] :: Executing "OkObjectResult", writing value of type '"System.String"'.

21:34:41 [Information] :: Executed action "NotificationService.API.Controllers.NotificationController.Queue (NotificationService.API)" in 8381.1856ms

21:34:41 [Information] :: Executed endpoint '"NotificationService.API.Controllers.NotificationController.Queue (NotificationService.API)"'

21:34:41 [Information] :: Request finished "HTTP/1.1" "POST" "https"://"localhost:7044""""/api/Notification/queue""" - 200 null "text/plain; charset=utf-8" 8438.8069ms

21:34:46 [Information] :: Notification Message "{\"MessageId\":\"\",\"Body\":\"\",\"TemplateWithParams\":\"a bipedal primate mammal (Homo sapiens) that is anatomically related to the great apes but distinguished especially by notable development of the brain with a resultant capacity for articulate (see ARTICULATE entry 1 sense 1a) speech and abstract reasoning, and is the sole living representative of the hominid family\",\"Subject\":\"Three Onboarding Invitation: Onboarding New Hire.\",\"RecipientEmails\":[\"<EMAIL>\"],\"PhoneNumber\":\"\",\"PushToken\":\"\",\"CC\":[],\"UserId\":\"c964a01d-d0f3-4553-b53d-07e773d66525\",\"Application\":1,\"Attachments\":[],\"NotificationTypes\":[1,3],\"Priority\":0,\"IsWhatsappNo\":false,\"TemplateWithPlaceHolders\":null}"

21:34:46 [Information] :: Executed DbCommand ("21"ms) [Parameters=["@__model_UserId_0='?', @__ToString_1='?'"], CommandType='Text', CommandTimeout='30']"
""SELECT p.\"Id\", p.\"AppName\", p.\"CreatedBy\", p.\"CreatedOn\", p.\"Token\", p.\"UpdatedBy\", p.\"UpdatedOn\", p.\"UserId\"
FROM \"PushTokens\" AS p
WHERE p.\"UserId\" = @__model_UserId_0 AND p.\"AppName\" = @__ToString_1"

21:34:47 [Information] :: notification-message-queue message received: {"MessageId":"","Body":"","TemplateWithParams":"a bipedal primate mammal (Homo sapiens) that is anatomically related to the great apes but distinguished especially by notable development of the brain with a resultant capacity for articulate (see ARTICULATE entry 1 sense 1a) speech and abstract reasoning, and is the sole living representative of the hominid family","Subject":"Three Onboarding Invitation: Onboarding New Hire.","RecipientEmails":["<EMAIL>"],"PhoneNumber":"","PushToken":"","CC":[],"UserId":"c964a01d-d0f3-4553-b53d-07e773d66525","Application":1,"Attachments":[],"NotificationTypes":[1,3],"Priority":0,"IsWhatsappNo":false,"TemplateWithPlaceHolders":null}

21:35:14 [Information] :: MailGun response {"id":"<<EMAIL>>","message":"Queued. Thank you."}


21:35:18 [Error] :: Firebase messaging error: InvalidArgument - "The registration token is not a valid FCM registration token"
FirebaseAdmin.Messaging.FirebaseMessagingException: The registration token is not a valid FCM registration token
   at FirebaseAdmin.Util.ErrorHandlingHttpClient`1.SendAndReadAsync(HttpRequestMessage request, CancellationToken cancellationToken)
   at FirebaseAdmin.Util.ErrorHandlingHttpClient`1.SendAndDeserializeAsync[TResult](HttpRequestMessage request, CancellationToken cancellationToken)
   at FirebaseAdmin.Messaging.FirebaseMessagingClient.SendAsync(Message message, Boolean dryRun, CancellationToken cancellationToken)
   at FirebaseAdmin.Messaging.FirebaseMessaging.SendAsync(Message message, Boolean dryRun, CancellationToken cancellationToken)
   at FirebaseAdmin.Messaging.FirebaseMessaging.SendAsync(Message message, Boolean dryRun)
   at FirebaseAdmin.Messaging.FirebaseMessaging.SendAsync(Message message)
   at NotificationService.Infrastructure.Services.FirebasePushNotificationService.SendMessage(NotificationMessageModel model) in C:\Users\<USER>\source\repos\Notification-Service\src\NotificationService.Infrastructure\Services\FirebasePushMessageService.cs:line 89

21:35:18 [Information] :: Executed DbCommand ("25"ms) [Parameters=["@p0='?', @p1='?', @p2='?' (DbType = DateTime), @p3='?', @p4='?', @p5='?', @p6='?', @p7='?', @p8='?', @p9='?', @p10='?', @p11='?', @p12='?', @p13='?', @p14='?', @p15='?', @p16='?', @p17='?' (DbType = DateTime), @p18='?', @p19='?', @p20='?', @p21='?' (DbType = DateTime), @p22='?', @p23='?', @p24='?', @p25='?', @p26='?', @p27='?', @p28='?', @p29='?', @p30='?', @p31='?', @p32='?', @p33='?', @p34='?', @p35='?', @p36='?' (DbType = DateTime), @p37='?'"], CommandType='Text', CommandTimeout='30']"
""INSERT INTO \"Notifications\" (\"Body\", \"CreatedBy\", \"CreatedOn\", \"Email\", \"MessageId\", \"MessageSid\", \"NotificationType\", \"PhoneNumber\", \"Priority\", \"PushToken\", \"ResponseId\", \"Status\", \"StatusCode\", \"StatusMessage\", \"Subject\", \"Template\", \"UpdatedBy\", \"UpdatedOn\", \"UserId\")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16, @p17, @p18)
RETURNING \"Id\";
INSERT INTO \"Notifications\" (\"Body\", \"CreatedBy\", \"CreatedOn\", \"Email\", \"MessageId\", \"MessageSid\", \"NotificationType\", \"PhoneNumber\", \"Priority\", \"PushToken\", \"ResponseId\", \"Status\", \"StatusCode\", \"StatusMessage\", \"Subject\", \"Template\", \"UpdatedBy\", \"UpdatedOn\", \"UserId\")
VALUES (@p19, @p20, @p21, @p22, @p23, @p24, @p25, @p26, @p27, @p28, @p29, @p30, @p31, @p32, @p33, @p34, @p35, @p36, @p37)
RETURNING \"Id\";"

21:35:56 [Information] :: Request starting "HTTP/1.1" "POST" "https"://"localhost:7044""""/api/Notification/queue""" - "application/json" 799

21:35:56 [Information] :: Executing endpoint '"NotificationService.API.Controllers.NotificationController.Queue (NotificationService.API)"'

21:35:56 [Information] :: Route matched with "{action = \"Queue\", controller = \"Notification\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Queue(NotificationService.Core.Models.NotificationMessageModel)" on controller "NotificationService.API.Controllers.NotificationController" ("NotificationService.API").

21:36:03 [Information] :: Successfully published message to RabbitMQ queue: "notification-message-queue"

21:36:04 [Information] :: Notification Message "{\"MessageId\":\"\",\"Body\":\"\",\"TemplateWithParams\":\"a bipedal primate mammal (Homo sapiens) that is anatomically related to the great apes but distinguished especially by notable development of the brain with a resultant capacity for articulate (see ARTICULATE entry 1 sense 1a) speech and abstract reasoning, and is the sole living representative of the hominid family\",\"Subject\":\"Three Onboarding Invitation: Onboarding New Hire.\",\"RecipientEmails\":[\"<EMAIL>\"],\"PhoneNumber\":\"\",\"PushToken\":\"\",\"CC\":[],\"UserId\":\"c964a01d-d0f3-4553-b53d-07e773d66525\",\"Application\":1,\"Attachments\":[],\"NotificationTypes\":[1,3],\"Priority\":0,\"IsWhatsappNo\":false,\"TemplateWithPlaceHolders\":null}"

21:36:04 [Information] :: Executed DbCommand ("1"ms) [Parameters=["@__model_UserId_0='?', @__ToString_1='?'"], CommandType='Text', CommandTimeout='30']"
""SELECT p.\"Id\", p.\"AppName\", p.\"CreatedBy\", p.\"CreatedOn\", p.\"Token\", p.\"UpdatedBy\", p.\"UpdatedOn\", p.\"UserId\"
FROM \"PushTokens\" AS p
WHERE p.\"UserId\" = @__model_UserId_0 AND p.\"AppName\" = @__ToString_1"

21:36:04 [Information] :: notification-message-queue message received: {"MessageId":"","Body":"","TemplateWithParams":"a bipedal primate mammal (Homo sapiens) that is anatomically related to the great apes but distinguished especially by notable development of the brain with a resultant capacity for articulate (see ARTICULATE entry 1 sense 1a) speech and abstract reasoning, and is the sole living representative of the hominid family","Subject":"Three Onboarding Invitation: Onboarding New Hire.","RecipientEmails":["<EMAIL>"],"PhoneNumber":"","PushToken":"","CC":[],"UserId":"c964a01d-d0f3-4553-b53d-07e773d66525","Application":1,"Attachments":[],"NotificationTypes":[1,3],"Priority":0,"IsWhatsappNo":false,"TemplateWithPlaceHolders":null}

21:36:06 [Information] :: Executing "OkObjectResult", writing value of type '"System.String"'.

21:36:06 [Information] :: Executed action "NotificationService.API.Controllers.NotificationController.Queue (NotificationService.API)" in 9187.0475ms

21:36:06 [Information] :: Executed endpoint '"NotificationService.API.Controllers.NotificationController.Queue (NotificationService.API)"'

21:36:06 [Information] :: Request finished "HTTP/1.1" "POST" "https"://"localhost:7044""""/api/Notification/queue""" - 200 null "text/plain; charset=utf-8" 9223.8392ms

21:36:07 [Information] :: MailGun response {"id":"<<EMAIL>>","message":"Queued. Thank you."}


21:36:08 [Error] :: Firebase messaging error: InvalidArgument - "The registration token is not a valid FCM registration token"
FirebaseAdmin.Messaging.FirebaseMessagingException: The registration token is not a valid FCM registration token
   at FirebaseAdmin.Util.ErrorHandlingHttpClient`1.SendAndReadAsync(HttpRequestMessage request, CancellationToken cancellationToken)
   at FirebaseAdmin.Util.ErrorHandlingHttpClient`1.SendAndDeserializeAsync[TResult](HttpRequestMessage request, CancellationToken cancellationToken)
   at FirebaseAdmin.Messaging.FirebaseMessagingClient.SendAsync(Message message, Boolean dryRun, CancellationToken cancellationToken)
   at FirebaseAdmin.Messaging.FirebaseMessaging.SendAsync(Message message, Boolean dryRun, CancellationToken cancellationToken)
   at FirebaseAdmin.Messaging.FirebaseMessaging.SendAsync(Message message, Boolean dryRun)
   at FirebaseAdmin.Messaging.FirebaseMessaging.SendAsync(Message message)
   at NotificationService.Infrastructure.Services.FirebasePushNotificationService.SendMessage(NotificationMessageModel model) in C:\Users\<USER>\source\repos\Notification-Service\src\NotificationService.Infrastructure\Services\FirebasePushMessageService.cs:line 89

21:36:08 [Information] :: Executed DbCommand ("1"ms) [Parameters=["@p0='?', @p1='?', @p2='?' (DbType = DateTime), @p3='?', @p4='?', @p5='?', @p6='?', @p7='?', @p8='?', @p9='?', @p10='?', @p11='?', @p12='?', @p13='?', @p14='?', @p15='?', @p16='?', @p17='?' (DbType = DateTime), @p18='?', @p19='?', @p20='?', @p21='?' (DbType = DateTime), @p22='?', @p23='?', @p24='?', @p25='?', @p26='?', @p27='?', @p28='?', @p29='?', @p30='?', @p31='?', @p32='?', @p33='?', @p34='?', @p35='?', @p36='?' (DbType = DateTime), @p37='?'"], CommandType='Text', CommandTimeout='30']"
""INSERT INTO \"Notifications\" (\"Body\", \"CreatedBy\", \"CreatedOn\", \"Email\", \"MessageId\", \"MessageSid\", \"NotificationType\", \"PhoneNumber\", \"Priority\", \"PushToken\", \"ResponseId\", \"Status\", \"StatusCode\", \"StatusMessage\", \"Subject\", \"Template\", \"UpdatedBy\", \"UpdatedOn\", \"UserId\")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16, @p17, @p18)
RETURNING \"Id\";
INSERT INTO \"Notifications\" (\"Body\", \"CreatedBy\", \"CreatedOn\", \"Email\", \"MessageId\", \"MessageSid\", \"NotificationType\", \"PhoneNumber\", \"Priority\", \"PushToken\", \"ResponseId\", \"Status\", \"StatusCode\", \"StatusMessage\", \"Subject\", \"Template\", \"UpdatedBy\", \"UpdatedOn\", \"UserId\")
VALUES (@p19, @p20, @p21, @p22, @p23, @p24, @p25, @p26, @p27, @p28, @p29, @p30, @p31, @p32, @p33, @p34, @p35, @p36, @p37)
RETURNING \"Id\";"

21:43:19 [Information] :: Executed DbCommand ("24"ms) [Parameters=[""], CommandType='Text', CommandTimeout='30']"
""SELECT \"MigrationId\", \"ProductVersion\"
FROM \"__EFMigrationsHistory\"
ORDER BY \"MigrationId\";"

21:43:19 [Information] :: Firebase Admin SDK initialized successfully

21:43:19 [Information] :: RabbitMQ Broker Url: amqps://xruwodqv:<EMAIL>/xruwodqv

21:43:23 [Information] :: RabbitMQ Broker Url: amqps://xruwodqv:<EMAIL>/xruwodqv

21:43:27 [Information] :: User profile is available. Using '"C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys"' as key repository and Windows DPAPI to encrypt keys at rest.

21:43:28 [Information] :: Now listening on: "https://localhost:7044"

21:43:28 [Information] :: Now listening on: "http://localhost:5179"

21:43:28 [Information] :: Application started. Press Ctrl+C to shut down.

21:43:28 [Information] :: Hosting environment: "Development"

21:43:28 [Information] :: Content root path: "C:\Users\<USER>\source\repos\Notification-Service\src\NotificationService.API"

21:43:29 [Information] :: Request starting "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/index.html""" - null null

21:43:30 [Information] :: Request finished "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/index.html""" - 200 null "text/html;charset=utf-8" 902.6639ms

21:43:30 [Information] :: Request starting "HTTP/2" "GET" "https"://"localhost:7044""""/_vs/browserLink""" - null null

21:43:30 [Information] :: Request starting "HTTP/2" "GET" "https"://"localhost:7044""""/_framework/aspnetcore-browser-refresh.js""" - null null

21:43:30 [Information] :: Request finished "HTTP/2" "GET" "https"://"localhost:7044""""/_framework/aspnetcore-browser-refresh.js""" - 200 13782 "application/javascript; charset=utf-8" 97.2171ms

21:43:30 [Information] :: Request finished "HTTP/2" "GET" "https"://"localhost:7044""""/_vs/browserLink""" - 200 null "text/javascript; charset=UTF-8" 195.5764ms

21:43:31 [Information] :: Request starting "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/v1/swagger.json""" - null null

21:43:31 [Information] :: Request finished "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/v1/swagger.json""" - 200 null "application/json;charset=utf-8" 261.473ms

21:43:37 [Information] :: Request starting "HTTP/1.1" "POST" "https"://"localhost:7044""""/api/Notification/queue""" - "application/json" 799

21:43:37 [Information] :: Executing endpoint '"NotificationService.API.Controllers.NotificationController.Queue (NotificationService.API)"'

21:43:37 [Information] :: Route matched with "{action = \"Queue\", controller = \"Notification\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Queue(NotificationService.Core.Models.NotificationMessageModel)" on controller "NotificationService.API.Controllers.NotificationController" ("NotificationService.API").

21:43:44 [Information] :: Successfully published message to RabbitMQ queue: "notification-message-queue"

21:43:45 [Information] :: Notification Message "{\"MessageId\":\"\",\"Body\":\"\",\"TemplateWithParams\":\"a bipedal primate mammal (Homo sapiens) that is anatomically related to the great apes but distinguished especially by notable development of the brain with a resultant capacity for articulate (see ARTICULATE entry 1 sense 1a) speech and abstract reasoning, and is the sole living representative of the hominid family\",\"Subject\":\"Three Onboarding Invitation: Onboarding New Hire.\",\"RecipientEmails\":[\"<EMAIL>\"],\"PhoneNumber\":\"\",\"PushToken\":\"\",\"CC\":[],\"UserId\":\"c964a01d-d0f3-4553-b53d-07e773d66525\",\"Application\":1,\"Attachments\":[],\"NotificationTypes\":[1,3],\"Priority\":0,\"IsWhatsappNo\":false,\"TemplateWithPlaceHolders\":null}"

21:43:45 [Information] :: Executing "OkObjectResult", writing value of type '"System.String"'.

21:43:45 [Information] :: Executed action "NotificationService.API.Controllers.NotificationController.Queue (NotificationService.API)" in 8110.7102ms

21:43:45 [Information] :: Executed endpoint '"NotificationService.API.Controllers.NotificationController.Queue (NotificationService.API)"'

21:43:45 [Information] :: Request finished "HTTP/1.1" "POST" "https"://"localhost:7044""""/api/Notification/queue""" - 200 null "text/plain; charset=utf-8" 8401.3569ms

21:43:46 [Information] :: Executed DbCommand ("21"ms) [Parameters=["@__model_UserId_0='?', @__ToString_1='?'"], CommandType='Text', CommandTimeout='30']"
""SELECT p.\"Id\", p.\"AppName\", p.\"CreatedBy\", p.\"CreatedOn\", p.\"Token\", p.\"UpdatedBy\", p.\"UpdatedOn\", p.\"UserId\"
FROM \"PushTokens\" AS p
WHERE p.\"UserId\" = @__model_UserId_0 AND p.\"AppName\" = @__ToString_1"

21:43:46 [Information] :: notification-message-queue message received: {"MessageId":"","Body":"","TemplateWithParams":"a bipedal primate mammal (Homo sapiens) that is anatomically related to the great apes but distinguished especially by notable development of the brain with a resultant capacity for articulate (see ARTICULATE entry 1 sense 1a) speech and abstract reasoning, and is the sole living representative of the hominid family","Subject":"Three Onboarding Invitation: Onboarding New Hire.","RecipientEmails":["<EMAIL>"],"PhoneNumber":"","PushToken":"","CC":[],"UserId":"c964a01d-d0f3-4553-b53d-07e773d66525","Application":1,"Attachments":[],"NotificationTypes":[1,3],"Priority":0,"IsWhatsappNo":false,"TemplateWithPlaceHolders":null}

21:43:48 [Information] :: MailGun response {"id":"<<EMAIL>>","message":"Queued. Thank you."}


21:43:53 [Error] :: Firebase messaging error: InvalidArgument - "The registration token is not a valid FCM registration token"
FirebaseAdmin.Messaging.FirebaseMessagingException: The registration token is not a valid FCM registration token
   at FirebaseAdmin.Util.ErrorHandlingHttpClient`1.SendAndReadAsync(HttpRequestMessage request, CancellationToken cancellationToken)
   at FirebaseAdmin.Util.ErrorHandlingHttpClient`1.SendAndDeserializeAsync[TResult](HttpRequestMessage request, CancellationToken cancellationToken)
   at FirebaseAdmin.Messaging.FirebaseMessagingClient.SendAsync(Message message, Boolean dryRun, CancellationToken cancellationToken)
   at FirebaseAdmin.Messaging.FirebaseMessaging.SendAsync(Message message, Boolean dryRun, CancellationToken cancellationToken)
   at FirebaseAdmin.Messaging.FirebaseMessaging.SendAsync(Message message, Boolean dryRun)
   at FirebaseAdmin.Messaging.FirebaseMessaging.SendAsync(Message message)
   at NotificationService.Infrastructure.Services.FirebasePushNotificationService.SendMessage(NotificationMessageModel model) in C:\Users\<USER>\source\repos\Notification-Service\src\NotificationService.Infrastructure\Services\FirebasePushMessageService.cs:line 89

21:43:54 [Information] :: Executed DbCommand ("27"ms) [Parameters=["@p0='?', @p1='?', @p2='?' (DbType = DateTime), @p3='?', @p4='?', @p5='?', @p6='?', @p7='?', @p8='?', @p9='?', @p10='?', @p11='?', @p12='?', @p13='?', @p14='?', @p15='?', @p16='?', @p17='?' (DbType = DateTime), @p18='?', @p19='?', @p20='?', @p21='?' (DbType = DateTime), @p22='?', @p23='?', @p24='?', @p25='?', @p26='?', @p27='?', @p28='?', @p29='?', @p30='?', @p31='?', @p32='?', @p33='?', @p34='?', @p35='?', @p36='?' (DbType = DateTime), @p37='?'"], CommandType='Text', CommandTimeout='30']"
""INSERT INTO \"Notifications\" (\"Body\", \"CreatedBy\", \"CreatedOn\", \"Email\", \"MessageId\", \"MessageSid\", \"NotificationType\", \"PhoneNumber\", \"Priority\", \"PushToken\", \"ResponseId\", \"Status\", \"StatusCode\", \"StatusMessage\", \"Subject\", \"Template\", \"UpdatedBy\", \"UpdatedOn\", \"UserId\")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16, @p17, @p18)
RETURNING \"Id\";
INSERT INTO \"Notifications\" (\"Body\", \"CreatedBy\", \"CreatedOn\", \"Email\", \"MessageId\", \"MessageSid\", \"NotificationType\", \"PhoneNumber\", \"Priority\", \"PushToken\", \"ResponseId\", \"Status\", \"StatusCode\", \"StatusMessage\", \"Subject\", \"Template\", \"UpdatedBy\", \"UpdatedOn\", \"UserId\")
VALUES (@p19, @p20, @p21, @p22, @p23, @p24, @p25, @p26, @p27, @p28, @p29, @p30, @p31, @p32, @p33, @p34, @p35, @p36, @p37)
RETURNING \"Id\";"

21:44:03 [Information] :: Request starting "HTTP/1.1" "POST" "https"://"localhost:7044""""/api/Notification/queue""" - "application/json" 799

21:44:03 [Information] :: Executing endpoint '"NotificationService.API.Controllers.NotificationController.Queue (NotificationService.API)"'

21:44:03 [Information] :: Route matched with "{action = \"Queue\", controller = \"Notification\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Queue(NotificationService.Core.Models.NotificationMessageModel)" on controller "NotificationService.API.Controllers.NotificationController" ("NotificationService.API").

21:44:11 [Information] :: Successfully published message to RabbitMQ queue: "notification-message-queue"

21:44:11 [Information] :: Notification Message "{\"MessageId\":\"\",\"Body\":\"\",\"TemplateWithParams\":\"a bipedal primate mammal (Homo sapiens) that is anatomically related to the great apes but distinguished especially by notable development of the brain with a resultant capacity for articulate (see ARTICULATE entry 1 sense 1a) speech and abstract reasoning, and is the sole living representative of the hominid family\",\"Subject\":\"Three Onboarding Invitation: Onboarding New Hire.\",\"RecipientEmails\":[\"<EMAIL>\"],\"PhoneNumber\":\"\",\"PushToken\":\"\",\"CC\":[],\"UserId\":\"c964a01d-d0f3-4553-b53d-07e773d66525\",\"Application\":1,\"Attachments\":[],\"NotificationTypes\":[1,3],\"Priority\":0,\"IsWhatsappNo\":false,\"TemplateWithPlaceHolders\":null}"

21:44:11 [Information] :: Executed DbCommand ("1"ms) [Parameters=["@__model_UserId_0='?', @__ToString_1='?'"], CommandType='Text', CommandTimeout='30']"
""SELECT p.\"Id\", p.\"AppName\", p.\"CreatedBy\", p.\"CreatedOn\", p.\"Token\", p.\"UpdatedBy\", p.\"UpdatedOn\", p.\"UserId\"
FROM \"PushTokens\" AS p
WHERE p.\"UserId\" = @__model_UserId_0 AND p.\"AppName\" = @__ToString_1"

21:44:11 [Information] :: notification-message-queue message received: {"MessageId":"","Body":"","TemplateWithParams":"a bipedal primate mammal (Homo sapiens) that is anatomically related to the great apes but distinguished especially by notable development of the brain with a resultant capacity for articulate (see ARTICULATE entry 1 sense 1a) speech and abstract reasoning, and is the sole living representative of the hominid family","Subject":"Three Onboarding Invitation: Onboarding New Hire.","RecipientEmails":["<EMAIL>"],"PhoneNumber":"","PushToken":"","CC":[],"UserId":"c964a01d-d0f3-4553-b53d-07e773d66525","Application":1,"Attachments":[],"NotificationTypes":[1,3],"Priority":0,"IsWhatsappNo":false,"TemplateWithPlaceHolders":null}

21:44:12 [Information] :: Executing "OkObjectResult", writing value of type '"System.String"'.

21:44:12 [Information] :: Executed action "NotificationService.API.Controllers.NotificationController.Queue (NotificationService.API)" in 8738.9704ms

21:44:12 [Information] :: Executed endpoint '"NotificationService.API.Controllers.NotificationController.Queue (NotificationService.API)"'

21:44:12 [Information] :: Request finished "HTTP/1.1" "POST" "https"://"localhost:7044""""/api/Notification/queue""" - 200 null "text/plain; charset=utf-8" 8774.3246ms

21:44:15 [Information] :: MailGun response {"id":"<<EMAIL>>","message":"Queued. Thank you."}


21:44:15 [Error] :: Firebase messaging error: InvalidArgument - "The registration token is not a valid FCM registration token"
FirebaseAdmin.Messaging.FirebaseMessagingException: The registration token is not a valid FCM registration token
   at FirebaseAdmin.Util.ErrorHandlingHttpClient`1.SendAndReadAsync(HttpRequestMessage request, CancellationToken cancellationToken)
   at FirebaseAdmin.Util.ErrorHandlingHttpClient`1.SendAndDeserializeAsync[TResult](HttpRequestMessage request, CancellationToken cancellationToken)
   at FirebaseAdmin.Messaging.FirebaseMessagingClient.SendAsync(Message message, Boolean dryRun, CancellationToken cancellationToken)
   at FirebaseAdmin.Messaging.FirebaseMessaging.SendAsync(Message message, Boolean dryRun, CancellationToken cancellationToken)
   at FirebaseAdmin.Messaging.FirebaseMessaging.SendAsync(Message message, Boolean dryRun)
   at FirebaseAdmin.Messaging.FirebaseMessaging.SendAsync(Message message)
   at NotificationService.Infrastructure.Services.FirebasePushNotificationService.SendMessage(NotificationMessageModel model) in C:\Users\<USER>\source\repos\Notification-Service\src\NotificationService.Infrastructure\Services\FirebasePushMessageService.cs:line 89

21:44:15 [Information] :: Executed DbCommand ("1"ms) [Parameters=["@p0='?', @p1='?', @p2='?' (DbType = DateTime), @p3='?', @p4='?', @p5='?', @p6='?', @p7='?', @p8='?', @p9='?', @p10='?', @p11='?', @p12='?', @p13='?', @p14='?', @p15='?', @p16='?', @p17='?' (DbType = DateTime), @p18='?', @p19='?', @p20='?', @p21='?' (DbType = DateTime), @p22='?', @p23='?', @p24='?', @p25='?', @p26='?', @p27='?', @p28='?', @p29='?', @p30='?', @p31='?', @p32='?', @p33='?', @p34='?', @p35='?', @p36='?' (DbType = DateTime), @p37='?'"], CommandType='Text', CommandTimeout='30']"
""INSERT INTO \"Notifications\" (\"Body\", \"CreatedBy\", \"CreatedOn\", \"Email\", \"MessageId\", \"MessageSid\", \"NotificationType\", \"PhoneNumber\", \"Priority\", \"PushToken\", \"ResponseId\", \"Status\", \"StatusCode\", \"StatusMessage\", \"Subject\", \"Template\", \"UpdatedBy\", \"UpdatedOn\", \"UserId\")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16, @p17, @p18)
RETURNING \"Id\";
INSERT INTO \"Notifications\" (\"Body\", \"CreatedBy\", \"CreatedOn\", \"Email\", \"MessageId\", \"MessageSid\", \"NotificationType\", \"PhoneNumber\", \"Priority\", \"PushToken\", \"ResponseId\", \"Status\", \"StatusCode\", \"StatusMessage\", \"Subject\", \"Template\", \"UpdatedBy\", \"UpdatedOn\", \"UserId\")
VALUES (@p19, @p20, @p21, @p22, @p23, @p24, @p25, @p26, @p27, @p28, @p29, @p30, @p31, @p32, @p33, @p34, @p35, @p36, @p37)
RETURNING \"Id\";"
