﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace NotificationService.Core.Exceptions
{
    public class ApiException : Exception
    {
        int HttpStatus { get; set; }
        public int Code { get; set; }
        public ApiException(int code, string message) : base(message)
        {
            Code = code;
            HttpStatus = 200;
        }
    }
}
