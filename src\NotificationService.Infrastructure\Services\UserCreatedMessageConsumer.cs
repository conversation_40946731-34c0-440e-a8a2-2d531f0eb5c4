using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using NotificationService.Core.Models;
using NotificationService.Core.Services;
using RabbitMQ.Client;
using WatchDog;

namespace NotificationService.Infrastructure.Services
{
    public class UserCreatedMessageConsumer : BaseConsumer
    {
        private readonly IUserService _userService;
        public UserCreatedMessageConsumer(IUserService userUservice, IConfiguration configuration, ILogger<NotificationMessageConsumer> logger) : base(configuration, logger)
        {
            _queue = "user-created-queue";
            _exchange = "user-created-event";
            _userService = userUservice;
            _exchangeType = ExchangeType.Fanout;
        }

        public override async Task ProcessMessage(String message)
        {
            try
            {
                _logger.LogInformation("User Created Message {0}", message);
                WatchLogger.Log("User Created Message {0}", message);

                var payload = JsonConvert.DeserializeObject<CreateUserModel>(message);
                await _userService.CreateUser(payload);
            }
            catch (Exception ex)
            {
                _logger.LogError("Error processing user created message {0}", ex.Message);
                WatchLogger.Log($"Error processing user created message - {ex.Message}", ex.ToString(), nameof(UserCreatedMessageConsumer));
            }
        }
    }
}
