﻿using NotificationService.Core.Constants;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.NetworkInformation;
using System.Text;
using System.Threading.Tasks;

namespace NotificationService.Core.Models
{
    public class ApiResponse<T> : BaseApiResponse
    {
        public T? Data { get; set; }
    }

    public class BaseApiResponse
    {
        public BaseApiResponse Ok(string message)
        {
            return new BaseApiResponse() { Message = message, Status = (int)ApiStatus.Ok };
        }

        public BaseApiResponse Ok()
        {
            return new BaseApiResponse() { Message = "Ok", Status = (int)ApiStatus.Ok };
        }

        public BaseApiResponse OkWithData<T>(string message, T data)
        {
            return new ApiResponse<T>() { Message = message, Status = (int)ApiStatus.Ok, Data = data };
        }

        public BaseApiResponse OkWithData<T>(T data)
        {
            return new ApiResponse<T>()
            {
                Message = "Ok",
                Status = (int)ApiStatus.Ok,
                Data = data
            };
        }

        public BaseApiResponse Bad(string message)
        {
            return new BaseApiResponse() { Message = message, Status = (int)ApiStatus.ValidationError };
        }

        public BaseApiResponse Fail(string message)
        {
            return new BaseApiResponse() { Message = message, Status = (int)ApiStatus.Failed };
        }
        public int Status { get; set; }
        public string? Message { get; set; }

        public string? DevMessage { get; set; }
    }

}
