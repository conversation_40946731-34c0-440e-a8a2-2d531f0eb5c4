using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using NotificationService.Core.Constants;
using NotificationService.Core.Models;
using NotificationService.Core.Services;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Security.Cryptography;
using Microsoft.IdentityModel.Tokens;
using System.IdentityModel.Tokens.Jwt;

namespace NotificationService.Infrastructure.Services
{
    public class ApnsService : IApnsService
    {
        private readonly ILogger<ApnsService> _logger;
        private readonly IConfiguration _configuration;
        private readonly string? _apnsKeyPath;
        private readonly string? _keyId;
        private readonly string? _teamId;
        private readonly string? _bundleId;
        private readonly string? _voipTopic;
        private readonly bool _isProduction;
        private readonly HttpClient _httpClient;
        private readonly string _apnsUrl;

        public ApnsService(IConfiguration configuration, ILogger<ApnsService> logger, IHttpClientFactory httpClientFactory)
        {
            _configuration = configuration;
            _logger = logger;

            // APNs configuration
            _apnsKeyPath = Path.Combine(Directory.GetCurrentDirectory(), "Secrets", "AuthKey.p8");
            _keyId = configuration["APNs:KeyId"];
            _teamId = configuration["APNs:TeamId"];
            _bundleId = configuration["FCM:ApnsBundleId"] ?? "com.jobpro.joble";
            _voipTopic = configuration["FCM:ApnsVoipTopic"] ?? "com.jobpro.joble.voip";
            _isProduction = configuration.GetValue<bool>("APNs:IsProduction", false);

            _httpClient = httpClientFactory.CreateClient();
            _apnsUrl = _isProduction ? "https://api.push.apple.com" : "https://api.sandbox.push.apple.com";

            _logger.LogInformation("APNs Service initialized. Production: {IsProduction}, URL: {ApnsUrl}", _isProduction, _apnsUrl);
        }

        private string GenerateJwtToken()
        {
            if (string.IsNullOrEmpty(_keyId) || string.IsNullOrEmpty(_teamId) || !File.Exists(_apnsKeyPath))
            {
                throw new InvalidOperationException("APNs configuration is incomplete");
            }

            try
            {
                var privateKeyContent = File.ReadAllText(_apnsKeyPath);

                // Remove header and footer from the private key
                var keyContent = privateKeyContent
                    .Replace("-----BEGIN PRIVATE KEY-----", "")
                    .Replace("-----END PRIVATE KEY-----", "")
                    .Replace("\n", "")
                    .Replace("\r", "");

                var keyBytes = Convert.FromBase64String(keyContent);

                using var ecdsa = ECDsa.Create();
                ecdsa.ImportPkcs8PrivateKey(keyBytes, out _);

                var securityKey = new ECDsaSecurityKey(ecdsa) { KeyId = _keyId };
                var credentials = new SigningCredentials(securityKey, SecurityAlgorithms.EcdsaSha256);

                var now = DateTimeOffset.UtcNow;
                var tokenDescriptor = new SecurityTokenDescriptor
                {
                    Issuer = _teamId,
                    IssuedAt = now.DateTime,
                    Expires = now.AddMinutes(60).DateTime,
                    SigningCredentials = credentials
                };

                var tokenHandler = new JwtSecurityTokenHandler();
                var token = tokenHandler.CreateToken(tokenDescriptor);
                return tokenHandler.WriteToken(token);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating JWT token for APNs");
                throw;
            }
        }

        public async Task<MessageResponseModel> SendVoipPushAsync(NotificationMessageModel model)
        {
            try
            {
                _logger.LogInformation("Sending VoIP push to token: {Token}", model.PushToken);

                // Create VoIP push payload
                var payload = CreateVoipPayload(model);

                // Add custom data if available
                if (model.TemplateWithPlaceHolders?.Props != null)
                {
                    foreach (var prop in model.TemplateWithPlaceHolders.Props)
                    {
                        if (prop.Key != "aps") // Don't override aps
                        {
                            payload[prop.Key] = prop.Value;
                        }
                    }
                }

                var jsonPayload = JsonSerializer.Serialize(payload);
                var content = new StringContent(jsonPayload, Encoding.UTF8, "application/json");

                // Generate JWT token
                var jwtToken = GenerateJwtToken();

                // Set headers
                var request = new HttpRequestMessage(HttpMethod.Post, $"{_apnsUrl}/3/device/{model.PushToken}")
                {
                    Content = content
                };

                request.Headers.Add("authorization", $"bearer {jwtToken}");
                request.Headers.Add("apns-id", Guid.NewGuid().ToString());
                request.Headers.Add("apns-push-type", "voip");
                request.Headers.Add("apns-topic", _voipTopic);
                request.Headers.Add("apns-priority", "10");

                var response = await _httpClient.SendAsync(request);
                var responseContent = await response.Content.ReadAsStringAsync();

                _logger.LogInformation("VoIP push sent. Status: {StatusCode}, Response: {Response}",
                    response.StatusCode, responseContent);

                return new MessageResponseModel
                {
                    ResponseId = request.Headers.GetValues("apns-id").FirstOrDefault() ?? Guid.NewGuid().ToString(),
                    StatusCode = ((int)response.StatusCode).ToString(),
                    StatusMessage = response.IsSuccessStatusCode ? "VoIP push sent successfully" : responseContent,
                    Status = response.IsSuccessStatusCode ? NotificationStatus.Delivered : NotificationStatus.Failed
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending VoIP push notification");
                return new MessageResponseModel
                {
                    StatusCode = "500",
                    StatusMessage = ex.Message,
                    Status = NotificationStatus.Failed
                };
            }
        }

        public async Task<MessageResponseModel> SendAlertPushAsync(NotificationMessageModel model)
        {
            try
            {
                _logger.LogInformation("Sending alert push to token: {Token}", model.PushToken);

                var payload = new Dictionary<string, object>
                {
                    ["aps"] = new Dictionary<string, object>
                    {
                        ["alert"] = new Dictionary<string, object>
                        {
                            ["title"] = model.Subject,
                            ["body"] = model.Body
                        },
                        ["sound"] = "default",
                        ["badge"] = 1
                    }
                };

                // Add custom data if available
                if (model.TemplateWithPlaceHolders?.Props != null)
                {
                    foreach (var prop in model.TemplateWithPlaceHolders.Props)
                    {
                        if (prop.Key != "aps") // Don't override aps
                        {
                            payload[prop.Key] = prop.Value;
                        }
                    }
                }

                var jsonPayload = JsonSerializer.Serialize(payload);
                var content = new StringContent(jsonPayload, Encoding.UTF8, "application/json");

                // Generate JWT token
                var jwtToken = GenerateJwtToken();

                // Set headers
                var request = new HttpRequestMessage(HttpMethod.Post, $"{_apnsUrl}/3/device/{model.PushToken}")
                {
                    Content = content
                };

                request.Headers.Add("authorization", $"bearer {jwtToken}");
                request.Headers.Add("apns-id", Guid.NewGuid().ToString());
                request.Headers.Add("apns-push-type", "alert");
                request.Headers.Add("apns-topic", _bundleId);
                request.Headers.Add("apns-priority", "10");

                var response = await _httpClient.SendAsync(request);
                var responseContent = await response.Content.ReadAsStringAsync();

                _logger.LogInformation("Alert push sent. Status: {StatusCode}, Response: {Response}",
                    response.StatusCode, responseContent);

                return new MessageResponseModel
                {
                    ResponseId = request.Headers.GetValues("apns-id").FirstOrDefault() ?? Guid.NewGuid().ToString(),
                    StatusCode = ((int)response.StatusCode).ToString(),
                    StatusMessage = response.IsSuccessStatusCode ? "Alert push sent successfully" : responseContent,
                    Status = response.IsSuccessStatusCode ? NotificationStatus.Delivered : NotificationStatus.Failed
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending alert push notification");
                return new MessageResponseModel
                {
                    StatusCode = "500",
                    StatusMessage = ex.Message,
                    Status = NotificationStatus.Failed
                };
            }
        }

        private Dictionary<string, object> CreateVoipPayload(NotificationMessageModel model)
        {
            return new Dictionary<string, object>
            {
                ["aps"] = new Dictionary<string, object>
                {
                    ["alert"] = new Dictionary<string, object>
                    {
                        ["title"] = model.Subject,
                        ["body"] = model.Body
                    },
                    ["sound"] = "default",
                    ["badge"] = 1,
                    ["category"] = "INCOMING_CALL",
                    ["content-available"] = 1
                }
            };
        }


    }
}
