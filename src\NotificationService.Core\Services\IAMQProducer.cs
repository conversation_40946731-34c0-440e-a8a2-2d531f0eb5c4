﻿namespace NotificationService.Core.Services
{
    /// <summary>
    /// Interface for message publishing operations
    /// </summary>
    public interface IAMQProducer
    {
        /// <summary>
        /// Publishes a message to the specified destination
        /// </summary>
        /// <typeparam name="TMessage">Type of the message to be published</typeparam>
        /// <param name="message">The message to publish</param>
        /// <param name="destination">The destination (queue/topic) to publish to</param>
        /// <returns>True if the publish operation was successful, false otherwise</returns>
        Task<bool> PublishAsync<TMessage>(TMessage message, string destination)
            where TMessage : class;
    }
}