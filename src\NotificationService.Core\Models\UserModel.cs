﻿namespace NotificationService.Core.Models
{
    public class UserDto
    {
        public string FirstName { get; set; }
        public string LastName { get; set; }
        public string MiddleName { get; set; }
        public string PhoneCountryCode { get; set; }
        public DateTime Created_At { get; set; }
        public string ForgotPasswordToken { get; set; }
        public DateTime? ForgotPasswordTokenExpiry { get; set; }
        public string EmailVerificationToken { get; set; }
        public DateTime? EmailVerificationTokenExpiry { get; set; }
        public string ProfileImageUrl { get; set; }
        public string ProofOfResidence { get; set; }
        public string GovernmentId { get; set; }
        public string Gender { get; set; }
        public string Status { get; set; }
        public DateTime? DateOfBirth { get; set; }
        public string StatusComment { get; set; }
        public string InvitedBy { get; set; }
        public DateTime Modified_At { get; set; }
        public string CV_URL { get; set; }
        public string OldReference { get; set; }
        public string NewReference { get; set; }
        public bool SendMail { get; set; }
        public bool PasswordCreatedByAdmin { get; set; }
        public string CompanyId { get; set; }
        public string UserInCompanyRole { get; set; }
        public bool IsVerified { get; set; }
        public bool IsEmailVerified { get; set; }
        public bool IsPhoneNumberVerified { get; set; }
        public string PhoneNumberVerificationToken { get; set; }
        public DateTime? PhoneNumberVerificationTokenExpiry { get; set; }
        public string Region { get; set; }
        public string Address { get; set; }
        public string ZipCode { get; set; }
        public string CountryCode { get; set; }
        public string Country { get; set; }
        public string TimeZone { get; set; }
        public string State { get; set; }
        public string JobPaysPin { get; set; }
        public string WeavrPasscode { get; set; }
        public string WeavrId { get; set; }
        public bool WeavrPasscodeForPublic { get; set; }
        public string BaseCurrency { get; set; }
        public string UserType { get; set; }
        public string UpdatedBy { get; set; }
        public string ClientRoleId { get; set; }
        public string ClientRole { get; set; }
        public string IpAddress { get; set; }
        public bool LockIpAddress { get; set; }
        public string Profession { get; set; }
        public string GoogleAuthId { get; set; }
        public string MicrosoftAuthId { get; set; }
        public string SecondaryEmail { get; set; }
        public string SecondaryPhoneNumber { get; set; }
        public string ReferralCode { get; set; }
        public int IndividualUserAccountStatus { get; set; }
        public List<string> UserEmployeeAppRole { get; set; }
        public string UserCompany { get; set; }
        public string Id { get; set; }
        public string UserName { get; set; }
        public string NormalizedUserName { get; set; }
        public string Email { get; set; }
        public string NormalizedEmail { get; set; }
        public bool EmailConfirmed { get; set; }
        public string PasswordHash { get; set; }
        public string SecurityStamp { get; set; }
        public string ConcurrencyStamp { get; set; }
        public string PhoneNumber { get; set; }
        public bool PhoneNumberConfirmed { get; set; }
        public bool TwoFactorEnabled { get; set; }
        public DateTime? LockoutEnd { get; set; }
        public bool LockoutEnabled { get; set; }
        public int AccessFailedCount { get; set; }
    }
}
