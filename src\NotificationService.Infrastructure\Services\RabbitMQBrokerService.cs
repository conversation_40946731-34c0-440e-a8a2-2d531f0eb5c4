using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using NotificationService.Core.Services;

namespace NotificationService.Infrastructure.Services
{
    public class RabbitMQBrokerService : BackgroundService, IMessageBrokerService
    {
        private readonly IConfiguration _configuration;
        private readonly IEnumerable<IAMQConsumer> _consumers;
        private readonly ILogger<RabbitMQBrokerService> _logger;

        public RabbitMQBrokerService(IConfiguration configuration, IEnumerable<IAMQConsumer> consumers, ILogger<RabbitMQBrokerService> logger)
        {
            _configuration = configuration;
            _consumers = consumers;
            _logger = logger;
        }

        #region Start Consumer
        public void StartConsumer(CancellationToken cancellationToken)
        {
            try
            {
                foreach (var consumer in _consumers)
                {
                    consumer.StartConsumer();
                    _logger.LogInformation("Started consumer: {ConsumerType}", consumer.GetType().Name);
                }
            }
            catch (OperationCanceledException operationCancelled)
            {
                _logger.LogError("Consumer Operation Cancelled {@ConsumerOperationCancelled}", operationCancelled);
            }
            catch (Exception e)
            {
                _logger.LogError("Consumer General Exception {@ConsumerGeneralException}", e);
                throw; // Re-throw to prevent the service from starting with failed consumers
            }
        }
        #endregion

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            // Start all consumers once
            StartConsumer(stoppingToken);

            // Keep the background service alive while consumers are running
            try
            {
                while (!stoppingToken.IsCancellationRequested)
                {
                    await Task.Delay(5000, stoppingToken); // Check every 5 seconds
                }
            }
            catch (OperationCanceledException)
            {
                _logger.LogInformation("RabbitMQ Broker Service execution cancelled");
            }
        }

        public override Task StopAsync(CancellationToken cancellationToken)
        {
            StopConsumer(cancellationToken);
            return base.StopAsync(cancellationToken);
        }

        public Task StopConsumer(CancellationToken cancellationToken)
        {
            foreach (var consumer in _consumers)
            {
                consumer.StopConsumer();
            }
            return Task.CompletedTask;
        }
    }
}
