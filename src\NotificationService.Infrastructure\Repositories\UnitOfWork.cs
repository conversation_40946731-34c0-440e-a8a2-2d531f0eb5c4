﻿using NotificationService.Core.Entities;
using NotificationService.Core.Repositories;
using NotificationService.Infrastructure.Repositories;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace NotificationService.Infrastructure.Repositories
{
    public class UnitOfWork : IUnitOfWork
    {
        private NotificationDbContext context;
        //private DbSet entity;

        public UnitOfWork(NotificationDbContext _context)
        {
            context = _context;
        }

        public void Add<T>(T entity) where T : EntityBase
        {
            context.Set<T>().Add(entity);
        }

        public void AddAll<T>(IEnumerable<T> entities) where T : EntityBase
        {
            context.Set<T>().AddRange(entities);
        }

        public IRepository<T> GetRepository<T>() where T : EntityBase
        {
            return new BaseRepository<T>(context);
        }

        public void Save()
        {
            context.SaveChanges();
        }

        public void Update<T>(T entity) where T : EntityBase
        {
            context.Set<T>().Update(entity);
            //throw new NotImplementedException();
        }

        public void UpdateAll<T>(IEnumerable<T> entities) where T : EntityBase
        {
            context.Set<T>().UpdateRange(entities);
            //throw new NotImplementedException();
        }

        public void Dispose()
        {
            context.Dispose();
        }

        public async Task AddAsync<T>(T entity) where T : EntityBase
        {
            await context.Set<T>().AddAsync(entity);
        }

        public async Task AddAllAsync<T>(IEnumerable<T> entities) where T : EntityBase
        {
            await context.Set<T>().AddRangeAsync(entities);
        }

        //public Task UpdateAsync<T>(T entity) where T : EntityBase
        //{
        //    context.Set<T>().upd(entity);
        //}

        //public Task UpdateAllAsync<T>(IEnumerable<T> entities) where T : EntityBase
        //{
        //    context.Set<T>().(entities);
        //}

        public async Task SaveAsync()
        {
            await context.SaveChangesAsync();
        }
    }
}

