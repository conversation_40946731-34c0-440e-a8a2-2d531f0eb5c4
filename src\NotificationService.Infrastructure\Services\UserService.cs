﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using NotificationService.Core.Constants;
using NotificationService.Core.Entities;
using NotificationService.Core.Exceptions;
using NotificationService.Core.Models;
using NotificationService.Core.Repositories;
using NotificationService.Core.Services;
using RestSharp;
using System.Net;

namespace NotificationService.Infrastructure.Services
{
    public class UserService : IUserService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly RestClient _restClient;
        private readonly ILogger<UserService> _logger;

        string baseUrl = string.Empty;
        string subdomain = string.Empty;

        public UserService(IUnitOfWork unitOfWork, IConfiguration configuration, ILogger<UserService> logger)
        {
            _unitOfWork = unitOfWork;
            _logger = logger;
            baseUrl = configuration["Endpoints:JobIdApi"];
            subdomain = configuration["Endpoints:subdomain"];
            _restClient = new RestClient(baseUrl);
        }

        public async Task CreatePushToken(PushTokenModel model)
        {
            try
            {
                if (model.Platform == Platform.IOS)
                {
                    if (model.IOSPushType == null)
                        throw new ApiException((int)HttpStatusCode.BadRequest, "IOSPushType is required for iOS platform");
                }
                else
                {
                    model.IOSPushType = null; // Clear IOSPushType for non-iOS platforms
                }

                if (string.IsNullOrEmpty(model.userId))
                {
                    _logger.LogError("UserId is required for push notifications");
                    throw new ApiException((int)HttpStatusCode.BadRequest, "UserId is required for push notifications");
                }

                var existingUser = await _unitOfWork.GetRepository<User>().SingleOrDefaultAsync(u => u.UserId == model.userId);
                if (existingUser == null)
                {
                    var request = new RestRequest($"getuserbyid/{model.userId}", Method.Get);

                    subdomain = model.Subdomain ?? subdomain;
                    request.AddHeader("subdomain", subdomain);

                    var response = await _restClient.ExecuteAsync<ApiResponse<object>>(request);
                    if (response.StatusCode != HttpStatusCode.OK)
                    {
                        throw new ApiException((int)response.StatusCode, response.Data?.Message ?? "User Id not found");
                    }

                    var userDto = JsonConvert.DeserializeObject<UserDto>(response.Content)!;

                    var newUser = new User
                    {
                        UserId = model.userId,
                        Email = userDto.Email,
                        CompanyEmail = userDto.Email,
                        Country = userDto.Country,
                        FirstName = userDto.FirstName,
                        LastName = userDto.LastName,
                        PhoneNumber = userDto.PhoneNumber,
                        ZipCode = userDto.ZipCode,
                        TimeZone = userDto.TimeZone,
                        Username = userDto.UserName
                    };

                    await _unitOfWork.GetRepository<User>().AddAsync(newUser);
                }

                var existingToken = await _unitOfWork.GetRepository<PushToken>().SingleOrDefaultAsync(pt => pt.UserId == model.userId && pt.AppName == model.Application.ToString() && pt.Platform == model.Platform && pt.IOSPushType == model.IOSPushType);
                if (existingToken != null)
                {
                    if (model.IOSPushType.HasValue && existingToken.IOSPushType != model.IOSPushType)
                    {
                        existingToken.IOSPushType = model.IOSPushType;
                    }

                    existingToken.Token = model.Token;
                    existingToken.UpdatedOn = DateTime.UtcNow;
                    existingToken.Platform = model.Platform;
                    await _unitOfWork.GetRepository<PushToken>().UpdateAsync(existingToken);
                }
                else
                {
                    await _unitOfWork.GetRepository<PushToken>().AddAsync(new PushToken()
                    {
                        AppName = model.Application.ToString(),
                        Token = model.Token,
                        UserId = model.userId,
                        Platform = model.Platform,
                        IOSPushType = model.IOSPushType,
                        CreatedOn = DateTime.UtcNow,
                        UpdatedOn = DateTime.UtcNow
                    });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while validating user ID {UserId}", model.userId);
                throw;
            }
        }

        public async Task CreateUser(CreateUserModel model)
        {
            var user = new User()
            {
                UserId = model.UserId,
                Email = model.Email,
                CompanyEmail = model.CompanyEmail,
                Country = model.Country,
                FirstName = model.FirstName,
                LastName = model.LastName,
                PhoneNumber = model.PhoneNumber,
                ZipCode = model.ZipCode,
                TimeZone = model.TimeZone,
                Username = model.Username,
            };

            await _unitOfWork.GetRepository<User>().AddAsync(user);
        }

        public User? GetUserById(string userId)
        {
            var repository = _unitOfWork.GetRepository<User>();
            var user = repository.Get(u => u.UserId == userId).FirstOrDefault();
            return user;
        }

        public PushToken? GetUserPushTokenById(string userId)
        {
            var repository = _unitOfWork.GetRepository<PushToken>();
            var token = repository.Get(u => u.UserId == userId).FirstOrDefault();
            return token;
        }
    }
}
