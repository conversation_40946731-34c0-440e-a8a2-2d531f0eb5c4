﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Internal;
using NotificationService.Core.Constants;
using System.ComponentModel.DataAnnotations.Schema;

namespace NotificationService.Core.Entities
{
    [Index(nameof(UserId), IsUnique = false)]
    [Index(nameof(MessageId), IsUnique = false)]
    public class Notification : EntityBase
    {
        public string? MessageId { get; set; }

        // This body is for sms and push notifications
        public string? Body { get; set; }
        public string? UserId { get; set; }

        // For email notifications
        public string? Subject { get; set; }
        public string? Email { get; set; }
        public string? PhoneNumber { get; set; }
        public string? PushToken { get; set; }


        [Column(TypeName = "text")]
        public NotificationType NotificationType { get; set; }


        [Column(TypeName = "text")]
        public NotificationPriority Priority { get; set; }

        // For email notifications
        public string? Template { get; set; }

        // For sms outbound messages
        public string? MessageSid { get; set; }


        [Column(TypeName = "text")]
        public NotificationStatus Status { get; set; }

        public string? ResponseId { get; set; }
        public string? StatusCode { get; set; }
        public string? StatusMessage { get; set; }
    }
}
