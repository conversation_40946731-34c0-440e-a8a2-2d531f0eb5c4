﻿<Project Sdk="Microsoft.NET.Sdk.Web">

	<PropertyGroup>
		<TargetFramework>net8.0</TargetFramework>
		<Nullable>enable</Nullable>
		<ImplicitUsings>enable</ImplicitUsings>
		<UserSecretsId>66a48eed-80c2-4190-87bf-224d0be936ad</UserSecretsId>
		<DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
		<PreserveCompilationContext>true</PreserveCompilationContext>
	</PropertyGroup>

	<PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
		<NoWarn>1701;1702,8604</NoWarn>
	</PropertyGroup>

	<PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
		<NoWarn>1701;1702,8604</NoWarn>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="7.0.15" />
		<PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="7.0.0" />
		<PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="9.0.2">
			<PrivateAssets>all</PrivateAssets>
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		</PackageReference>
		<PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.17.0" />
		<PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL" Version="9.0.4" />
		<PackageReference Include="Swashbuckle.AspNetCore" Version="6.4.0" />
		<PackageReference Include="MassTransit" Version="8.3.4" />
		<PackageReference Include="MassTransit.RabbitMQ" Version="8.2.5" />
		<PackageReference Include="AutoMapper" Version="13.0.1" />
    <PackageReference Include="RestSharp" Version="110.2.0" />
	</ItemGroup>

	<ItemGroup>
		<Folder Include="Filters\" />
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\NotificationService.Infrastructure\NotificationService.Infrastructure.csproj" />
	</ItemGroup>

</Project>
