﻿using Microsoft.AspNetCore.Diagnostics;
using Newtonsoft.Json;
using NotificationService.Core.Constants;
using NotificationService.Core.Exceptions;
using NotificationService.Core.Models;
using System.Net;
using WatchDog;

namespace NotificationService.API.Middlewares
{
    public static class ExceptionMiddleware
    {
        public static void ConfigureExceptionHandler(this IApplicationBuilder app, ILoggerFactory logger)
        {
            app.UseExceptionHandler(appError =>
            {
                appError.Run(async context =>
                {
                    context.Response.StatusCode = (int)HttpStatusCode.InternalServerError;
                    context.Response.ContentType = "application/json";

                    //context.Request.

                    var contextFeature = context.Features.Get<IExceptionHandlerFeature>();
                    if (contextFeature != null)
                    {
                        var log = logger.CreateLogger("ConfigureExceptionHandler");
                        Exception exception = contextFeature.Error;
                        int code = (int)ApiStatus.SystemError;
                        string message = "Error processing request";
                        int httpStatus = 500;

                        if (exception is ApiException)
                        {
                            var ex = (ApiException)exception;
                            code = ex.Code;
                            message = ex.Message;

                            switch (code)
                            {
                                case (int)ApiStatus.ValidationError:
                                case (int)ApiStatus.NoResult:
                                    httpStatus = 400;
                                    break;
                                case (int)ApiStatus.NotFound:
                                    httpStatus = 404;
                                    break;
                                case (int)ApiStatus.InvalidOperation:
                                    httpStatus = 400;
                                    break;
                                default: httpStatus = 500;
                                    break;

                            }
                        }

                        log.LogError(exception, exception.Message, "");
                        WatchLogger.LogError(exception.ToString(), exception.Message, exception.TargetSite?.DeclaringType?.FullName);

                        var responseString = JsonConvert.SerializeObject(new BaseApiResponse()
                        {
                            Status = code,
                            Message = exception.Message,
                            DevMessage = exception.ToString()
                        });

                        context.Response.StatusCode = httpStatus;
                        await context.Response.WriteAsync(responseString);
                    }
                });
            });
        }
    }
}

