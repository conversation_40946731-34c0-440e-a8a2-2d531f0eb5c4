﻿using NotificationService.Core;
using System.Security.Claims;

namespace NotificationService.API.Middlewares
{
    public class UserContextMiddleware
    {
        private readonly RequestDelegate _next;

        public UserContextMiddleware(RequestDelegate next)
        {
            _next = next;
        }

        public Task Invoke(HttpContext httpContext)
        {
            bool? isAuthenticated = httpContext.User.Identity?.IsAuthenticated;
            if (isAuthenticated.HasValue && isAuthenticated.Value)
            {
                Console.WriteLine(httpContext.User);
                UserContext.Current = new UserContext(httpContext.User);
            }
            return _next(httpContext);
        }

    }

    // Extension method used to add the middleware to the HTTP request pipeline.
    public static class UserContextMiddlewareExtension
    {
        public static IApplicationBuilder UseUserContextMiddleware(this IApplicationBuilder builder)
        {
            return builder.UseMiddleware<UserContextMiddleware>();
        }
    }
}
