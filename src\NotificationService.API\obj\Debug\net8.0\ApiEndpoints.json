[{"ContainingType": "NotificationService.API.Controllers.HealthCheckController", "Method": "GetEnvironmentVariables", "RelativePath": "api/HealthCheck/get-all-env-variables", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "NotificationService.Core.Models.ViewEnvVariablesDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "NotificationService.API.Controllers.NotificationController", "Method": "Queue", "RelativePath": "api/Notification/queue", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "message", "Type": "NotificationService.Core.Models.NotificationMessageModel", "IsRequired": true}], "ReturnTypes": [{"Type": "NotificationService.Core.Models.ApiResponse`1[[System.String, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "NotificationService.API.Controllers.NotificationController", "Method": "Send", "RelativePath": "api/Notification/send", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "message", "Type": "NotificationService.Core.Models.NotificationMessageModel", "IsRequired": true}], "ReturnTypes": [{"Type": "NotificationService.Core.Models.ApiResponse`1[[System.String, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "NotificationService.API.Controllers.TestController", "Method": "CreateUser", "RelativePath": "api/Test/create", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "model", "Type": "NotificationService.Core.Models.CreateUserModel", "IsRequired": true}], "ReturnTypes": [{"Type": "NotificationService.Core.Models.ApiResponse`1[[System.String, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "NotificationService.API.Controllers.UserController", "Method": "PushToken", "RelativePath": "api/User/create-push-token", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "model", "Type": "NotificationService.Core.Models.PushTokenModel", "IsRequired": true}], "ReturnTypes": [{"Type": "NotificationService.Core.Models.ApiResponse`1[[System.String, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}]