using FirebaseAdmin;
using FirebaseAdmin.Messaging;
using Google.Apis.Auth.OAuth2;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using NotificationService.Core.Models;
using NotificationService.Core.Services;
using NotificationService.Core.Constants;

namespace NotificationService.Infrastructure.Services
{
    public class FirebasePushNotificationService : IPushMessageService
    {
        private readonly string? _googleAuthPath;
        private readonly string? _appLogo;
        private readonly string? _vapidKey;
        private readonly string? _projectId;
        private readonly string? _apnsVoipTopic;
        private readonly ILogger<FirebasePushNotificationService> _logger;
        private readonly IConfiguration _configuration;
        private static FirebaseApp? _firebaseApp;
        private static readonly object _lockObject = new object();

        public FirebasePushNotificationService(
            IConfiguration configuration,
            ILogger<FirebasePushNotificationService> logger)
        {
            _configuration = configuration;
            _logger = logger;
            _googleAuthPath = Path.Combine(Directory.GetCurrentDirectory(), "Secrets", "jobpro-private-key.json");
            _appLogo = configuration["FCM:AppLogo"];
            _vapidKey = Environment.GetEnvironmentVariable("VapidKey") ?? configuration["FCM:VapidKey"];
            _projectId = configuration["FCM:ProjectId"] ?? "jobpro-dev"; // Default from existing config
            _apnsVoipTopic = configuration["FCM:ApnsVoipTopic"] ?? "com.jobpro.joble.voip";

            InitializeFirebaseApp();
        }

        private void InitializeFirebaseApp()
        {
            if (_firebaseApp == null)
            {
                lock (_lockObject)
                {
                    if (_firebaseApp == null)
                    {
                        try
                        {
                            _logger.LogInformation("Initializing Firebase Admin SDK with ProjectId: {ProjectId}", _projectId);
                            _logger.LogInformation("Service account file path: {FilePath}", _googleAuthPath);

                            if (File.Exists(_googleAuthPath))
                            {
                                _logger.LogInformation("Service account file found, reading credentials...");
                                var credential = GoogleCredential.FromFile(_googleAuthPath);

                                _logger.LogInformation("Creating Firebase app with ProjectId: {ProjectId}", _projectId);
                                _firebaseApp = FirebaseApp.Create(new AppOptions()
                                {
                                    Credential = credential,
                                    ProjectId = _projectId
                                });
                                _logger.LogInformation("Firebase Admin SDK initialized successfully for project: {ProjectId}", _projectId);
                            }
                            else
                            {
                                _logger.LogError("Firebase service account file not found at: {FilePath}", _googleAuthPath);
                            }
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(ex, "Error initializing Firebase Admin SDK for project: {ProjectId}", _projectId);
                        }
                    }
                }
            }
        }

        public async Task<MessageResponseModel> SendMessage(NotificationMessageModel model)
        {
            if (_firebaseApp == null)
            {
                return new MessageResponseModel
                {
                    StatusCode = "500",
                    StatusMessage = "Firebase Admin SDK not initialized",
                    Status = NotificationStatus.Failed
                };
            }

            try
            {
                var responseIds = new List<string>();
                foreach (var token in model.PushTokens)
                {
                    _logger.LogInformation($"Push token for user {model.UserId} and app {model.Application}: {token?.Token}");

                    model.PushToken = token.Token;
                    var message = CreateFirebaseMessage(model);
                    var messaging = FirebaseMessaging.GetMessaging(_firebaseApp);

                    var response = await messaging.SendAsync(message);
                    responseIds.Add(response);

                    _logger.LogInformation($"Firebase message sent successfully. Message ID: {response}");
                }

                return new MessageResponseModel
                {
                    ResponseId = responseIds.FirstOrDefault(),
                    StatusCode = "200",
                    StatusMessage = "Message sent successfully",
                    Status = NotificationStatus.Delivered
                };
            }
            catch (FirebaseMessagingException ex)
            {
                _logger.LogError(ex, "Firebase messaging error: {ErrorCode} - {ErrorMessage}", ex.ErrorCode, ex.Message);

                // Provide specific guidance for SenderId mismatch
                if (ex.Message.Contains("SenderId mismatch"))
                {
                    _logger.LogError("SenderId mismatch detected. Please verify:");
                    _logger.LogError("1. ProjectId in configuration matches the Firebase project: {ProjectId}", _projectId);
                    _logger.LogError("2. Service account JSON file is from the correct Firebase project");
                    _logger.LogError("3. Service account file path: {FilePath}", _googleAuthPath);
                }

                return new MessageResponseModel
                {
                    StatusCode = ex.ErrorCode.ToString(),
                    StatusMessage = ex.Message,
                    Status = NotificationStatus.Failed
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending Firebase notification");
                return new MessageResponseModel
                {
                    StatusCode = "500",
                    StatusMessage = ex.Message,
                    Status = NotificationStatus.Failed
                };
            }
        }

        private FirebaseAdmin.Messaging.Message CreateFirebaseMessage(NotificationMessageModel model)
        {
            var messageBuilder = new FirebaseAdmin.Messaging.Message();

            // Set the token (device token or topic)
            if (model.PushToken?.StartsWith("/topics/") == true)
            {
                messageBuilder.Topic = model.PushToken.Substring(8); // Remove "/topics/" prefix
            }
            else
            {
                messageBuilder.Token = model.PushToken;
            }

            // Check if this is a VoIP push notification
            bool isVoipPush = model.Platform == Platform.IOS &&
                             model.IOSPushType == IOSPushType.Voip &&
                             model.NotificationKey == NotificationKey.Call;

            if (!isVoipPush)
            {
                // Create notification payload for regular pushes only
                messageBuilder.Notification = new Notification
                {
                    Title = model.Subject,
                    Body = model.Body,
                    ImageUrl = _appLogo
                };
            }

            // Add custom data if available
            if (model.TemplateWithPlaceHolders?.Props != null)
            {
                messageBuilder.Data = model.TemplateWithPlaceHolders.Props;
            }

            // Check if the token is a web push subscription and configure accordingly
            if (model.Platform == Platform.Web)
            {
                messageBuilder.Webpush = new WebpushConfig
                {
                    Notification = new WebpushNotification
                    {
                        Title = model.Subject,
                        Body = model.Body,
                        Icon = _appLogo
                    },
                    Headers = new Dictionary<string, string>
                    {
                        ["Urgency"] = "high",
                        ["TTL"] = "4500"
                    }
                };

                // Add click action if available
                var clickAction = model.TemplateWithPlaceHolders?.Props?.GetValueOrDefault("click_action");
                if (!string.IsNullOrEmpty(clickAction))
                {
                    messageBuilder.Webpush.FcmOptions = new WebpushFcmOptions
                    {
                        Link = clickAction
                    };
                }
            }
            else
            {
                if (model.Platform == Platform.IOS && model.IOSPushType == IOSPushType.Alert)
                {
                    messageBuilder.Apns = new ApnsConfig
                    {
                        Headers = new Dictionary<string, string>
                        {
                            ["apns-priority"] = "10", // Make sure it's treated as a high-priority push
                            ["apns-push-type"] = "alert" // Required for iOS 13+
                        },
                        Aps = new Aps
                        {
                            Alert = new ApsAlert
                            {
                                Title = model.Subject,
                                Body = model.Body,
                                LaunchImage = _appLogo
                            },
                            Badge = 1,
                            Sound = "default"
                        }
                    };
                }
                else if (model.Platform == Platform.IOS && model.IOSPushType == IOSPushType.Voip && model.NotificationKey != null && model.NotificationKey == NotificationKey.Call)
                {
                    messageBuilder = new FirebaseAdmin.Messaging.Message
                    {
                        Token = model.PushToken, // This should be the raw APNs token from PKPushRegistry
                        Apns = new ApnsConfig
                        {
                            Headers = new Dictionary<string, string>
                            {
                                ["apns-priority"] = "10",
                                ["apns-push-type"] = "voip",
                                ["apns-topic"] = _apnsVoipTopic ?? "com.jobpro.joble.voip"
                            },
                            Aps = new Aps
                            {
                                ContentAvailable = true
                            }
                        },
                        Data = model.TemplateWithPlaceHolders?.Props // custom call data
                    };
                }
                else
                {
                    // Configure for mobile platforms (Android/iOS)
                    messageBuilder.Android = new AndroidConfig
                    {
                        Priority = Priority.High,
                        Notification = new AndroidNotification
                        {
                            Title = model.Subject,
                            Body = model.Body,
                            Icon = "ic_small_icon",
                            // Color = "#FF6600",
                            ChannelId = "default",
                            Priority = FirebaseAdmin.Messaging.NotificationPriority.HIGH,
                        }
                    };
                }
            }

            return messageBuilder;
        }

        private bool IsWebPushToken(string token)
        {
            // Web push tokens typically start with 'ep' (endpoint) or contain specific patterns
            return !string.IsNullOrEmpty(token) && (
                token.StartsWith("ep") ||
                token.Contains("googleapis.com/gcm/send") ||
                token.Contains("fcm.googleapis.com")
            );
        }

        public async Task<MessageResponseModel> SendMessageToTopic(string topic, string title, string body, Dictionary<string, string>? dataObject = null)
        {
            if (_firebaseApp == null)
            {
                return new MessageResponseModel
                {
                    StatusCode = "500",
                    StatusMessage = "Firebase Admin SDK not initialized",
                    Status = NotificationStatus.Failed
                };
            }

            try
            {
                var message = new FirebaseAdmin.Messaging.Message
                {
                    Topic = topic,
                    Notification = new Notification
                    {
                        Title = title,
                        Body = body,
                        ImageUrl = _appLogo
                    },
                    Data = dataObject
                };

                var messaging = FirebaseMessaging.GetMessaging(_firebaseApp);
                var response = await messaging.SendAsync(message);

                _logger.LogInformation($"Firebase topic message sent successfully. Message ID: {response}");

                return new MessageResponseModel
                {
                    ResponseId = response,
                    StatusCode = "200",
                    StatusMessage = "Topic message sent successfully",
                    Status = NotificationStatus.Delivered
                };
            }
            catch (FirebaseMessagingException ex)
            {
                _logger.LogError(ex, "Firebase messaging error for topic {Topic}: {ErrorCode} - {ErrorMessage}", topic, ex.ErrorCode, ex.Message);
                return new MessageResponseModel
                {
                    StatusCode = ex.ErrorCode.ToString(),
                    StatusMessage = ex.Message,
                    Status = NotificationStatus.Failed
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending Firebase topic notification to {Topic}", topic);
                return new MessageResponseModel
                {
                    StatusCode = "500",
                    StatusMessage = ex.Message,
                    Status = NotificationStatus.Failed
                };
            }
        }
    }

}