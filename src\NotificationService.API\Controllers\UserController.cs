﻿using Microsoft.AspNetCore.Mvc;
using NotificationService.Core.Models;
using NotificationService.Core.Services;

namespace NotificationService.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class UserController : ControllerBase
    {
        private readonly IUserService _userService;

        public UserController(IUserService userService)
        {
            _userService = userService; 
        }


        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<string>))]
        [HttpPost("create-push-token")]
        public async Task<IActionResult> PushToken([FromBody] PushTokenModel model)
        {
            await _userService.CreatePushToken(model);
            return Ok(new 
            { 
                Message = "Sucessfully created push token for user.",
                Data = model,
                Error = false
            });

        }
    }
}