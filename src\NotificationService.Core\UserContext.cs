﻿using System.Security.Claims;

namespace NotificationService.Core
{
    public class UserContext
    {
        ClaimsPrincipal principal;

        public UserContext(ClaimsPrincipal _claimsPrincipal)
        {
            principal = _claimsPrincipal;
        }

        //private void getContext()
        //{
        //    //private readonly IHttpContextAccessor _httpContextAccessor;
        //    //IConfigurationRoot Configuration;
        //    public HttpContext Current => new HttpContextAccessor().HttpContext;
        //}

        public static UserContext Current { get; set; }

        public string? Username
        {
            get
            {
                string? username = string.Empty;
                //var principal = ClaimsPrincipal.Current;
                if (principal != null)
                {
                    var usernameClaim = principal.Claims.FirstOrDefault(c => c.Type == ClaimTypes.Name);
                    username = usernameClaim?.Value;
                }
                return username;
            }
        }

        public string? UserId
        {
            get
            {
                string? userid = "";
                if (principal != null)
                {
                    var usernameClaim = principal.Claims.FirstOrDefault(c => c.Type == "UserId");
                    userid = usernameClaim?.Value;
                }
                return userid;
            }
        }

        public string? CustomerType
        {
            get
            {
                string? customerType = string.Empty;
                ///var principal = ClaimsPrincipal.Current;
                if (principal != null)
                {
                    var usernameClaim = principal.Claims.FirstOrDefault(c => c.Type == "CustomerType");
                    customerType = usernameClaim?.Value;
                }
                return customerType;
            }
        }

        public string? Email
        {
            get
            {
                string? userId = string.Empty;
                ///var principal = ClaimsPrincipal.Current;
                if (principal != null)
                {
                    var usernameClaim = principal.Claims.FirstOrDefault(c => c.Type == ClaimTypes.Email);
                    userId = usernameClaim?.Value;
                }
                return userId;
            }
        }

        public string? PhoneNumber
        {
            get
            {
                string? userId = string.Empty;
                ///var principal = ClaimsPrincipal.Current;
                if (principal != null)
                {
                    var usernameClaim = principal.Claims.FirstOrDefault(c => c.Type == ClaimTypes.MobilePhone);
                    userId = usernameClaim?.Value;
                }
                return userId;
            }
        }

        public string? Role
        {
            get
            {
                string? role = string.Empty;
                ///var principal = ClaimsPrincipal.Current;
                if (principal != null)
                {
                    var claim = principal.Claims.FirstOrDefault(c => c.Type == ClaimTypes.Role);
                    role = claim?.Value;
                }
                return role;
            }
        }
    }
}
