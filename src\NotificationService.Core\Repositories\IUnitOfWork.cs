﻿using NotificationService.Core.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace NotificationService.Core.Repositories
{
    public interface IUnitOfWork
    {
        IRepository<T> GetRepository<T>() where T : EntityBase;
        void Add<T>(T entity) where T : EntityBase;
        Task AddAsync<T>(T entity) where T : EntityBase;
        void AddAll<T>(IEnumerable<T> entities) where T : EntityBase;
        Task AddAllAsync<T>(IEnumerable<T> entities) where T : EntityBase;
        void Update<T>(T entity) where T : EntityBase;
        //Task UpdateAsync<T>(T entity) where T : EntityBase;
        void UpdateAll<T>(IEnumerable<T> entities) where T : EntityBase;
        //Task UpdateAllAsync<T>(IEnumerable<T> entities) where T : EntityBase;
        void Save();
        Task SaveAsync();
    }
}

