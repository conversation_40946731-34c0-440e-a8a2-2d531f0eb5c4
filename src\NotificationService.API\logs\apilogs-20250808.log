
13:23:30 [Information] :: Executed DbCommand ("27"ms) [Parameters=[""], CommandType='Text', CommandTimeout='30']"
""SELECT \"MigrationId\", \"ProductVersion\"
FROM \"__EFMigrationsHistory\"
ORDER BY \"MigrationId\";"

13:23:32 [Information] :: Firebase Admin SDK initialized successfully

13:23:32 [Information] :: RabbitMQ Broker Url: amqps://xruwodqv:<EMAIL>/xruwodqv

13:23:32 [Information] :: RabbitMQ Broker Url: amqps://xruwodqv:<EMAIL>/xruwodqv

13:23:32 [Information] :: User profile is available. Using '"C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys"' as key repository and Windows DPAPI to encrypt keys at rest.

13:23:32 [Information] :: Creating new RabbitMQ connection for queue notification-message-queue

13:23:37 [Information] :: RabbitMQ consumer started successfully for queue notification-message-queue

13:23:37 [Information] :: Started consumer: "NotificationMessageConsumer"

13:23:37 [Information] :: Creating new RabbitMQ connection for queue user-created-queue

13:23:41 [Information] :: RabbitMQ consumer started successfully for queue user-created-queue

13:23:41 [Information] :: Started consumer: "UserCreatedMessageConsumer"

13:23:42 [Information] :: Now listening on: "https://localhost:7044"

13:23:42 [Information] :: Now listening on: "http://localhost:5179"

13:23:42 [Information] :: Application started. Press Ctrl+C to shut down.

13:23:42 [Information] :: Hosting environment: "Development"

13:23:42 [Information] :: Content root path: "C:\Users\<USER>\source\repos\Notification-Service\src\NotificationService.API"

13:23:43 [Information] :: Request starting "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/index.html""" - null null

13:23:43 [Information] :: Request finished "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/index.html""" - 200 null "text/html;charset=utf-8" 458.1278ms

13:23:43 [Information] :: Request starting "HTTP/2" "GET" "https"://"localhost:7044""""/_framework/aspnetcore-browser-refresh.js""" - null null

13:23:43 [Information] :: Request starting "HTTP/2" "GET" "https"://"localhost:7044""""/_vs/browserLink""" - null null

13:23:43 [Information] :: Request finished "HTTP/2" "GET" "https"://"localhost:7044""""/_framework/aspnetcore-browser-refresh.js""" - 200 13782 "application/javascript; charset=utf-8" 21.0999ms

13:23:43 [Information] :: Request finished "HTTP/2" "GET" "https"://"localhost:7044""""/_vs/browserLink""" - 200 null "text/javascript; charset=UTF-8" 97.7958ms

13:23:44 [Information] :: Request starting "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/v1/swagger.json""" - null null

13:23:45 [Information] :: Request finished "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/v1/swagger.json""" - 200 null "application/json;charset=utf-8" 489.0607ms

13:24:07 [Information] :: Notification Message "{\"pattern\":\"notification-message-queue\",\"data\":\"{\\"cc\\":[],\\"attachments\\":[],\\"notificationTypes\\":[3],\\"messageId\\":\\"bdac6dab-7702-49ed-840c-8e21920ebd11\\",\\"priority\\":0,\\"isWhatsappNo\\":false,\\"application\\":1,\\"body\\":\\"c964a01d-d0f3-4553-b53d-07e773d66525 is calling in General\\",\\"subject\\":\\"Incoming audio call\\",\\"userId\\":\\"c964a01d-d0f3-4553-b53d-07e773d66525\\",\\"templateWithPlaceHolders\\":{\\"template\\":\\"incoming_call\\",\\"props\\":{\\"callId\\":\\"02jjdajqslds\\",\\"callerId\\":\\"6894d2ed1bc552f27cad33b9\\",\\"callerName\\":\\"c964a01d-d0f3-4553-b53d-07e773d66525\\",\\"chatId\\":\\"6894d2aa1bc552f27cad331d\\",\\"callType\\":\\"audio\\",\\"chatType\\":\\"CIRCLE\\",\\"key\\":\\"call\\",\\"initiateCallPayload\\":\\"{\\\\"event\\\\":\\\\"INITIATE_CALL\\\\",\\\\"data\\\\":{\\\\"callerSocket\\\\":\\\\"yHkUsVFqkRP4yp2bAAAc\\\\",\\\\"callerId\\\\":\\\\"6894d2ed1bc552f27cad33b9\\\\",\\\\"callId\\\\":\\\\"02jjdajqslds\\\\",\\\\"callerJobProUserId\\\\":\\\\"c964a01d-d0f3-4553-b53d-07e773d66525\\\\",\\\\"callerName\\\\":\\\\"Osigwe Chidozie\\\\",\\\\"format\\\\":\\\\"audio\\\\",\\\\"type\\\\":\\\\"dm\\\\",\\\\"meetingId\\\\":\\\\"6894d2aa1bc552f27cad331d\\\\",\\\\"title\\\\":\\\\"Osigwe Chidozie\\\\",\\\\"name\\\\":\\\\"Osigwe Chidozie\\\\",\\\\"profilePictureUrl\\\\":\\\\"https://jobpro-public-dev-eu-central-1-010526262538.s3.eu-central-1.amazonaws.com/BB1CDBE9077B5043C70A98A05EA4DEE162F1-oua7kXE5bDF2Y3x9mh4gEi2dSo.png?X-Amz-Expires=604800&X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=AKIAQE43KAUFBUX3QAH7%2F20250805%2Feu-central-1%2Fs3%2Faws4_request&X-Amz-Date=20250805T102022Z&X-Amz-SignedHeaders=host&X-Amz-Signature=6964630ce1cc19b586eea5cc6d68a53da562bbb3f52aab0893339c05ad779bb2\\\\",\\\\"status\\\\":\\\\"INITIATE_CALL\\\\",\\\\"participants\\\\":[\\\\"de56874f-9080-4a54-88c4-265231fa7629\\\\"]}}\\"}}}\"}"

13:24:13 [Information] :: Executed DbCommand ("13"ms) [Parameters=["@__model_UserId_0='?', @__ToString_1='?'"], CommandType='Text', CommandTimeout='30']"
""SELECT p.\"Id\", p.\"AppName\", p.\"CreatedBy\", p.\"CreatedOn\", p.\"Platform\", p.\"Token\", p.\"UpdatedBy\", p.\"UpdatedOn\", p.\"UserId\"
FROM \"PushTokens\" AS p
WHERE p.\"UserId\" = @__model_UserId_0 AND p.\"AppName\" = @__ToString_1"

13:24:13 [Information] :: Executed DbCommand ("12"ms) [Parameters=["@__ToString_0='?'"], CommandType='Text', CommandTimeout='30']"
""SELECT u.\"Id\", u.\"CompanyEmail\", u.\"Country\", u.\"CreatedBy\", u.\"CreatedOn\", u.\"Email\", u.\"FirstName\", u.\"LastName\", u.\"PhoneNumber\", u.\"TimeZone\", u.\"UpdatedBy\", u.\"UpdatedOn\", u.\"UserId\", u.\"Username\", u.\"ZipCode\"
FROM \"Users\" AS u
WHERE u.\"UserId\" = @__ToString_0
LIMIT 1"

13:24:13 [Information] :: Push token for user c964a01d-d0f3-4553-b53d-07e773d66525 and app Joble: c3AMk9G3pRZcKri84Dj_FC:APA91bGVvdz2xOYvcb3VgqshMyAZOjiugdoB0LStTGLbWTYMdihbfcNt1sTc8uUTyet4z3XLOVNJi9wo2VZ5GHYenFw-ahIemLh9G0NR12Og8OaWNdMIJ4g

13:24:15 [Information] :: Firebase message sent successfully. Message ID: projects/jobpro-prod/messages/0:1754655856562178%dfee03a5dfee03a5

13:24:15 [Information] :: Executed DbCommand ("16"ms) [Parameters=["@p0='?', @p1='?', @p2='?' (DbType = DateTime), @p3='?', @p4='?', @p5='?', @p6='?', @p7='?', @p8='?', @p9='?', @p10='?', @p11='?', @p12='?', @p13='?', @p14='?', @p15='?', @p16='?', @p17='?' (DbType = DateTime), @p18='?'"], CommandType='Text', CommandTimeout='30']"
""INSERT INTO \"Notifications\" (\"Body\", \"CreatedBy\", \"CreatedOn\", \"Email\", \"MessageId\", \"MessageSid\", \"NotificationType\", \"PhoneNumber\", \"Priority\", \"PushToken\", \"ResponseId\", \"Status\", \"StatusCode\", \"StatusMessage\", \"Subject\", \"Template\", \"UpdatedBy\", \"UpdatedOn\", \"UserId\")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16, @p17, @p18)
RETURNING \"Id\";"

13:25:06 [Information] :: notification-message-queue message received and processed: {"pattern":"notification-message-queue","data":"{\"cc\":[],\"attachments\":[],\"notificationTypes\":[3],\"messageId\":\"bdac6dab-7702-49ed-840c-8e21920ebd11\",\"priority\":0,\"isWhatsappNo\":false,\"application\":1,\"body\":\"c964a01d-d0f3-4553-b53d-07e773d66525 is calling in General\",\"subject\":\"Incoming audio call\",\"userId\":\"c964a01d-d0f3-4553-b53d-07e773d66525\",\"templateWithPlaceHolders\":{\"template\":\"incoming_call\",\"props\":{\"callId\":\"02jjdajqslds\",\"callerId\":\"6894d2ed1bc552f27cad33b9\",\"callerName\":\"c964a01d-d0f3-4553-b53d-07e773d66525\",\"chatId\":\"6894d2aa1bc552f27cad331d\",\"callType\":\"audio\",\"chatType\":\"CIRCLE\",\"key\":\"call\",\"initiateCallPayload\":\"{\\\"event\\\":\\\"INITIATE_CALL\\\",\\\"data\\\":{\\\"callerSocket\\\":\\\"yHkUsVFqkRP4yp2bAAAc\\\",\\\"callerId\\\":\\\"6894d2ed1bc552f27cad33b9\\\",\\\"callId\\\":\\\"02jjdajqslds\\\",\\\"callerJobProUserId\\\":\\\"c964a01d-d0f3-4553-b53d-07e773d66525\\\",\\\"callerName\\\":\\\"Osigwe Chidozie\\\",\\\"format\\\":\\\"audio\\\",\\\"type\\\":\\\"dm\\\",\\\"meetingId\\\":\\\"6894d2aa1bc552f27cad331d\\\",\\\"title\\\":\\\"Osigwe Chidozie\\\",\\\"name\\\":\\\"Osigwe Chidozie\\\",\\\"profilePictureUrl\\\":\\\"https://jobpro-public-dev-eu-central-1-010526262538.s3.eu-central-1.amazonaws.com/BB1CDBE9077B5043C70A98A05EA4DEE162F1-oua7kXE5bDF2Y3x9mh4gEi2dSo.png?X-Amz-Expires=604800&X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=AKIAQE43KAUFBUX3QAH7%2F20250805%2Feu-central-1%2Fs3%2Faws4_request&X-Amz-Date=20250805T102022Z&X-Amz-SignedHeaders=host&X-Amz-Signature=6964630ce1cc19b586eea5cc6d68a53da562bbb3f52aab0893339c05ad779bb2\\\",\\\"status\\\":\\\"INITIATE_CALL\\\",\\\"participants\\\":[\\\"de56874f-9080-4a54-88c4-265231fa7629\\\"]}\"}}"}

13:26:34 [Information] :: Notification Message "{\"pattern\":\"notification-message-queue\",\"data\":\"{\\"cc\\":[],\\"attachments\\":[],\\"notificationTypes\\":[3],\\"messageId\\":\\"d42b5d7c-b428-435c-b2ed-1e10e2a41ef4\\",\\"priority\\":0,\\"isWhatsappNo\\":false,\\"application\\":1,\\"body\\":\\"c964a01d-d0f3-4553-b53d-07e773d66525 is calling in General\\",\\"subject\\":\\"Incoming audio call\\",\\"userId\\":\\"c964a01d-d0f3-4553-b53d-07e773d66525\\",\\"templateWithPlaceHolders\\":{\\"template\\":\\"incoming_call\\",\\"props\\":{\\"callId\\":\\"02jjdajqslds\\",\\"callerId\\":\\"6894d2ed1bc552f27cad33b9\\",\\"callerName\\":\\"c964a01d-d0f3-4553-b53d-07e773d66525\\",\\"chatId\\":\\"6894d2aa1bc552f27cad331d\\",\\"callType\\":\\"audio\\",\\"chatType\\":\\"CIRCLE\\",\\"key\\":\\"call\\",\\"initiateCallPayload\\":\\"{\\\\"event\\\\":\\\\"INITIATE_CALL\\\\",\\\\"data\\\\":{\\\\"callerSocket\\\\":\\\\"yHkUsVFqkRP4yp2bAAAc\\\\",\\\\"callerId\\\\":\\\\"6894d2ed1bc552f27cad33b9\\\\",\\\\"callId\\\\":\\\\"02jjdajqslds\\\\",\\\\"callerJobProUserId\\\\":\\\\"c964a01d-d0f3-4553-b53d-07e773d66525\\\\",\\\\"callerName\\\\":\\\\"Osigwe Chidozie\\\\",\\\\"format\\\\":\\\\"audio\\\\",\\\\"type\\\\":\\\\"dm\\\\",\\\\"meetingId\\\\":\\\\"6894d2aa1bc552f27cad331d\\\\",\\\\"title\\\\":\\\\"Osigwe Chidozie\\\\",\\\\"name\\\\":\\\\"Osigwe Chidozie\\\\",\\\\"profilePictureUrl\\\\":\\\\"https://jobpro-public-dev-eu-central-1-010526262538.s3.eu-central-1.amazonaws.com/BB1CDBE9077B5043C70A98A05EA4DEE162F1-oua7kXE5bDF2Y3x9mh4gEi2dSo.png?X-Amz-Expires=604800&X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=AKIAQE43KAUFBUX3QAH7%2F20250805%2Feu-central-1%2Fs3%2Faws4_request&X-Amz-Date=20250805T102022Z&X-Amz-SignedHeaders=host&X-Amz-Signature=6964630ce1cc19b586eea5cc6d68a53da562bbb3f52aab0893339c05ad779bb2\\\\",\\\\"status\\\\":\\\\"INITIATE_CALL\\\\",\\\\"participants\\\\":[\\\\"de56874f-9080-4a54-88c4-265231fa7629\\\\"]}}\\"}}}\"}"

13:26:34 [Information] :: Executed DbCommand ("1"ms) [Parameters=["@__model_UserId_0='?', @__ToString_1='?'"], CommandType='Text', CommandTimeout='30']"
""SELECT p.\"Id\", p.\"AppName\", p.\"CreatedBy\", p.\"CreatedOn\", p.\"Platform\", p.\"Token\", p.\"UpdatedBy\", p.\"UpdatedOn\", p.\"UserId\"
FROM \"PushTokens\" AS p
WHERE p.\"UserId\" = @__model_UserId_0 AND p.\"AppName\" = @__ToString_1"

13:26:34 [Information] :: Executed DbCommand ("2"ms) [Parameters=["@__ToString_0='?'"], CommandType='Text', CommandTimeout='30']"
""SELECT u.\"Id\", u.\"CompanyEmail\", u.\"Country\", u.\"CreatedBy\", u.\"CreatedOn\", u.\"Email\", u.\"FirstName\", u.\"LastName\", u.\"PhoneNumber\", u.\"TimeZone\", u.\"UpdatedBy\", u.\"UpdatedOn\", u.\"UserId\", u.\"Username\", u.\"ZipCode\"
FROM \"Users\" AS u
WHERE u.\"UserId\" = @__ToString_0
LIMIT 1"

13:26:45 [Information] :: Push token for user c964a01d-d0f3-4553-b53d-07e773d66525 and app Joble: c3AMk9G3pRZcKri84Dj_FC:APA91bGVvdz2xOYvcb3VgqshMyAZOjiugdoB0LStTGLbWTYMdihbfcNt1sTc8uUTyet4z3XLOVNJi9wo2VZ5GHYenFw-ahIemLh9G0NR12Og8OaWNdMIJ4g
