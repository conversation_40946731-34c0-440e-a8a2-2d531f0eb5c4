{
  "ConnectionStrings": {
    //"NotificationConnection": "Data Source=DESKTOP-EONO2PO\\SQLEXPRESS;Initial Catalog=Notification_Service;Integrated Security=True;TrustServerCertificate=True",
    "NotificationConnection": "Data Source=*************;Database=Notification_service_qa;User Id=test;Password=***********************;MultipleActiveResultSets=false;Integrated Security=false;TrustServerCertificate=true"
  },

  "CorsOrigins": [ "http://localhost:4200" ],
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning"
    }
  },

  "JwtSettings": {
    "validAudience": "http://localhost",
    "securityKey": "staging!###!!!!=--=-90-&%^][ghg0gvtftf1!!@@%556872%$#$#$%@$}^^^^secretkey!98765",
    "PublicKey": "19df230f6259c9d4cd0b0a24aeb3d40b624f9e3ddf7b70384471c774916a42b9",
    "APIKey": "f268c65e63630f75783482346cb60048e940a0161e33376070d5f641a29b7e8b",
    "SecretKey": "staging!###!!!!=--=-90-&%^][ghg0gvtftf1!!@@%556872%$#$#$%@$}^^^^secretkey!98765",
    "expiryInMinutes": 600,
    "RefreshTokenExpiryInDays": 7,
    "validIssuer": "EfacRuhuna"
  },

  "WatchDogCredentials": {
    "Username": "admin",
    "Password": "admin"
  },

  "AllowedHosts": "*",
  "AMQP": {
    "HostName": "amqps://user:<EMAIL>"
  },

  "LogPath": "./logs/apilogs-.log",
  "Serilog": {
    "Using": [ "Serilog.Sinks.File" ],
    "MinimunLevel": {
      "Default": "Information"
    },
    "WriteTo": [
      {
        "Name": "File",
        "Args": {
          "path": "./logs/api-logs-.log",
          "rollingInterval": "Day"
        }
      }
    ]
  },

  "GCP": {
    "AuthFile": "C:\\applications\\zarttech\\secrets\\google-credentials.json",
    "BucketName": "jobpro-assets"
  },

  "Twilio": {
    "AccountSID": "**********************************",
    "AuthToken": "134e2aa7e61fdde8d4644a2b17a17213"
  },

  "MAILGUN_API_KEY": "**************************************************",
  "MAILGUN_DOMAIN": "jobpro.app",

  "MailgunApiKey": "",
  "MailgunDomain": "jobpro.app",
  "EmailOptions": {
    "Url": "https://api.mailgun.net/v3/",
    "ApiKey": "",
    "FromName": "JobPro",
    "FromEmail": "<EMAIL>",
    "Domain": "jobpro.app",
    "JobleEmail": "<EMAIL>",
    "JobPaysEmail": "<EMAIL>",
    "JobEyesEmail": "<EMAIL>",
    "JobIdEmail": "<EMAIL>",
    "CaringBoss": "<EMAIL>"
  },
  "AWS": {
    "Region": "eu-central-1",
    "Profile": "jobpro-dev"
  },
  "Messaging": {
    "UseAWS": "true" // or "false" for RabbitMQ
  }
}