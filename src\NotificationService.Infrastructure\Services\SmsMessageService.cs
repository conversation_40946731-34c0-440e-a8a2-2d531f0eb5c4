﻿using NotificationService.Core.Models;
using NotificationService.Core.Services;
using Twilio;
using Twilio.Rest.Api.V2010.Account;
using AfricasTalkingCS;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;
using Newtonsoft.Json;
using NotificationService.Core.Constants;

namespace NotificationService.Infrastructure.Services
{
    public class SmsMessageService : ISmsMessageService
    {
        protected readonly ILogger<NotificationMessageConsumer> _logger;
        protected readonly IConfiguration _configuration;
        public SmsMessageService(IConfiguration configuration,ILogger<NotificationMessageConsumer> logger)
        {
            _logger = logger;
            _configuration= configuration;
        }

        #region Send an outbound sms
        public async Task<MessageResponseModel?> SendMessage(NotificationMessageModel model)
        {
            MessageResponseModel? result  =  null;
            result = await SendUsingTwilio(model);

            //if (model.PhoneNumber.StartsWith("+234"))
            //{
            //    result = await SendUsingAfriTalk(model);
            //}
            //else
            //{
            //    result = await SendUsingTwilio(model);
            //}
            return result;
        }
        #endregion

        #region Twilio method to send an outbound sms
        private async Task<MessageResponseModel?> SendUsingTwilio(NotificationMessageModel model)
        {
            try
            {
                var accountSid = Environment.GetEnvironmentVariable("JOBPRO_TWILIO_ACCOUNT_SID") ?? _configuration["Twilio:AccountSID"];
                var authToken = Environment.GetEnvironmentVariable("JOBPRO_TWILIO_AUTH_TOKEN") ?? _configuration["Twilio:AuthToken"];

                TwilioClient.Init(accountSid, authToken);

                MessageResource? message = null;
                model.IsWhatsappNo = false;
                if (model.IsWhatsappNo)
                {
                    message = await MessageResource.CreateAsync(
                        body: model.Body,
                        from: new Twilio.Types.PhoneNumber("whatsapp:+***********"),
                        to: new Twilio.Types.PhoneNumber("whatsapp:" + model.PhoneNumber)
                    );
                }
                else
                {
                    message = await MessageResource.CreateAsync(
                        body: model.Body,
                    from: new Twilio.Types.PhoneNumber("JobPro"),
                        to: new Twilio.Types.PhoneNumber(model.PhoneNumber)
                    );
                }

                _logger.LogInformation("Twillo response" + JsonConvert.SerializeObject(message));
                var response = new MessageResponseModel()
                {
                    Status = message.Status == MessageResource.StatusEnum.Queued ? NotificationStatus.Processing : NotificationStatus.Pending,
                    StatusCode = message.ErrorCode == null ? "200" : message.ErrorCode.Value.ToString(),
                    StatusMessage = message.ErrorMessage
                };

                return response;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, ex.Message);
                return new MessageResponseModel()
                {
                    Status = NotificationStatus.Failed,
                    StatusCode = "500",
                    StatusMessage = ex.Message
                };
            }
        }
        #endregion

        #region Twilio method to check the status of an outbound sms
        public async Task<MessageResource> CheckStatus(string messageId)
        {
            var accountSid = Environment.GetEnvironmentVariable("JOBPRO_TWILIO_ACCOUNT_SID") ?? _configuration["Twilio:AccountSID"];
            var authToken = Environment.GetEnvironmentVariable("JOBPRO_TWILIO_AUTH_TOKEN") ?? _configuration["Twilio:AuthToken"];

            TwilioClient.Init(accountSid, authToken);

            var message = await MessageResource.FetchAsync(
                               pathSid: messageId
                                          );

            return message;
        }
        #endregion

        #region Africa's Talk method to send an outbound sms
        private MessageResponseModel? SendUsingAfriTalk(NotificationMessageModel model)
        {
            MessageResponseModel? result = null;
            var apiKey = Environment.GetEnvironmentVariable("JOBPRO_AFRICA_TALKING_APIKEY") ?? _configuration["AIT:ApiKey"];
            var username = Environment.GetEnvironmentVariable("JOBPRO_AFRICA_TALKING_USERNAME") ?? _configuration["AIT:Username"];
            var gateway = new AfricasTalkingGateway(username, apiKey);

            try
            {
                var sms = gateway.SendMessage(model.PhoneNumber, model.Body, model.Application.ToString());
                var response = (string)JsonConvert.SerializeObject(sms);
                _logger.LogInformation("Africa Talks response" + response);

                foreach (var res in sms["SMSMessageData"]["Recipients"])
                {
                    result = new MessageResponseModel();
                    var status = (string)res["status"];
                    if (status.Equals("Success") && (string)res["statusCode"] == "101")
                    {
                        result.Status = NotificationStatus.Completed;
                    }
                    else
                    {
                        result.Status = NotificationStatus.Failed;
                    }

                    result.ResponseId = (string)res["messageId"];
                    result.StatusCode = (string)res["statusCode"];
                    result.StatusMessage = (string)res["status"];
                }
            }
            catch (AfricasTalkingGatewayException ex)
            {
                _logger.LogError(ex, ex.Message);
                Console.WriteLine(ex);
            }

            return result;
        }
        #endregion
    }
}
