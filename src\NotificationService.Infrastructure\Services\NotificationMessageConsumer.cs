using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using NotificationService.Core.Models;
using NotificationService.Core.Services;
using RabbitMQ.Client;
using WatchDog;

namespace NotificationService.Infrastructure.Services
{
    public class NotificationMessageConsumer : BaseConsumer
    {
        private readonly INotificationService _notificationService;

        public NotificationMessageConsumer(INotificationService notificationService,
            IConfiguration configuration, ILogger<NotificationMessageConsumer> logger) : base(configuration, logger)
        {
            _queue = "notification-message-queue";
            _exchange = "notification-message-event";
            _notificationService = notificationService;
            _exchangeType = ExchangeType.Direct;
        }

        public override async Task ProcessMessage(string message)
        {
            try
            {
                _logger.LogInformation("Notification Message {0}", message);
                WatchLogger.Log("Notification Message", message);

                if (message.Contains("pattern") && message.Contains("data"))
                {
                    var wrapper = JsonConvert.DeserializeObject<NotificationMessageWrapper>(message);
                    var payload = JsonConvert.DeserializeObject<NotificationMessageModel>(wrapper.Data);
                    await _notificationService.ProcessMessage(payload);
                }
                else
                {
                    var payload = JsonConvert.DeserializeObject<NotificationMessageModel>(message);
                    await _notificationService.ProcessMessage(payload);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error processing notification message {ex.Message}", ex);
                WatchLogger.Log($"Error processing notification message - {ex.Message}", ex.ToString(), nameof(NotificationMessageConsumer));
            }
        }
    }
}
