using NotificationService.Core.Services;
using RabbitMQ.Client.Events;
using RabbitMQ.Client;
using System.Text;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using WatchDog;
using RabbitMQ.Client.Exceptions;

namespace NotificationService.Infrastructure.Services
{
    public abstract class BaseConsumer : IAMQConsumer
    {
        protected readonly string _hostName;
        protected string _queue = string.Empty;
        protected string _exchange = string.Empty;
        protected string _exchangeType = string.Empty;
        protected readonly ILogger<NotificationMessageConsumer> _logger;
        private IModel? channel = null;
        private IConnection? connection = null;
        private bool isConsuming = false;

        /// <summary>
        /// 
        /// </summary>
        /// <param name="configuration"></param>
        /// <param name="logger"></param>
        public BaseConsumer(IConfiguration configuration, ILogger<NotificationMessageConsumer> logger)
        {
            _hostName = Environment.GetEnvironmentVariable("JOBPRO_RABBITMQ_BROKER_URL") ?? configuration["AMQP:HostName"] ?? throw new ArgumentNullException("RabbitMQ host configuration is missing");
            _logger = logger;
            _logger.LogInformation($"RabbitMQ Broker Url: {_hostName}");

            // Don't create connection here - let StartConsumer handle it
            // This allows for better error handling and reconnection logic
        }

        #region Start and Stop Consumer
        public async virtual Task StartConsumer()
        {
            // Check if already consuming and connection is healthy
            if (isConsuming && connection != null && connection.IsOpen && channel != null && channel.IsOpen)
            {
                //_logger.LogInformation($"Consumer for queue {_queue} is already running and healthy");
                return;
            }

            // If we reach here, either not consuming or connection is unhealthy
            if (isConsuming)
            {
                _logger.LogWarning($"Consumer for queue {_queue} was marked as consuming but connection is unhealthy. Restarting...");
                StopConsumer();
            }

            try
            {
                // Ensure we have a valid connection
                if (connection == null || !connection.IsOpen)
                {
                    _logger.LogInformation($"Creating new RabbitMQ connection for queue {_queue}");
                    var factory = new ConnectionFactory { Uri = new Uri(_hostName), DispatchConsumersAsync = true };
                    connection = factory.CreateConnection();
                }

                channel = connection.CreateModel();
                if (channel == null)
                {
                    _logger.LogError("Error while creating channel");
                    return;
                }

                var expiration = 360000000; // 100 hours
                var args = new Dictionary<string, object> { { "x-message-ttl", expiration } };

                try
                {
                    channel.QueueDeclare(_queue, exclusive: false, durable: true, autoDelete: false, arguments: args);
                    channel.BasicQos(prefetchSize: 0, prefetchCount: 1, global: false);
                }
                catch (OperationInterruptedException ex)
                {
                    _logger.LogError(ex, "Error while declaring queue");
                    WatchLogger.Log("RabbitMQ: OperationInterruptedException.", ex.Message);
                    return;
                }

                var consumer = new AsyncEventingBasicConsumer(channel);
                consumer.Received += async (model, eventArgs) =>
                {
                    try
                    {
                        var body = eventArgs.Body.ToArray();
                        var message = Encoding.UTF8.GetString(body);
                        await ProcessMessage(message);

                        _logger.LogInformation($"{_queue} message received and processed: {message}");
                        WatchLogger.Log($"{_queue} message received: {message}");

                        channel.BasicAck(deliveryTag: eventArgs.DeliveryTag, multiple: false);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error processing message in queue {Queue}", _queue);
                        // Don't ack the message if processing failed
                        channel.BasicNack(deliveryTag: eventArgs.DeliveryTag, multiple: false, requeue: true);
                    }
                };

                channel.BasicConsume(queue: _queue, autoAck: false, consumer: consumer);
                isConsuming = true;
                _logger.LogInformation($"RabbitMQ consumer started successfully for queue {_queue}");
                await Task.CompletedTask;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error while starting consumer for queue {Queue}", _queue);
                isConsuming = false;
                throw;
            }
        }

        public abstract Task ProcessMessage(string message);

        public void StopConsumer()
        {
            isConsuming = false;

            if (this.channel != null)
            {
                try
                {
                    this.channel.Close();
                    this.channel.Dispose();
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error closing RabbitMQ channel");
                }
                this.channel = null;
            }

            if (this.connection != null)
            {
                try
                {
                    this.connection.Close();
                    this.connection.Dispose();
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error closing RabbitMQ connection");
                }
                this.connection = null;
            }

            _logger.LogInformation($"RabbitMQ consumer stopped for queue {_queue}");
        }
        #endregion
    }
}
