﻿using Newtonsoft.Json.Converters;
using System.Text.Json.Serialization;

namespace NotificationService.Core.Constants
{
    [<PERSON>son<PERSON>onverter(typeof(JsonStringEnumConverter))]
    public enum NotificationType
    {
        Email = 1,
        Sms,
        Push,
        Web
    }

    public enum NotificationStatus
    {
        Pending = 1,
        Processing,
        Completed,
        Delivered,
        Failed
    }

    [JsonConverter(typeof(JsonStringEnumConverter))]
    public enum Application
    {
        Joble = 1,
        JobPays,
        Echo,
        JobEvent,
        JobID,
        CaringBoss,
        Jobfy
    }

    [JsonConverter(typeof(JsonStringEnumConverter))]
    public enum Platform
    {
        Andriod = 1,
        IOS,
        Web
    }

    [<PERSON><PERSON><PERSON>onverter(typeof(JsonStringEnumConverter))]
    public enum IOSPushType
    {
        Alert = 1,
        Voip
    }

    [JsonConverter(typeof(JsonStringEnumConverter))]
    public enum NotificationKey
    {
        Chat = 1,
        Call
    }
}
