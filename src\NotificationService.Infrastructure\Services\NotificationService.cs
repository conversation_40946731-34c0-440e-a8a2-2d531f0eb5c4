﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using NotificationService.Core.Constants;
using NotificationService.Core.Entities;
using NotificationService.Core.Exceptions;
using NotificationService.Core.Models;
using NotificationService.Core.Repositories;
using NotificationService.Core.Services;
using RestSharp;
using System.Net;

namespace NotificationService.Infrastructure.Services
{
    public class NotificationService : INotificationService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IServiceProvider _serviceProvider;
        ILogger<EmailMessageService> _logger;
        private readonly IEnumerable<IMessageProcessorService> _messageProcessors;
        private readonly RestClient _restClient;
        string baseUrl = string.Empty;
        string subdomain = string.Empty;

        public NotificationService(IServiceProvider serviceProvider, IEnumerable<IMessageProcessorService> messageProcessors,
            IConfiguration configuration, ILogger<EmailMessageService> logger)
        {
            _unitOfWork = serviceProvider.CreateScope().ServiceProvider.GetRequiredService<IUnitOfWork>();
            _serviceProvider = serviceProvider;
            _logger = logger;
            _messageProcessors = messageProcessors;
            baseUrl = configuration["Endpoints:JobIdApi"];
            subdomain = configuration["Endpoints:subdomain"];
            _restClient = new RestClient(baseUrl);
        }

        public async Task<bool> ProcessMessage(NotificationMessageModel model)
        {
            if (model == null) return false;

            // If push notification is enabled, ensure the user exists
            if (model.NotificationTypes.Contains(NotificationType.Push) && string.IsNullOrEmpty(model.UserId))
            {
                _logger.LogError("UserId is required for push notifications");
                throw new ApiException((int)HttpStatusCode.BadRequest, "UserId is required for push notifications");
            }

            var request = new RestRequest($"getuserbyid/{model.UserId}", Method.Get);
            if (string.IsNullOrEmpty(model.PushToken))
            {
                var pushTokens = _unitOfWork.GetRepository<PushToken>()
                    .Get(u => u.UserId == model.UserId && u.AppName == model.Application.ToString()).ToList();
                model.PushTokens = pushTokens;
            }

            // Check if subject is a userId(valid guid), if yes, get the name that owns the userid
            if (model.Subject != null && Guid.TryParse(model.Subject, out Guid userId))
            {
                var existingUser = await _unitOfWork.GetRepository<User>().SingleOrDefaultAsync(u => u.UserId == userId.ToString());
                if (existingUser == null)
                {
                    request = new RestRequest($"getuserbyid/{userId}", Method.Get);
                    request.AddHeader("subdomain", "api");

                    var response = await _restClient.ExecuteAsync<ApiResponse<object>>(request);
                    if (response.StatusCode != HttpStatusCode.OK)
                    {
                        model.Subject = "You have a new message";
                    }
                    else
                    {
                        var userDto = JsonConvert.DeserializeObject<UserDto>(response.Content)!;
                        model.Subject = userDto.FirstName + " " + userDto.LastName;
                    }
                }
                else
                {
                    model.Subject = existingUser.FirstName + " " + existingUser.LastName;
                }
            }

            // Check if a sentence conatians a valid guid, if yes, get the name that owns the userid
            // Convert he string to list of words separated by space
            // Then check if any of the words is a valid guid
            if (model.Body != null)
            {
                var words = model.Body.Split(' ');
                foreach (var word in words)
                {
                    if (Guid.TryParse(word, out Guid newUseriD))
                    {
                        var existingUser = await _unitOfWork.GetRepository<User>().SingleOrDefaultAsync(u => u.UserId == newUseriD.ToString());
                        if (existingUser == null)
                        {
                            request = new RestRequest($"getuserbyid/{newUseriD}", Method.Get);
                            request.AddHeader("subdomain", "api");

                            var response = await _restClient.ExecuteAsync<ApiResponse<object>>(request);
                            if (response.StatusCode != HttpStatusCode.OK)
                            {
                                model.Body = "You have a new message";
                            }
                            else
                            {
                                var userDto = JsonConvert.DeserializeObject<UserDto>(response.Content)!;
                                model.Body = model.Body.Replace(word, userDto.FirstName + " " + userDto.LastName);
                            }
                        }
                        else
                        {
                            model.Body = model.Body.Replace(word, existingUser.FirstName + " " + existingUser.LastName);
                        }
                    }
                }
            }

            bool overallSuccess = true;
            var notifications = new List<Notification>();

            foreach (var type in model.NotificationTypes)
            {
                var notification = new Notification();
                try
                {
                    notification.MessageId = model.MessageId;
                    notification.Body = model.Body;
                    notification.NotificationType = type;
                    notification.Template = model.TemplateWithParams;
                    notification.Priority = model.Priority;
                    notification.Email = model.RecipientEmails != null ? string.Join(",", model.RecipientEmails) : null;
                    notification.PhoneNumber = model.PhoneNumber;
                    notification.PushToken = model.PushToken;
                    notification.UserId = model.UserId;
                }
                catch (Exception ex)
                {
                    _logger.LogError($"Error creating notification for type {type}: {ex.Message}", ex);
                    overallSuccess = false;
                    continue; // Skip to the next type
                }

                IMessageProcessorService? processor = type switch
                {
                    NotificationType.Email => _messageProcessors.FirstOrDefault(p => typeof(IEmailMessageService).IsAssignableFrom(p.GetType())),
                    NotificationType.Sms => _messageProcessors.FirstOrDefault(p => typeof(ISmsMessageService).IsAssignableFrom(p.GetType())),
                    NotificationType.Push => _messageProcessors.FirstOrDefault(p => typeof(IPushMessageService).IsAssignableFrom(p.GetType())),
                    _ => null
                };

                if (processor != null)
                {
                    try
                    {
                        var result = await processor.SendMessage(model);
                        if (result != null)
                        {
                            notification.Status = result.Status;
                            notification.StatusCode = result.StatusCode;
                            notification.StatusMessage = result.StatusMessage;
                            notifications.Add(notification);
                        }
                        else
                        {
                            notification.Status = NotificationStatus.Failed;
                            notification.StatusCode = "500";
                            notification.StatusMessage = "Processor returned null result";
                            notifications.Add(notification);
                            overallSuccess = false;
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError($"Error processing notification type {type}: {ex.Message}", ex);
                        notification.Status = NotificationStatus.Failed;
                        notification.StatusCode = "500";
                        notification.StatusMessage = ex.Message;
                        notifications.Add(notification);
                        overallSuccess = false;

                        throw;
                    }
                }
                else
                {
                    _logger.LogWarning($"No processor found for notification type {type}");
                    overallSuccess = false;
                }
            }

            // Save all notifications in a single transaction
            if (notifications.Count != 0)
            {
                try
                {
                    await _unitOfWork.GetRepository<Notification>().AddRangeAsync(notifications);
                    await _unitOfWork.SaveAsync();
                }
                catch (Exception ex)
                {
                    _logger.LogError($"Error saving notifications: {ex.Message}", ex);
                    return false;
                }
            }

            return overallSuccess;
        }
    }
}
