{"format": 1, "restore": {"C:\\Users\\<USER>\\source\\repos\\Notification-Service\\src\\NotificationService.Infrastructure\\NotificationService.Infrastructure.csproj": {}}, "projects": {"C:\\Users\\<USER>\\source\\repos\\Notification-Service\\src\\NotificationService.Core\\NotificationService.Core.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\source\\repos\\Notification-Service\\src\\NotificationService.Core\\NotificationService.Core.csproj", "projectName": "NotificationService.Core", "projectPath": "C:\\Users\\<USER>\\source\\repos\\Notification-Service\\src\\NotificationService.Core\\NotificationService.Core.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\source\\repos\\Notification-Service\\src\\NotificationService.Core\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}, "https://nuget.pkg.github.com/dozzman-z/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[9.0.2, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.304/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\source\\repos\\Notification-Service\\src\\NotificationService.Infrastructure\\NotificationService.Infrastructure.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\source\\repos\\Notification-Service\\src\\NotificationService.Infrastructure\\NotificationService.Infrastructure.csproj", "projectName": "NotificationService.Infrastructure", "projectPath": "C:\\Users\\<USER>\\source\\repos\\Notification-Service\\src\\NotificationService.Infrastructure\\NotificationService.Infrastructure.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\source\\repos\\Notification-Service\\src\\NotificationService.Infrastructure\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}, "https://nuget.pkg.github.com/dozzman-z/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"C:\\Users\\<USER>\\source\\repos\\Notification-Service\\src\\NotificationService.Core\\NotificationService.Core.csproj": {"projectPath": "C:\\Users\\<USER>\\source\\repos\\Notification-Service\\src\\NotificationService.Core\\NotificationService.Core.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"AWSSDK.Core": {"target": "Package", "version": "[3.7.400.70, )"}, "AWSSDK.Extensions.NETCore.Setup": {"target": "Package", "version": "[3.7.301, )"}, "AWSSDK.S3": {"target": "Package", "version": "[3.7.410.11, )"}, "AfricasTalking.NET": {"target": "Package", "version": "[1.2.4, )"}, "AutoMapper": {"target": "Package", "version": "[13.0.1, )"}, "FirebaseAdmin": {"target": "Package", "version": "[3.3.0, )"}, "Google.Apis.Auth": {"target": "Package", "version": "[1.68.0, )"}, "Google.Cloud.Storage.V1": {"target": "Package", "version": "[4.7.0, )"}, "JobPro.Common.Services": {"target": "Package", "version": "[1.0.0, )"}, "Kralizek.Extensions.Configuration.AWSSecretsManager": {"target": "Package", "version": "[1.7.0, )"}, "MailKit": {"target": "Package", "version": "[4.3.0, )"}, "MassTransit": {"target": "Package", "version": "[8.3.4, )"}, "MassTransit.AmazonSQS": {"target": "Package", "version": "[8.3.4, )"}, "MassTransit.Kafka": {"target": "Package", "version": "[8.2.5, )"}, "Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[9.0.2, )"}, "Microsoft.EntityFrameworkCore.Design": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[9.0.2, )"}, "Microsoft.EntityFrameworkCore.Tools": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[9.0.2, )"}, "Microsoft.Extensions.Configuration.Abstractions": {"target": "Package", "version": "[9.0.2, )"}, "Microsoft.Extensions.DependencyInjection.Abstractions": {"target": "Package", "version": "[9.0.2, )"}, "Microsoft.Extensions.Hosting.Abstractions": {"target": "Package", "version": "[9.0.2, )"}, "MimeKit": {"target": "Package", "version": "[4.3.0, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}, "Npgsql.EntityFrameworkCore.PostgreSQL": {"target": "Package", "version": "[9.0.4, )"}, "RabbitMQ.Client": {"target": "Package", "version": "[6.8.1, )"}, "RazorLight": {"target": "Package", "version": "[2.3.1, )"}, "RestSharp": {"target": "Package", "version": "[110.2.0, )"}, "Serilog.AspNetCore": {"target": "Package", "version": "[7.0.0, )"}, "Serilog.Sinks.File": {"target": "Package", "version": "[5.0.0, )"}, "Twilio": {"target": "Package", "version": "[6.16.1, )"}, "WatchDog.NET": {"target": "Package", "version": "[1.4.10, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.304/PortableRuntimeIdentifierGraph.json"}}}}}