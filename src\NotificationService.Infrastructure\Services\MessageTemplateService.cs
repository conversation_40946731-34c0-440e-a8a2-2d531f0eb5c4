﻿using Google.Apis.Auth.OAuth2;
using Google.Cloud.Storage.V1;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using NotificationService.Core.Services;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace NotificationService.Infrastructure.Services
{
    public class MessageTemplateService : IMessageTemplateService
    {
        private  GoogleCredential _googleCredential;
        private readonly string _gcpAuthFile;
        private readonly string _gcpBucketName;
        protected readonly ILogger<MessageTemplateService> _logger;


        public MessageTemplateService(IConfiguration configuration, ILogger<MessageTemplateService> logger) 
        {
            _gcpAuthFile = configuration["GCP:AuthFile"] ??  "";
            _gcpBucketName = configuration["GCP:BucketName"] ?? "";
            _logger = logger;
        }

        public async Task<string> GetMessageTemplate(string templateName, string bucketName)
        {
            if(string.IsNullOrWhiteSpace(bucketName)) bucketName = _gcpBucketName?? string.Empty;   
            var templateByte = await DownloadFile(templateName, bucketName);
            return Encoding.Default.GetString(templateByte);
        }

        public Task<string> GetMessageTemplate(string templateName)
        {
            throw new NotImplementedException();
        }

        private void Authenticate()
        {

            try
            {
                if(_googleCredential== null)
                {
                    var environment = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT");
                    if (environment == Environments.Production)
                    {
                        // Store the json file in Secrets.
                        _googleCredential = GoogleCredential.FromJson(_gcpAuthFile);
                    }
                    else
                    {
                        _googleCredential = GoogleCredential.FromFile(_gcpAuthFile);
                    }
                }
 
            }
            catch (Exception ex)
            {
                _logger.LogError($"{ex.Message}");
                throw;
            }
        }

        public async Task<byte[]> DownloadFile(string fileNameToDownload, string bucketName)
        {
            try
            {
                Authenticate();
                //_googleCredential = GoogleCredential.FromFile(_gcpAuthFile);
                using (var storageClient = StorageClient.Create(_googleCredential))
                {
                    using (var memoryStream = new MemoryStream())
                    {
                        await storageClient.DownloadObjectAsync(bucketName, fileNameToDownload, memoryStream);
                        var fileBytes = memoryStream.ToArray();
                        //var fileBase64 = Convert.ToBase64String(fileBytes);
                        return fileBytes;
                    }
                }
            }
            catch(Exception ex) 
            {
                _logger.LogError(ex,ex.Message);
            }
            return null;
        }
    }
}
