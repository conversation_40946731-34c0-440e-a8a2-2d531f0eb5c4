name: Deploy to GKE

on:
  push:
    branches:
      - aws-postgres-branch
jobs:
  deploy:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v2

      - name: Authenticate with Docker Hub
        run: docker login -u ${{ secrets.DOCKER_USERNAME }} -p ${{ secrets.DOCKER_PASSWORD }}

      - name: Build and Push Docker Image
        run: |
         
          IMAGE_TAG="notsv${{ github.run_id }}"
          docker build -t zarttechjobpro/jobpro:$IMAGE_TAG .
          docker tag zarttechjobpro/jobpro:$IMAGE_TAG zarttechjobpro/jobpro:notsv${{ github.run_id }}
          docker push zarttechjobpro/jobpro:notsv${{ github.run_id }}

      - name: Remove Unused Docker Images
        run: docker system prune -f

      
      - name: Set Git Config
        run: |
          git config --global user.email "<EMAIL>"
          git config --global user.name "dozzman-z"
    

      - name: <PERSON>lone Repository
        run: |
          git clone https://${{ secrets.USERNAME }}:${{ secrets.TOKEN }}@github.com/Zarttech-main/provisioning.git

      - name: Update Kubernetes YAML
        run: |
          cd provisioning
          sed -i 's/notsv.*/'notsv${{ github.run_id }}'/g' projects/AWS/notification-svc/deployment.yml
          cat projects/AWS/notification-svc/deployment.yml
          git add projects/AWS/notification-svc/deployment.yml
          git commit -m "updated deployment"
          git push
