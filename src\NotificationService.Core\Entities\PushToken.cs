﻿using Microsoft.EntityFrameworkCore;
using NotificationService.Core.Constants;

namespace NotificationService.Core.Entities
{
    [Index(nameof(UserId), IsUnique = false)]
    public class PushToken : EntityBase
    {
        public string AppName { get; set; } = default!;
        public string UserId { get; set; } = default!;
        public string Token { get; set; } = default!;
        public Platform Platform { get; set; }
        public IOSPushType? IOSPushType { get; set; }
    }
}
