﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using NotificationService.Core.Repositories;
using NotificationService.Core.Services;
using NotificationService.Infrastructure.Repositories;
using NotificationService.Infrastructure.Services;
using System.Reflection;

namespace NotificationService.Infrastructure
{
    public static class ServiceConnector
    {
        public static void AddCoreServices(this IServiceCollection services, ConfigurationManager config)
        {
            services.AddSingleton<IAMQProducer, BaseProducer>();

            // - DO NOT DELETE
            services.AddScoped<RabbitMQBrokerService, RabbitMQBrokerService>();
            var asm = Assembly.GetExecutingAssembly();
            var types = asm.GetTypes();
            var consumerType = typeof(IAMQConsumer);

            var consumers = types.Where(tp => consumerType.IsAssignableFrom(tp) && !tp.IsAbstract).ToList();
            consumers.ForEach((type) =>
            {
                services.AddScoped(consumerType, type);
            });

            //var asm = Assembly.GetExecutingAssembly();
            //var types = asm.GetTypes();
            var processorType = typeof(IMessageProcessorService);
            var processors = types.Where(tp => processorType.IsAssignableFrom(tp) && !tp.IsAbstract).ToList();
            processors.ForEach((type) =>
            {
                services.AddScoped(processorType, type);
            });

            services.AddScoped<IUnitOfWork, UnitOfWork>();
            services.AddScoped(typeof(IRepository<>), typeof(BaseRepository<>));
            services.AddScoped<IUserService, UserService>();
            services.AddScoped<INotificationService, Services.NotificationService>();
            services.AddScoped<IMessageTemplateService, MessageTemplateService>();
            //services.AddScoped<IEventProcessingSetUp, EventProcessingSetUp>();

            // Add hosted services
            //services.AddHostedService<NotificationEventConsumer>();

            // Add AWS Secret Manager configuration - DO NOT DELETE --- Comment out for AWS
            //try
            //{
            //    var serviceName = config.GetValue<string>("ServiceSettings:Name");
            //    services.AddSingleton<IAmazonSQS>(_ => new AmazonSQSClient(RegionEndpoint.EUNorth1));
            //    services.AddSingleton<IAmazonSimpleNotificationService>(_ => new AmazonSimpleNotificationServiceClient(RegionEndpoint.EUNorth1));
            //    config.AddSecretsManager(region: RegionEndpoint.EUNorth1,
            //        configurator: options =>
            //        {
            //            options.SecretFilter = secretName => secretName.Name.StartsWith($"{serviceName}_");
            //            options.KeyGenerator = (_, name) => name.Replace($"{serviceName}_", "")
            //            .Replace("__", ":");
            //        });
            //}
            //catch (System.Exception ex)
            //{
            //    Console.WriteLine($"Error getting AWS Secrets Manager {ex.Message}");
            //}
        }
    }
}
