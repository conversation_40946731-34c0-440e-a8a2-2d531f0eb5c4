
11:56:31 [Error] :: Failed executing DbCommand ("90"ms) [Parameters=[""], CommandType='Text', CommandTimeout='30']"
""SELECT \"MigrationId\", \"ProductVersion\"
FROM \"__EFMigrationsHistory\"
ORDER BY \"MigrationId\";"

11:56:31 [Error] :: Failed executing DbCommand ("8"ms) [Parameters=[""], CommandType='Text', CommandTimeout='30']"
""SELECT \"MigrationId\", \"ProductVersion\"
FROM \"__EFMigrationsHistory\"
ORDER BY \"MigrationId\";"

11:56:32 [Information] :: Executed DbCommand ("39"ms) [Parameters=[""], CommandType='Text', CommandTimeout='30']"
""CREATE TABLE IF NOT EXISTS \"__EFMigrationsHistory\" (
    \"MigrationId\" character varying(150) NOT NULL,
    \"ProductVersion\" character varying(32) NOT NULL,
    CONSTRAINT \"PK___EFMigrationsHistory\" PRIMARY KEY (\"MigrationId\")
);"

11:56:32 [Information] :: Executed DbCommand ("2"ms) [Parameters=[""], CommandType='Text', CommandTimeout='30']"
""LOCK TABLE \"__EFMigrationsHistory\" IN ACCESS EXCLUSIVE MODE"

11:56:32 [Information] :: Executed DbCommand ("4"ms) [Parameters=[""], CommandType='Text', CommandTimeout='30']"
""SELECT \"MigrationId\", \"ProductVersion\"
FROM \"__EFMigrationsHistory\"
ORDER BY \"MigrationId\";"

11:56:32 [Information] :: Applying migration '"20250302120031_init"'.

11:56:32 [Information] :: Executed DbCommand ("12"ms) [Parameters=[""], CommandType='Text', CommandTimeout='30']"
""CREATE TABLE \"Notifications\" (
    \"Id\" bigint GENERATED BY DEFAULT AS IDENTITY,
    \"MessageId\" text,
    \"Body\" text,
    \"UserId\" text,
    \"Subject\" text,
    \"Email\" text,
    \"PhoneNumber\" text,
    \"PushToken\" text,
    \"NotificationType\" text NOT NULL,
    \"Priority\" text NOT NULL,
    \"Template\" text,
    \"MessageSid\" text,
    \"Status\" text NOT NULL,
    \"ResponseId\" text,
    \"StatusCode\" text,
    \"StatusMessage\" text,
    \"CreatedBy\" text,
    \"CreatedOn\" timestamp with time zone NOT NULL,
    \"UpdatedBy\" text,
    \"UpdatedOn\" timestamp with time zone NOT NULL,
    CONSTRAINT \"PK_Notifications\" PRIMARY KEY (\"Id\")
);"

11:56:32 [Information] :: Executed DbCommand ("10"ms) [Parameters=[""], CommandType='Text', CommandTimeout='30']"
""CREATE TABLE \"PushTokens\" (
    \"Id\" bigint GENERATED BY DEFAULT AS IDENTITY,
    \"AppName\" text NOT NULL,
    \"UserId\" text NOT NULL,
    \"Token\" text NOT NULL,
    \"CreatedBy\" text,
    \"CreatedOn\" timestamp with time zone NOT NULL,
    \"UpdatedBy\" text,
    \"UpdatedOn\" timestamp with time zone NOT NULL,
    CONSTRAINT \"PK_PushTokens\" PRIMARY KEY (\"Id\")
);"

11:56:32 [Information] :: Executed DbCommand ("9"ms) [Parameters=[""], CommandType='Text', CommandTimeout='30']"
""CREATE TABLE \"Users\" (
    \"Id\" bigint GENERATED BY DEFAULT AS IDENTITY,
    \"UserId\" text NOT NULL,
    \"Username\" text NOT NULL,
    \"PhoneNumber\" text NOT NULL,
    \"Email\" text NOT NULL,
    \"FirstName\" text,
    \"LastName\" text,
    \"CompanyEmail\" text NOT NULL,
    \"ZipCode\" text NOT NULL,
    \"Country\" text NOT NULL,
    \"TimeZone\" text NOT NULL,
    \"CreatedBy\" text,
    \"CreatedOn\" timestamp with time zone NOT NULL,
    \"UpdatedBy\" text,
    \"UpdatedOn\" timestamp with time zone NOT NULL,
    CONSTRAINT \"PK_Users\" PRIMARY KEY (\"Id\")
);"

11:56:32 [Information] :: Executed DbCommand ("5"ms) [Parameters=[""], CommandType='Text', CommandTimeout='30']"
""CREATE INDEX \"IX_Notifications_CreatedBy\" ON \"Notifications\" (\"CreatedBy\");"

11:56:32 [Information] :: Executed DbCommand ("3"ms) [Parameters=[""], CommandType='Text', CommandTimeout='30']"
""CREATE INDEX \"IX_Notifications_CreatedOn\" ON \"Notifications\" (\"CreatedOn\");"

11:56:32 [Information] :: Executed DbCommand ("2"ms) [Parameters=[""], CommandType='Text', CommandTimeout='30']"
""CREATE INDEX \"IX_Notifications_MessageId\" ON \"Notifications\" (\"MessageId\");"

11:56:32 [Information] :: Executed DbCommand ("2"ms) [Parameters=[""], CommandType='Text', CommandTimeout='30']"
""CREATE INDEX \"IX_Notifications_UserId\" ON \"Notifications\" (\"UserId\");"

11:56:32 [Information] :: Executed DbCommand ("2"ms) [Parameters=[""], CommandType='Text', CommandTimeout='30']"
""CREATE INDEX \"IX_PushTokens_CreatedBy\" ON \"PushTokens\" (\"CreatedBy\");"

11:56:32 [Information] :: Executed DbCommand ("3"ms) [Parameters=[""], CommandType='Text', CommandTimeout='30']"
""CREATE INDEX \"IX_PushTokens_CreatedOn\" ON \"PushTokens\" (\"CreatedOn\");"

11:56:32 [Information] :: Executed DbCommand ("3"ms) [Parameters=[""], CommandType='Text', CommandTimeout='30']"
""CREATE UNIQUE INDEX \"IX_PushTokens_UserId\" ON \"PushTokens\" (\"UserId\");"

11:56:32 [Information] :: Executed DbCommand ("3"ms) [Parameters=[""], CommandType='Text', CommandTimeout='30']"
""CREATE INDEX \"IX_Users_CreatedBy\" ON \"Users\" (\"CreatedBy\");"

11:56:32 [Information] :: Executed DbCommand ("3"ms) [Parameters=[""], CommandType='Text', CommandTimeout='30']"
""CREATE INDEX \"IX_Users_CreatedOn\" ON \"Users\" (\"CreatedOn\");"

11:56:32 [Information] :: Executed DbCommand ("5"ms) [Parameters=[""], CommandType='Text', CommandTimeout='30']"
""CREATE UNIQUE INDEX \"IX_Users_UserId\" ON \"Users\" (\"UserId\");"

11:56:32 [Information] :: Executed DbCommand ("1"ms) [Parameters=[""], CommandType='Text', CommandTimeout='30']"
""INSERT INTO \"__EFMigrationsHistory\" (\"MigrationId\", \"ProductVersion\")
VALUES ('20250302120031_init', '9.0.2');"

11:56:33 [Information] :: User profile is available. Using '"C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys"' as key repository and Windows DPAPI to encrypt keys at rest.

11:56:34 [Information] :: Now listening on: "https://localhost:7044"

11:56:34 [Information] :: Now listening on: "http://localhost:5179"

11:56:34 [Information] :: Application started. Press Ctrl+C to shut down.

11:56:34 [Information] :: Hosting environment: "Development"

11:56:34 [Information] :: Content root path: "C:\Users\<USER>\source\repos\Notification-Service\src\NotificationService.API"

11:56:35 [Information] :: Request starting "HTTP/2" "GET" "https"://"localhost:7044""""/swagger""" - null null

11:56:35 [Information] :: Request finished "HTTP/2" "GET" "https"://"localhost:7044""""/swagger""" - 301 0 null 221.2827ms

11:56:35 [Information] :: Request starting "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/index.html""" - null null

11:56:36 [Information] :: Request finished "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/index.html""" - 200 null "text/html;charset=utf-8" 204.4395ms

11:56:36 [Information] :: Request starting "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/swagger-ui-standalone-preset.js""" - null null

11:56:36 [Information] :: Request starting "HTTP/2" "GET" "https"://"localhost:7044""""/_framework/aspnetcore-browser-refresh.js""" - null null

11:56:36 [Information] :: Request starting "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/swagger-ui-bundle.js""" - null null

11:56:36 [Information] :: Request starting "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/swagger-ui.css""" - null null

11:56:36 [Information] :: Request starting "HTTP/2" "GET" "https"://"localhost:7044""""/_vs/browserLink""" - null null

11:56:36 [Information] :: Request finished "HTTP/2" "GET" "https"://"localhost:7044""""/_framework/aspnetcore-browser-refresh.js""" - 200 13782 "application/javascript; charset=utf-8" 76.7624ms

11:56:36 [Information] :: Sending file. Request path: '"/swagger-ui.css"'. Physical path: '"N/A"'

11:56:36 [Information] :: Sending file. Request path: '"/swagger-ui-standalone-preset.js"'. Physical path: '"N/A"'

11:56:36 [Information] :: Request finished "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/swagger-ui.css""" - 200 143943 "text/css" 137.7023ms

11:56:36 [Information] :: Request finished "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/swagger-ui-standalone-preset.js""" - 200 339486 "text/javascript" 147.4709ms

11:56:36 [Information] :: Sending file. Request path: '"/swagger-ui-bundle.js"'. Physical path: '"N/A"'

11:56:36 [Information] :: Request finished "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/swagger-ui-bundle.js""" - 200 1096145 "text/javascript" 185.629ms

11:56:36 [Information] :: Request finished "HTTP/2" "GET" "https"://"localhost:7044""""/_vs/browserLink""" - 200 null "text/javascript; charset=UTF-8" 203.0683ms

11:56:36 [Information] :: Request starting "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/v1/swagger.json""" - null null

11:56:36 [Information] :: Request starting "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/favicon-32x32.png""" - null null

11:56:36 [Information] :: Sending file. Request path: '"/favicon-32x32.png"'. Physical path: '"N/A"'

11:56:36 [Information] :: Request finished "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/favicon-32x32.png""" - 200 628 "image/png" 41.1473ms

11:56:36 [Information] :: Request finished "HTTP/2" "GET" "https"://"localhost:7044""""/swagger/v1/swagger.json""" - 200 null "application/json;charset=utf-8" 299.5426ms
