﻿using Microsoft.EntityFrameworkCore;

namespace NotificationService.Core.Entities
{
    [Index(nameof(UserId), IsUnique = true)]
    public class User : EntityBase
    {
        public string UserId { get; set; } = default!;
        public string Username { get; set; } = default!;
        public string PhoneNumber { get; set; } = default!;
        public string Email { get; set; } = default!;
        public string? FirstName { get; set; }
        public string? LastName { get; set; }
        public string CompanyEmail { get; set; } = default!;
        public string? ZipCode { get; set; }
        public string? Country { get; set; }
        public string? TimeZone { get; set; }
    }
}

