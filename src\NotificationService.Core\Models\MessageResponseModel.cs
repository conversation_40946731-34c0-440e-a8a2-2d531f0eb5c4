﻿using NotificationService.Core.Constants;

namespace NotificationService.Core.Models
{
    /// <summary>
    /// 
    /// </summary>
    public class MessageResponseModel
    {
        public string ResponseId { get; set; }  
        public NotificationStatus Status { get; set; }
        public string StatusCode { get; set; }
        public string StatusMessage { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public MessageResponseModel()
        {
            Status = NotificationStatus.Pending;
        }
    }
}
